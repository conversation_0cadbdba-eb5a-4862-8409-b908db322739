import React from 'react';
import VoiceDialog from '@/features/voiceai/ui/VoiceDialog';
import { useVoiceDialog } from '@/contexts/VoiceDialogContext';

const GlobalVoiceDialog: React.FC = () => {
  const { isVoiceDialogOpen, closeVoiceDialog, businessId } = useVoiceDialog();

  return (
    <VoiceDialog
      isOpen={isVoiceDialogOpen}
      onClose={closeVoiceDialog}
      isFullScreen={true}
      businessId={businessId}
    />
  );
};

export default GlobalVoiceDialog;

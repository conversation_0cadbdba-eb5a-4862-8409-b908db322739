-- Add foreign key constraints with cascade deletion for shared items
-- This ensures that when a user leaves a group, their shared deals and bookings are automatically deleted

-- First, let's add a composite foreign key from group_deal_shares to group_members
-- This will cascade delete all shared deals when a user leaves the group
ALTER TABLE group_deal_shares 
ADD CONSTRAINT fk_group_deal_shares_member 
<PERSON><PERSON><PERSON><PERSON><PERSON>EY (group_id, shared_by) 
REFERENCES group_members (group_id, user_id) 
ON DELETE CASCADE;

-- Add a composite foreign key from group_booking_shares to group_members
-- This will cascade delete all shared bookings when a user leaves the group
ALTER TABLE group_booking_shares 
ADD CONSTRAINT fk_group_booking_shares_member 
<PERSON><PERSON><PERSON><PERSON><PERSON> KEY (group_id, shared_by) 
REFERENCES group_members (group_id, user_id) 
ON DELETE CASCADE;

-- Add a unique constraint on group_members if it doesn't exist already
-- This is required for the foreign key references above
ALTER TABLE group_members 
ADD CONSTRAINT unique_group_member 
UNIQUE (group_id, user_id);

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Users, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useGroups } from '@/hooks/group/useGroups';
import CreateGroupDialog from '@/components/groups/CreateGroupDialog';
import GroupCard from '@/components/groups/GroupCard';
import BottomNavigationBar from '@/components/toolbars/BottomNavigationBar';
import { useBusinessMode } from '@/hooks/useBusinessMode';
import type { GroupWithMemberCount } from '@/types/groups';
import UnifiedHeader from '@/components/toolbars/UnifiedHeader';

const Groups = () => {
  const navigate = useNavigate();
  const { isBusinessMode } = useBusinessMode();
  const [searchQuery, setSearchQuery] = useState('');
  
  const { data: groups = [], isLoading, error } = useGroups();

  const filteredGroups = groups.filter(group =>
    group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    group.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleGroupClick = (group: GroupWithMemberCount) => {
    navigate(`/groups/${group.id}`);
  };

  const handleEditGroup = (group: GroupWithMemberCount) => {
    // TODO: Implementare modifica gruppo
    console.log('Edit group:', group.id);
  };

  const handleShareGroup = (group: GroupWithMemberCount) => {
    // TODO: Implementare condivisione gruppo
    console.log('Share group:', group.id);
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <UnifiedHeader title="I Miei Gruppi" isBusiness={isBusinessMode} />
        <main className="pt-16 pb-20 px-4">
          <div className="text-center py-12">
            <p className="text-red-600">Errore nel caricamento dei gruppi</p>
          </div>
        </main>
        <BottomNavigationBar isBusiness={isBusinessMode} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <UnifiedHeader title="I Miei Gruppi" isBusiness={isBusinessMode} />
      
      <main className="pt-16 pb-20 px-4">
        {/* Header con ricerca e pulsante crea */}
        <div className="flex flex-col space-y-4 mb-6">
          <div className="flex items-center justify-end">
          {!isLoading && filteredGroups.length > 0 && ( <CreateGroupDialog />)}
          </div>
          
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Cerca gruppi..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Loading state */}
        {isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3].map((item) => (
              <div key={item} className="h-32 bg-gray-200 rounded-xl animate-pulse" />
            ))}
          </div>
        )}

        {/* Lista gruppi */}
        {!isLoading && filteredGroups.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredGroups.map((group) => (
              <GroupCard
                key={group.id}
                group={group}
                onClick={handleGroupClick}
                onEdit={handleEditGroup}
                onShare={handleShareGroup}
              />
            ))}
          </div>
        )}

        {/* Empty state */}
        {!isLoading && filteredGroups.length === 0 && !searchQuery && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="h-12 w-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Nessun gruppo ancora
            </h3>
            <p className="text-gray-600 mb-6 max-w-sm mx-auto">
              Crea il tuo primo gruppo per iniziare a condividere offerte e prenotazioni 
              con amici e familiari.
            </p>
            <CreateGroupDialog />
          </div>
        )}

        {/* No search results */}
        {!isLoading && filteredGroups.length === 0 && searchQuery && (
          <div className="text-center py-12">
            <p className="text-gray-600">
              Nessun gruppo trovato per "{searchQuery}"
            </p>
          </div>
        )}
      </main>

      <BottomNavigationBar isBusiness={isBusinessMode} />
    </div>
  );
};

export default Groups;

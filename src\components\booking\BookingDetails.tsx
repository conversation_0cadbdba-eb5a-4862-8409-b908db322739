import { Calendar, Clock, MapPin } from "lucide-react";
import { format, parseISO } from "date-fns";
import { it } from "date-fns/locale";
import { motion } from "framer-motion";
import type { Database } from "@/integrations/supabase/types";

type Deal = Database["public"]["Tables"]["deals"]["Row"];
type Business = Database["public"]["Tables"]["businesses"]["Row"];

interface DealWithBusiness extends Deal {
  businesses: Business;
}

interface BookingDetailsProps {
  deal: DealWithBusiness;
  bookingDate: string;
  bookingTime: string;
}

const BookingDetails = ({
  deal,
  bookingDate,
  bookingTime,
}: BookingDetailsProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-sm overflow-hidden"
    >
      <img
        src={
          deal.images?.[0] || `https://picsum.photos/800/600?random=${deal.id}`
        }
        alt={deal.title}
        className="w-full h-48 object-cover"
      />

      <div className="p-4 space-y-4">
        <h2 className="text-xl font-semibold">{deal.title}</h2>

        <div className="flex items-start gap-3 text-gray-600">
          <MapPin className="h-5 w-5 text-gray-400 mt-1" />
          <div>
            <p className="font-medium">{deal.businesses.name}</p>
            <p className="text-sm">{deal.businesses.address}</p>
          </div>
        </div>

        <div className="flex items-center gap-3 text-gray-600">
          <Calendar className="h-5 w-5 text-gray-400" />
          <p>
            {format(parseISO(bookingDate), "EEEE d MMMM yyyy", { locale: it })}
          </p>
        </div>

        <div className="flex items-center gap-3 text-gray-600">
          <Clock className="h-5 w-5 text-gray-400" />
          <p>{bookingTime}</p>
        </div>

        {deal.original_price !== 0 ? (
          <div className="border-t pt-4 mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Prezzo originale</span>
              <span className="line-through">€{deal.original_price}</span>
            </div>
            <div className="flex justify-between font-semibold">
              <span>Totale da pagare</span>
              <span className="text-brand-primary">
                €{deal.discounted_price}
              </span>
            </div>
          </div>
        ) : (
          <div className="border-t pt-4 mt-4">
            <div className="flex justify-between font-semibold">
              <span>Sconto</span>
              <span className="text-brand-primary">
                {deal.discount_percentage}%
              </span>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default BookingDetails;

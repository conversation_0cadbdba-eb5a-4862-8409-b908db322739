
import { Database } from "@/integrations/supabase/types";

export type TimeSlot = {
  start_time: string;
  end_time: string;
  available_seats: number;
  booked_seats?: number;
};

export type DaySchedule = {
  day: number;
  day_name: string;
  time_slots: TimeSlot[];
};

export type WeeklySchedule = {
  schedule: DaySchedule[];
  exceptions: string[];
};

export type DealStatus = 'draft' | 'published' | 'expired';

// Alias for backward compatibility
export type Deal = DealFromDB;

// Tipo per i dati provenienti dal database
export type DealFromDB = {
  id: string;
  title: string;
  description: string;
  original_price: number;
  discounted_price: number;
  discount_percentage: number;
  images: string[];
  business_id: string;
  start_date: string;
  end_date: string;
  time_slots: any;
  status: DealStatus;
  created_at: string;
  updated_at: string;
  auto_confirm: boolean;
  category_id: string;
  fake: boolean;
  terms_conditions: string;
  // Optional properties for different use cases
  image_url?: string;
  business_name?: string;
  price?: number;
  capacity?: number;
  businesses?: {
    name: string;
    address: string;
    city?: string;
    zip_code?: string;
    state?: string;
    country?: string;
    score?: number;
    review_count?: number;
  };
};

export type FormData = {
  title: string;
  description: string;
  original_price: string;
  discount_percentage: string;
  discounted_price: string;
  start_date: string;
  end_date: string;
  time_slots: WeeklySchedule;
  images: File[];
  status: DealStatus;
  deal_categories: string[]; // Array of deal category IDs
};

// Tipo per gli slot disponibili utilizzato in BusinessCarousel e altri componenti
export interface AvailableSlot {
  day: number;
  start_time: string;
  end_time: string;
  available_seats: number;
}

export type JsonCompatible = string | number | boolean | null | { [key: string]: JsonCompatible } | JsonCompatible[];

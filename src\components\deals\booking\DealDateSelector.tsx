
import ButtonDate from "@/components/ButtonDate";
import { format } from "date-fns";
import { it } from "date-fns/locale";

interface DealDateSelectorProps {
  availableDates: Date[];
  selectedDate: string | null;
  onDateSelect: (date: string) => void;
}

const DealDateSelector = ({ availableDates, selectedDate, onDateSelect }: DealDateSelectorProps) => {
  return (
    <div>
      <h3 className="text-sm font-semibold mb-2">Seleziona una Data</h3>
      <div className="flex gap-2 overflow-x-auto pb-2">
        {availableDates.slice(0, 7).map((date,idx) => (

          <ButtonDate
            key={`date-${idx}`}
            date={date}
            isSelected={selectedDate === format(date, 'yyyy-MM-dd')}
            today={new Date()}
            onSelect={(date) => onDateSelect(format(date, 'yyyy-MM-dd'))}/>
          
          // <button
          //   key={date.toISOString()}
          //   onClick={() => onDateSelect(format(date, 'yyyy-MM-dd'))}
          //   className={`px-4 py-2 border rounded-full text-sm whitespace-nowrap min-w-[120px] ${
          //     selectedDate === format(date, 'yyyy-MM-dd')
          //       ? 'bg-brand-primary text-white border-brand-primary'
          //       : 'bg-white text-gray-700 border-gray-200'
          //   }`}
          // >
          //   {format(date, 'EEEE d MMM', { locale: it })}
          // </button>
        ))}
      </div>
    </div>
  );
};

export default DealDateSelector;


export const getFormattedCurrency = priceString => {
  const price = parseFloat(priceString.replace('$', ''));
  return formatCurrency(price);
};

export const formatCurrency = (price: number) => {
  const formatter = new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    currencyDisplay: 'symbol',
    
  });

  return formatter.format(price);
};

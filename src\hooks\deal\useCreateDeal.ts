
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { FormData, WeeklySchedule } from "@/types/deals";
import { useNavigate } from "react-router-dom";
import { INITIAL_SCHEDULE } from "@/data/daysNames";



export const useCreateDeal = (businessId: string | undefined) => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const today = new Date();
  const formattedToday = today.toISOString().slice(0, 16);

  const [formData, setFormData] = useState<FormData>({
    title: "",
    description: "",
    original_price: "",
    discount_percentage: "",
    discounted_price: "",
    start_date: formattedToday,
    end_date: "",
    time_slots: INITIAL_SCHEDULE,
    images: [],
    status: "draft",
    deal_categories: []
  });

  useEffect(() => {
    if (formData.original_price && formData.discount_percentage) {
      const original = parseFloat(formData.original_price);
      const discount = parseFloat(formData.discount_percentage);
      if (!isNaN(original) && !isNaN(discount)) {
        const discounted = original * (1 - discount / 100);
        setFormData(prev => ({
          ...prev,
          discounted_price: discounted.toFixed(2)
        }));
      }
    }
  }, [formData.original_price, formData.discount_percentage]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("formData", formData);
    try {
      setIsSubmitting(true);

      if (!formData.title || formData.original_price === "" || !formData.start_date || !formData.end_date) {
        toast.error("Compila tutti i campi obbligatori");
        return;
      }

      const originalPrice = parseFloat(formData.original_price);
      const discountPercentage = parseFloat(formData.discount_percentage || "0");
      const discountedPrice = parseFloat(formData.discounted_price || "0");

      if (isNaN(originalPrice)) {
        toast.error("Inserisci un prezzo originale valido");
        return;
      }

      const startDate = new Date(formData.start_date);
      const endDate = new Date(formData.end_date);
      
      if (endDate <= startDate) {
        toast.error("La data di fine deve essere successiva alla data di inizio");
        return;
      }

      const imageUrls = [];
      for (const image of formData.images) {
        const fileExt = image.name.split('.').pop();
        const filePath = `${Date.now()}.${fileExt}`;
        
        const { error: uploadError } = await supabase.storage
          .from('deals')
          .upload(filePath, image);

        if (uploadError) {
          throw uploadError;
        }

        const { data: { publicUrl } } = supabase.storage
          .from('deals')
          .getPublicUrl(filePath);

        imageUrls.push(publicUrl);
      }

      const timeSlots = JSON.parse(JSON.stringify(formData.time_slots));

      // TODO Check if we need to check.
      // Verifica che ogni time_slot abbia i campi obbligatori
      let availableSeats = 1;
      timeSlots.schedule.forEach((day: any) => {
        day.time_slots.forEach((slot: any) => {
          // Se non c'è il campo available_seats, lo impostiamo a 1
          if (!slot.available_seats) {
            slot.available_seats = 1;
          }
          // Aggiorniamo il numero massimo di posti disponibili
          if (slot.available_seats > availableSeats) {
            availableSeats = slot.available_seats;
          }
        });
      });


      console.log("timeSlots", timeSlots);

      const { error } = await supabase
        .from('deals')
        .insert({
          business_id: businessId,
          title: formData.title,
          description: formData.description,
          original_price: originalPrice,
          discount_percentage: discountPercentage || null,
          discounted_price: discountedPrice || originalPrice,
          start_date: new Date(formData.start_date).toISOString(),
          end_date: new Date(formData.end_date).toISOString(),
          time_slots: timeSlots,
          images: imageUrls,
          status: formData.status
        });

      if (error) throw error;

      // TODO: Save deal categories when the backend tables are properly configured
      // For now, just log the selected categories
      if (formData.deal_categories.length > 0) {
        console.log('Selected deal categories:', formData.deal_categories);
      }

     // toast.success("Offerta creata con successo!");
      navigate(`/business/${businessId}`);
    } catch (error) {
      toast.error("Errore durante la creazione dell'offerta");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    formData,
    setFormData,
    isSubmitting,
    handleSubmit,
  };
};

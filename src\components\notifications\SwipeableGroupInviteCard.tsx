
import React, { useState, useRef } from 'react';
import { useSpring, animated } from '@react-spring/web';
import { useGesture } from '@use-gesture/react';
import { Users, Check, X } from 'lucide-react';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAcceptInvite, useDeclineInvite } from '@/hooks/group/useReceivedInvites';
import { useDeleteNotification } from '@/hooks/notifications/useNotifications';
import { toast } from 'sonner';

interface SwipeableGroupInviteCardProps {
  notification: {
    id: string;
    entity: string;
    entity_id: string;
    created_at: string;
  };
  onProcessed?: () => void;
}

const SwipeableGroupInviteCard = ({ notification, onProcessed }: SwipeableGroupInviteCardProps) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isProcessed, setIsProcessed] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  
  const acceptInvite = useAcceptInvite();
  const declineInvite = useDeclineInvite();
  const deleteNotification = useDeleteNotification();

  const [{ x, opacity, scale }, api] = useSpring(() => ({
    x: 0,
    opacity: 1,
    scale: 1,
    config: { tension: 300, friction: 30 }
  }));

  const handleVibration = (pattern: number | number[]) => {
    if ('vibrate' in navigator) {
      navigator.vibrate(pattern);
    }
  };

  const handleAccept = async () => {
    if (isProcessing || isProcessed) return;
    
    setIsProcessing(true);
    handleVibration(50);
    
    try {
      await acceptInvite.mutateAsync(notification.entity_id);
      // Cancella la notifica dopo aver accettato l'invito
      await deleteNotification.mutateAsync(notification.id);
      setIsProcessed(true);
      api.start({ 
        x: window.innerWidth, 
        opacity: 0,
        onRest: () => onProcessed?.()
      });
      toast.success('Invito accettato!');
    } catch (error) {
      setIsProcessing(false);
      api.start({ x: 0 });
      toast.error('Errore nell\'accettazione dell\'invito');
    }
  };

  const handleDecline = async () => {
    if (isProcessing || isProcessed) return;
    
    setIsProcessing(true);
    handleVibration([50, 50, 50]);
    
    try {
      await declineInvite.mutateAsync(notification.entity_id);
      // Cancella la notifica dopo aver rifiutato l'invito
      await deleteNotification.mutateAsync(notification.id);
      setIsProcessed(true);
      api.start({ 
        x: -window.innerWidth, 
        opacity: 0,
        onRest: () => onProcessed?.()
      });
      toast.success('Invito rifiutato');
    } catch (error) {
      setIsProcessing(false);
      api.start({ x: 0 });
      toast.error('Errore nel rifiuto dell\'invito');
    }
  };

  const bind = useGesture(
    {
      onDrag: ({ active, movement: [mx], velocity: [vx], direction: [dx] }) => {
        if (isProcessing || isProcessed) return;

        if (active) {
          // Limiting drag distance and adding resistance
          const resistance = Math.abs(mx) > 100 ? 0.1 : 1;
          api.start({ 
            x: mx * resistance,
            scale: 1 - Math.abs(mx) * 0.0005,
            immediate: true 
          });
        } else {
          // Determine if swipe was significant enough
          const swipeThreshold = 80;
          const velocityThreshold = 0.5;
          
          if (Math.abs(mx) > swipeThreshold || Math.abs(vx) > velocityThreshold) {
            if (dx > 0) {
              // Swipe right - Accept
              api.start({ x: window.innerWidth * 0.8, scale: 0.9 });
              setTimeout(handleAccept, 100);
            } else {
              // Swipe left - Decline
              api.start({ x: -window.innerWidth * 0.8, scale: 0.9 });
              setTimeout(handleDecline, 100);
            }
          } else {
            // Snap back to center
            api.start({ x: 0, scale: 1 });
          }
        }
      }
    },
    {
      drag: {
        filterTaps: true,
        axis: 'x'
      }
    }
  );

  return (
    <div className="relative overflow-hidden rounded-lg">
      {/* Action Background Indicators */}
      <div className="absolute inset-0 flex">
        <div className="flex-1 bg-red-500 flex items-center justify-start pl-6">
          <X className="h-6 w-6 text-white" />
          <span className="ml-2 text-white font-medium">Rifiuta</span>
        </div>
        <div className="flex-1 bg-green-500 flex items-center justify-end pr-6">
          <span className="mr-2 text-white font-medium">Accetta</span>
          <Check className="h-6 w-6 text-white" />
        </div>
      </div>

      {/* Main Card */}
      <animated.div
        ref={cardRef}
        {...bind()}
        style={{ x, opacity, scale }}
        className="bg-blue-50 border border-blue-200 rounded-lg p-4 cursor-grab active:cursor-grabbing touch-pan-x select-none relative z-10"
      >
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 mt-1">
            <Users className="h-5 w-5 text-blue-500" />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <h3 className="font-medium text-gray-900 text-sm">
                Invito al Gruppo
              </h3>
              <span className="text-xs text-gray-500 flex-shrink-0">
                {format(new Date(notification.created_at), "HH:mm", { locale: it })}
              </span>
            </div>
            
            <p className="text-sm text-gray-600 mb-3">
              Hai ricevuto un nuovo invito per unirti a un gruppo
            </p>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                <span className="text-xs text-blue-600 font-medium">
                  Scorri per rispondere
                </span>
              </div>
              
              <span className="text-xs text-gray-400">
                {format(new Date(notification.created_at), "dd MMM", { locale: it })}
              </span>
            </div>

            {/* Mobile Action Buttons (fallback) */}
            <div className="flex gap-2 mt-3 sm:hidden">
              <button
                onClick={handleDecline}
                disabled={isProcessing}
                className="flex-1 bg-red-500 text-white py-2 px-3 rounded-lg text-sm font-medium disabled:opacity-50"
              >
                <X className="h-4 w-4 inline mr-1" />
                Rifiuta
              </button>
              <button
                onClick={handleAccept}
                disabled={isProcessing}
                className="flex-1 bg-green-500 text-white py-2 px-3 rounded-lg text-sm font-medium disabled:opacity-50"
              >
                <Check className="h-4 w-4 inline mr-1" />
                Accetta
              </button>
            </div>
          </div>
        </div>
      </animated.div>
    </div>
  );
};

export default SwipeableGroupInviteCard;

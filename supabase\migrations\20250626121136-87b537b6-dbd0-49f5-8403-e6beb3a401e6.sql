


-- Drop the problematic policy first
DROP POLICY IF EXISTS "Users can view members of groups they belong to" ON group_members;

-- Create a security definer function to check if user can view group members
CREATE OR REPLACE FUNCTION public.can_user_view_group_members(target_group_id uuid, target_user_id uuid)
RETURNS boolean AS $$
BEGIN
  -- User can view if they are the target user
  IF target_user_id = auth.uid() THEN
    RETURN true;
  END IF;
  
  -- User can view if they are a member of the same group
  IF EXISTS (
    SELECT 1 FROM group_members 
    WHERE group_id = target_group_id 
    AND user_id = auth.uid()
  ) THEN
    RETURN true;
  END IF;
  
  -- User can view if they created the group
  IF EXISTS (
    SELECT 1 FROM groups 
    WHERE id = target_group_id 
    AND created_by = auth.uid()
  ) THEN
    RETURN true;
  END IF;
  
  RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER STABLE;

-- Create the corrected policy using the security definer function
CREATE POLICY "Users can view members of groups they belong to" ON group_members
    FOR SELECT
    USING (
        auth.uid() IS NOT NULL AND 
        public.can_user_view_group_members(group_id, user_id)
    );



// src/hooks/intro/useUserIntro.ts
// Description: 
// This hook is used to show the intro screen to the user 
// version 1.0.0 2025-07-04 00:40 
// Author: <PERSON>



import { useState, useEffect } from 'react';

const SWIPE_INTRO_STORAGE_KEY = 'catchup-user-intro-shown';

export const useUserIntro = () => {
  const [showIntro, setShowIntro] = useState(false);

  useEffect(() => {
    const hasSeenIntro = localStorage.getItem(SWIPE_INTRO_STORAGE_KEY);
    if (!hasSeenIntro || hasSeenIntro === 'false') {
      setShowIntro(true);
    }
  }, []);

  const hideIntro = () => {
    setShowIntro(false);
    localStorage.setItem(SWIPE_INTRO_STORAGE_KEY, 'true');
  };

  // const hideIntroForever = () => {
  //   setShowIntro(false);
  //   localStorage.setItem(SWIPE_INTRO_STORAGE_KEY, 'forever');
  // };

  const closeIntro = () => {
    setShowIntro(false);
  };
  const resetIntro = () => {
    localStorage.removeItem(SWIPE_INTRO_STORAGE_KEY);
    setShowIntro(true);
  };

  return {
    showIntro,
    hideIntro,
    closeIntro,
   // hideIntroForever,
    resetIntro
  };
};
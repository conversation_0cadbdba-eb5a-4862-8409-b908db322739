import * as React from "react";
import * as SliderPrimitive from "@radix-ui/react-slider";
import { cn } from "@/lib/utils";
import { Clock } from "lucide-react";
import { Button } from "@/components/ui/button";

/**
 * TimeSlider Component
 *
 * Un componente slider ottimizzato per selezionare orari nell'arco della giornata.
 * Il selettore si muove a intervalli di 15 minuti (00:00, 00:15, 00:30, 00:45, etc.).
 *
 * @param {Object} props - Proprietà del componente
 * @param {number[]} props.value - <PERSON>ore corrente (0-100 che rappresenta 00:00-23:59)
 * @param {(value: number[]) => void} props.onValueChange - Gestore per i cambiamenti di valore
 * @param {"range" | "gradient"} props.fillMode - Modalità di riempimento: "range" per colore singolo, "gradient" per colori 24h
 */
const TimeSlider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root> & {
    onResetToNow?: () => void;
    fillMode?: "range" | "gradient";
  }
>(({ className, value, onValueChange, onResetToNow, fillMode = "gradient", ...props }, ref) => {
  // Calcola l'ora corrente solo quando richiesto tramite il pulsante
  const handleNowClick = React.useCallback(() => {
    if (!onValueChange) return;

    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();

    // Arrotonda ai 15 minuti più vicini
    const roundedMinutes = Math.round(minutes / 15) * 15;

    // Converti l'ora corrente in posizione percentuale (0:00 = 0%, 23:59 = 100%)
    const totalMinutesSinceMidnight = hours * 60 + roundedMinutes;
    const totalMinutesInDay = 24 * 60;
    const percentage = (totalMinutesSinceMidnight / totalMinutesInDay) * 100;

    onValueChange([Math.min(100, Math.max(0, percentage))]);

    // Se esiste la funzione onResetToNow, chiamala (per aggiornare anche la data)
    if (onResetToNow) onResetToNow();
  }, [onValueChange, onResetToNow]);

  // Precalcola la posizione del thumb per l'etichetta "ORA"
  const thumbPosition = value ? value[0] : 33;

  // Converti la percentuale dello slider in stringa oraria (arrotondata ai 15 minuti)
  const getTimeFromPercentage = (percentage: number): string => {
    const totalMinutes = (percentage / 100) * 24 * 60;
    // Arrotonda ai 15 minuti più vicini
    const roundedMinutes = Math.round(totalMinutes / 15) * 15;
    const hours = Math.floor(roundedMinutes / 60);
    const minutes = roundedMinutes % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  };

  // Determina se l'ora è mattina, pomeriggio, sera o notte
  const getTimeOfDay = (percentage: number): string => {
    const hours = Math.floor((percentage / 100) * 24);
    if (hours >= 5 && hours < 12) return "mattina";
    if (hours >= 12 && hours < 18) return "pomeriggio";
    if (hours >= 18 && hours < 22) return "sera";
    return "notte";
  };

  // Ottieni il periodo del giorno corrente
  const timeOfDay = value ? getTimeOfDay(value[0]) : "mattina";

  // Colori per i diversi periodi del giorno
  const timeColors = {
    mattina: "bg-amber-400",
    pomeriggio: "bg-primary",
    sera: "bg-indigo-600",
    notte: "bg-slate-700"
  };

  return (
    <div className="w-full space-y-1">
      <div className="flex justify-between items-center text-xs font-medium px-1">
        <span className="text-gray-600">Naviga per orario</span>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleNowClick}
          className="text-xs h-7 py-1 px-2"
        >
          <Clock className="h-3.5 w-3.5 mr-1" />
          Ora
        </Button>
      </div>

      <div className="relative mt-2">
        <SliderPrimitive.Root
          ref={ref}
          className={cn(
            "relative flex w-full touch-none select-none items-center",
            className
          )}
          value={value}
          onValueChange={onValueChange}
          min={0}
          max={100}
          step={100 / (24 * 4)} // 15 minuti = 1/96 del giorno
          {...props}
        >
          <SliderPrimitive.Track className="relative h-2 w-full grow overflow-hidden rounded-full bg-gray-200">
            {fillMode === "gradient" ? (
              <>
                {/* Smooth gradient for 24-hour color representation */}
                <div 
                  className="absolute inset-0 rounded-full"
                  style={{
                    background: `linear-gradient(to right,
                      rgb(51 65 85) 0%,           /* Night: 00:00 */
                      rgb(51 65 85) 20.8%,        /* Night: 05:00 */
                      rgb(251 191 36) 35%,        /* Morning transition */
                      rgb(251 191 36) 50%,        /* Morning: 12:00 */
                      hsl(var(--primary)) 62.5%,  /* Afternoon transition */
                      hsl(var(--primary)) 75%,    /* Afternoon: 18:00 */
                      rgb(79 70 229) 83.5%,       /* Evening transition */
                      rgb(79 70 229) 91.7%,       /* Evening: 22:00 */
                      rgb(51 65 85) 100%          /* Night: 24:00 */
                    )`
                  }}
                ></div>
                
                {/* Semi-transparent overlay for the selected range */}
                <SliderPrimitive.Range className="absolute h-full bg-white/20 rounded-full" />
              </>
            ) : (
              /* Original range mode - single color based on time of day */
              <SliderPrimitive.Range className={`absolute h-full ${timeColors[timeOfDay]}`} />
            )}
          </SliderPrimitive.Track>
          <SliderPrimitive.Thumb className={`block h-5 w-5 rounded-full border-2 ${fillMode === "gradient" ? "border-white bg-white shadow-lg" : "border-primary bg-white"} ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border-solid`} />
        </SliderPrimitive.Root>

        <div
          className="absolute top-3 text-center text-xs font-bold text-brand-primary mt-3"
          style={{
            left: `${thumbPosition}%`,
            transform: 'translateX(-50%)'
          }}
        >
          {value ? getTimeFromPercentage(value[0]) : "ORA"}
        </div>
      </div>

      <div className="flex justify-between items-center text-xs text-gray-600 mt-4">
        <span>00:00</span>
        <span>06:00</span>
        <span>12:00</span>
        <span>18:00</span>
        <span>24:00</span>
      </div>
    </div>
  );
});

TimeSlider.displayName = "TimeSlider";

export { TimeSlider };

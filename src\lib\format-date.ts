import { format } from 'date-fns';

function getOrdinal(n: number): string {
  if (n > 3 && n < 21) return `${n}th`;
  switch (n % 10) {
    case 1: return `${n}st`;
    case 2: return `${n}nd`;
    case 3: return `${n}rd`;
    default: return `${n}th`;
  }
}

export const formatFriendlyDateTimeCET  = (date: Date): string => {
  const day = getOrdinal(date.getDate());
  const month = format(date, 'MMM');
  const year = format(date, 'yyyy');
  const time = format(date, 'h:mma').toLowerCase(); // '12:22pm'

  return `${day} ${month}, ${year} ${time} CET`;
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return format(date, 'dd/MM/yyyy');
};

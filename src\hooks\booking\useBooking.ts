
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { Database } from "@/integrations/supabase/types";
import { BookingDetails } from "@/types/booking";
import { createBookingChatConversation } from "@/services/chatService";
import { useCreateNotification } from "@/hooks/notifications/useNotifications";

type Deal = Database["public"]["Tables"]["deals"]["Row"];
type Business = Database["public"]["Tables"]["businesses"]["Row"];

interface DealWithBusiness extends Deal {
  businesses: Business;
}

export const useBooking = (
  deal: DealWithBusiness | null,
  bookingDetails: BookingDetails | null,
  userId: string | undefined,
  isFakeBooking: boolean = false
) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const createNotification = useCreateNotification();

  const handleConfirmBooking = async () => {
    if (!userId || !deal || !bookingDetails) return;

    if (!deal.businesses?.owner_id) {
      toast.error("Errore: proprietario dell'attività non trovato");
      return;
    }

    // Check if the logged in user is the owner of the business
    if (userId === deal.businesses.owner_id) {
      toast.error("Non puoi prenotare un'offerta della tua attività");
      return;
    }
 
    const booking_status = deal.auto_confirm ? "confirmed" : "pending";
    setIsLoading(true);
   
  
    try {
      // Prepara i dati del QR code
      const qrData = {
        bookingId: crypto.randomUUID(),
        dealTitle: deal.title,
        businessName: deal.businesses.name,
        date: bookingDetails.date,
        time: bookingDetails.start_time,
        status: booking_status,
      };

      // 1. Crea la prenotazione con i dati del QR code e i prezzi
      const { data: booking, error: bookingError } = await supabase
        .from("bookings")
        .insert({
          user_id: userId,
          deal_id: deal.id,
          booking_date: bookingDetails.date,
          booking_time: bookingDetails.start_time,
          status: booking_status,
          qr_data: qrData,
          original_price: deal.original_price,
          discounted_price: deal.discounted_price,
          discount_percentage: deal.discount_percentage,
          fake: isFakeBooking,
        })
        .select()
        .single();

      if (bookingError) throw bookingError;

      //Update the qr_data with the booking id
      const qrDataId = {
        bookingId: booking.id,
        dealTitle: deal.title,
        businessName: deal.businesses.name,
        date: bookingDetails.date,
        time: bookingDetails.start_time,
        status: booking_status,
      };

      const { error: qrDataError } = await supabase
        .from("bookings")
        .update({
          qr_data: qrDataId,
        })
        .eq("id", booking.id);

      if (qrDataError) throw qrDataError;

      // 2. Aggiorna la tabella time_slot_bookings con i dati della prenotazione
      if (bookingDetails.start_time && bookingDetails.end_time) {
        const dayOfWeek = bookingDetails.day || new Date(bookingDetails.date).getDay() || 7;
        
        const { error: timeSlotError } = await supabase
          .from("time_slot_bookings")
          .insert({
            deal_id: deal.id,
            booking_id: booking.id,
            day_of_week: dayOfWeek,
            start_time: bookingDetails.start_time,
            end_time: bookingDetails.end_time,
            booking_date: bookingDetails.date,
            fake: isFakeBooking,
            booked_seats: 1 // Default a 1 posto per prenotazione, può essere parametrizzato in futuro
          });
          
        if (timeSlotError) {
          console.error("Errore nell'aggiornamento della disponibilità degli slot:", timeSlotError);
          // Non blocchiamo il flusso per questo errore, ma lo registriamo
        }
        
        // Aggiorniamo anche il conteggio dei posti prenotati nel time_slots dell'offerta (It is necessary to update the time_slots table???)

      }

      // 3. Crea la conversazione, aggiungi i partecipanti e il messaggio di benvenuto
      const conversationResult = await createBookingChatConversation({
        bookingId: booking.id,
        businessId: deal.business_id,
        dealId: deal.id,
        businessOwnerId: deal.businesses.owner_id,
        customerId: userId,
        welcomeMessageData: {
          businessName: deal.businesses.name,
          address: deal.businesses.address,
          dealTitle: deal.title,
          dealDescription: deal.description,
          bookingDate: bookingDetails.date,
          startTime: bookingDetails.start_time,
          discountedPrice: deal.discounted_price,
          discountPercentage: deal.discount_percentage,
          qrData: qrDataId
        }
      });

      // 4. Create notification
      await createNotification.mutateAsync({
        entity: 'bookings',
        entity_id: conversationResult.conversationId
      });

      //toast.success("Prenotazione confermata con successo!");
      navigate(`/booking-confirmed/${booking.id}`);
    } catch (error) {
      console.error("Error creating booking:", error);
      toast.error("Errore durante la conferma della prenotazione");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    handleConfirmBooking,
  };
};

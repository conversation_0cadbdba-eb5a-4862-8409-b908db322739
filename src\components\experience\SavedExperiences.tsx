import { useState, useEffect } from "react";
import { Calendar, Clock, Euro, MapPin, MoreVertical, Edit, Trash, Copy, Eye } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import type { Experience } from "@/pages/mains/Experience";
import { useAuth } from "@/hooks/auth/useAuth";

interface SavedExperiencesProps {
  onSelectExperience: (experience: Experience) => void;
}

const SavedExperiences = ({ onSelectExperience }: SavedExperiencesProps) => {
  const { user } = useAuth();
  const [savedExperiences, setSavedExperiences] = useState<Experience[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'draft' | 'confirmed' | 'completed'>('all');
  const [experienceToDelete, setExperienceToDelete] = useState<string | null>(null);

  // Mock saved journeys data
  useEffect(() => {
    const mockExperiences: Experience[] = [
      {
        id: 'saved-1',
        name: 'Weekend Benessere Marzo',
        description: 'Relax completo in centro città',
        date: '2024-03-15',
        stops: [
          {
            id: 'stop-1',
            dealId: 'deal-1',
            businessId: 'business-1',
            dealTitle: 'Massaggio Rilassante',
            businessName: 'Spa Milano Centro',
            address: 'Via Montenapoleone 10',
            latitude: 45.4685,
            longitude: 9.1824,
            startTime: '10:00',
            endTime: '11:00',
            estimatedDuration: 60,
            price: 80,
            category: 'Benessere',
            order: 1
          },
          {
            id: 'stop-2',
            dealId: 'deal-2',
            businessId: 'business-2',
            dealTitle: 'Pranzo Detox',
            businessName: 'Green Café',
            address: 'Corso Buenos Aires 20',
            latitude: 45.4758,
            longitude: 9.2025,
            startTime: '13:00',
            endTime: '14:00',
            estimatedDuration: 60,
            price: 25,
            category: 'Ristorazione',
            order: 2
          }
        ],
        totalDuration: 120,
        totalPrice: 105,
        totalTravelTime: 20,
        status: 'confirmed',
        userId: user?.id,
        createdAt: '2024-03-01T10:00:00Z',
        updatedAt: '2024-03-01T10:00:00Z'
      },
      {
        id: 'saved-2',
        name: 'Tour Gastronomico Aprile',
        description: 'Scoperta dei sapori locali',
        date: '2024-04-20',
        stops: [
          {
            id: 'stop-3',
            dealId: 'deal-3',
            businessId: 'business-3',
            dealTitle: 'Degustazione Vini',
            businessName: 'Enoteca Centrale',
            address: 'Via Brera 5',
            latitude: 45.4722,
            longitude: 9.1881,
            startTime: '11:00',
            endTime: '12:30',
            estimatedDuration: 90,
            price: 40,
            category: 'Enogastronomia',
            order: 1
          },
          {
            id: 'stop-4',
            dealId: 'deal-4',
            businessId: 'business-4',
            dealTitle: 'Pranzo Tradizionale',
            businessName: 'Osteria del Centro',
            address: 'Via Dante 15',
            latitude: 45.4695,
            longitude: 9.1845,
            startTime: '13:00',
            endTime: '14:30',
            estimatedDuration: 90,
            price: 55,
            category: 'Ristorazione',
            order: 2
          },
          {
            id: 'stop-5',
            dealId: 'deal-5',
            businessId: 'business-5',
            dealTitle: 'Corso di Cucina',
            businessName: 'Scuola Chef Milano',
            address: 'Via Garibaldi 30',
            latitude: 45.4730,
            longitude: 9.1890,
            startTime: '16:00',
            endTime: '18:00',
            estimatedDuration: 120,
            price: 75,
            category: 'Enogastronomia',
            order: 3
          }
        ],
        totalDuration: 300,
        totalPrice: 170,
        totalTravelTime: 30,
        status: 'draft',
        userId: user?.id,
        createdAt: '2024-03-10T15:30:00Z',
        updatedAt: '2024-03-12T09:20:00Z'
      },
      {
        id: 'saved-3',
        name: 'Giornata Sport Febbraio',
        description: 'Allenamento e relax',
        date: '2024-02-28',
        stops: [
          {
            id: 'stop-6',
            dealId: 'deal-6',
            businessId: 'business-6',
            dealTitle: 'Personal Training',
            businessName: 'FitClub Pro',
            address: 'Via Sport 25',
            latitude: 45.4650,
            longitude: 9.1800,
            startTime: '09:00',
            endTime: '10:00',
            estimatedDuration: 60,
            price: 50,
            category: 'Fitness',
            order: 1
          },
          {
            id: 'stop-7',
            dealId: 'deal-7',
            businessId: 'business-7',
            dealTitle: 'Yoga Class',
            businessName: 'Yoga Studio Zen',
            address: 'Via Pace 18',
            latitude: 45.4710,
            longitude: 9.1850,
            startTime: '11:30',
            endTime: '12:30',
            estimatedDuration: 60,
            price: 25,
            category: 'Fitness',
            order: 2
          }
        ],
        totalDuration: 120,
        totalPrice: 75,
        totalTravelTime: 25,
        status: 'completed',
        userId: user?.id,
        createdAt: '2024-02-20T14:00:00Z',
        updatedAt: '2024-02-28T18:00:00Z'
      }
    ];

    setSavedExperiences(mockExperiences);
  }, [user?.id]);

  const filteredExperiences = selectedStatus === 'all' 
    ? savedExperiences 
    : savedExperiences.filter(experience => experience.status === selectedStatus);

  const getStatusColor = (status: Experience['status']) => {
    switch (status) {
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: Experience['status']) => {
    switch (status) {
      case 'draft':
        return 'Bozza';
      case 'confirmed':
        return 'Confermato';
      case 'completed':
        return 'Completato';
      default:
        return 'Sconosciuto';
    }
  };

  const handleDeleteExperience = async (journeyId: string) => {
    try {
      setSavedExperiences(prev => prev.filter(journey => journey.id !== journeyId));
      toast.success("Esperienza eliminato con successo");
    } catch (error) {
      toast.error("Errore durante l'eliminazione dell'esperienza");
    }
    setExperienceToDelete(null);
  };

  const handleDuplicateExperience = (experience: Experience) => {
    const duplicatedExperience: Experience = {
      ...experience,
      id: `journey-${Date.now()}`,
      name: `${experience.name} (Copia)`,
      status: 'draft',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    setSavedExperiences(prev => [duplicatedExperience, ...prev]);
    toast.success("Esperienza duplicato con successo");
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('it-IT', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('it-IT', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold mb-4">Viaggi Salvati</h2>
        <p className="text-muted-foreground mb-6">
          Gestisci i tuoi viaggi salvati e pianificati
        </p>
      </div>

      {/* Status Filter */}
      <div className="flex flex-wrap gap-2">
        {[
          { id: 'all', label: 'Tutti' },
          { id: 'draft', label: 'Bozze' },
          { id: 'confirmed', label: 'Confermati' },
          { id: 'completed', label: 'Completati' }
        ].map((status) => (
          <Button
            key={status.id}
            variant={selectedStatus === status.id ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedStatus(status.id as any)}
          >
            {status.label}
          </Button>
        ))}
      </div>

      {/* Experiences List */}
      <div className="space-y-4">
        {filteredExperiences.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Nessuna esperienza trovata</h3>
              <p className="text-muted-foreground">
                {selectedStatus === 'all' 
                  ? "Non hai ancora salvato nessuna esperienza."
                  : `Non hai espereinze con stato "${getStatusLabel(selectedStatus as Experience['status'])}"`
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredExperiences.map((experience) => (
            <Card key={experience.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <CardTitle className="text-lg">{experience.name}</CardTitle>
                      <Badge className={getStatusColor(experience.status)}>
                        {getStatusLabel(experience.status)}
                      </Badge>
                    </div>
                    {experience.description && (
                      <p className="text-sm text-muted-foreground mb-2">
                        {experience.description}
                      </p>
                    )}
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(experience.date)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <span>Modificato: {formatDateTime(experience.updatedAt)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onSelectExperience(experience)}>
                        <Edit className="w-4 h-4 mr-2" />
                        Modifica
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDuplicateExperience(experience)}>
                        <Copy className="w-4 h-4 mr-2" />
                        Duplica
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => setExperienceToDelete(experience.id)}
                        className="text-destructive"
                      >
                        <Trash className="w-4 h-4 mr-2" />
                        Elimina
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Experience Stats */}
                <div className="grid grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-4 h-4 text-muted-foreground" />
                    <span>{experience.stops.length} tappe</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span>{Math.round(experience.totalDuration / 60)}h</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Euro className="w-4 h-4 text-muted-foreground" />
                    <span>{experience.totalPrice}</span>
                  </div>
                  <div className="text-right">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => onSelectExperience(experience)}
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      Apri
                    </Button>
                  </div>
                </div>

                {/* Experience Preview */}
                {experience.stops.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Anteprima Tappe:</h4>
                    <div className="space-y-1">
                      {experience.stops.slice(0, 3).map((stop, index) => (
                        <div key={stop.id} className="flex items-center justify-between text-xs bg-muted p-2 rounded">
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className="text-xs px-1">
                              {index + 1}
                            </Badge>
                            <span className="font-medium">{stop.dealTitle}</span>
                            <span className="text-muted-foreground">- {stop.businessName}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-muted-foreground">
                            <span>{stop.startTime}</span>
                            <span>€{stop.price}</span>
                          </div>
                        </div>
                      ))}
                      {experience.stops.length > 3 && (
                        <div className="text-xs text-muted-foreground text-center py-1">
                          +{experience.stops.length - 3} altre tappe
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!experienceToDelete} onOpenChange={() => setExperienceToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Elimina Espereinza</AlertDialogTitle>
            <AlertDialogDescription>
              Sei sicuro di voler eliminare questa esperienza? Questa azione non può essere annullata.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Annulla</AlertDialogCancel>
            <AlertDialogAction 
              onClick={() => experienceToDelete && handleDeleteExperience(experienceToDelete)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Elimina
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default SavedExperiences;
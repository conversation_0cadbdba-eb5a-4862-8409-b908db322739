import { useParams, useNavigate } from "react-router-dom";

import { useCreateDeal } from "@/hooks/deal/useCreateDeal";
import UnifiedHeader from "@/components/toolbars/UnifiedHeader";
import CreateDealForm from "@/components/deals/creation/CreateDealForm";

const CreateDeal = () => {
  const { businessId } = useParams();
  const navigate = useNavigate();
  const { formData, setFormData, isSubmitting, handleSubmit } = useCreateDeal(businessId);

  if (!businessId) return null;

  /**
   * Handle form submission
   */
  const onSubmitDeal = () => {
    // Simulate form submission
    const formEvent = new Event('submit', { cancelable: true }) as unknown as React.FormEvent;
    handleSubmit(formEvent);
  };

  /**
   * Handle form cancellation
   */
  const onCancelDeal = () => {
    // Navigate back to business page
    navigate(`/business/${businessId}`);
  };

  return (
    <div className="bg-gray-50 max-w-md mx-auto min-h-screen">
      <UnifiedHeader variant="minimal" showBackButton={true} title="Crea Offerta" />

      <main className="px-4 pt-20 pb-32">
        <div className="space-y-6">
          <CreateDealForm 
            formData={formData}
            setFormData={setFormData}
            businessId={businessId}
            onSubmit={onSubmitDeal}
            onCancel={onCancelDeal}
          />
        </div>
      </main>

      {/* <DealFooter
        onSubmit={handleSubmit}
        isSubmitting={isSubmitting}
      /> */}
    </div>
  );
};

export default CreateDeal;

import { supabase } from "@/integrations/supabase/client";

// Weather types
export interface WeatherData {
  current: {
    temp: number;
    feelsLike: number;
    humidity: number;
    pressure: number;
    visibility: number;
    uvIndex: number;
    windSpeed: number;
    windDirection: number;
    condition: string;
    icon: string;
    description: string;
  };
  location: {
    city: string;
    country: string;
    lat: number;
    lon: number;
  };
  forecast: DailyForecast[];
  hourly: HourlyForecast[];
}

export interface DailyForecast {
  date: string;
  tempMin: number;
  tempMax: number;
  condition: string;
  icon: string;
  description: string;
  precipitation: number;
  humidity: number;
  windSpeed: number;
}

export interface HourlyForecast {
  time: string;
  temp: number;
  condition: string;
  icon: string;
  precipitation: number;
}

export interface WeatherAlert {
  title: string;
  description: string;
  severity: 'minor' | 'moderate' | 'severe' | 'extreme';
  start: string;
  end: string;
}

class WeatherService {
  private baseUrl = 'https://api.openweathermap.org/data/2.5';
  private onecallUrl = 'https://api.openweathermap.org/data/3.0/onecall';

  /**
   * Get current weather and forecast for given coordinates
   */
  async getWeatherData(lat: number, lon: number): Promise<WeatherData> {
    try {
      // Call the edge function to get weather data
      const { data, error } = await supabase.functions.invoke('get-weather-data', {
        body: { lat, lon }
      });

      if (error) {
        console.error('Weather API error:', error);
        throw new Error('Failed to fetch weather data');
      }

      return data;
    } catch (error) {
      console.error('Weather service error:', error);
      throw error;
    }
  }

  /**
   * Get weather alerts for the location
   */
  async getWeatherAlerts(lat: number, lon: number): Promise<WeatherAlert[]> {
    // Weather alerts are not implemented yet, return empty array
    return [];
  }

  /**
   * Get weather-based deal recommendations
   */
  async getWeatherBasedDeals(lat: number, lon: number, weatherCondition: string): Promise<any[]> {
    try {
      const { data, error } = await supabase.functions.invoke('get-weather-based-deals', {
        body: { lat, lon, weather_condition: weatherCondition }
      });

      if (error) {
        console.error('Weather-based deals error:', error);
        return [];
      }

      return data?.deals || [];
    } catch (error) {
      console.error('Weather-based deals service error:', error);
      return [];
    }
  }

  /**
   * Get weather icon URL from OpenWeatherMap
   */
  getWeatherIconUrl(iconCode: string, size: '2x' | '4x' = '2x'): string {
    return `https://openweathermap.org/img/wn/${iconCode}@${size}.png`;
  }

  /**
   * Convert wind direction in degrees to compass direction
   */
  getWindDirection(degrees: number): string {
    const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
    const index = Math.round(degrees / 22.5) % 16;
    return directions[index];
  }

  /**
   * Get weather condition color for UI theming
   */
  getWeatherColor(condition: string): string {
    const conditionLower = condition.toLowerCase();
    
    if (conditionLower.includes('sun') || conditionLower.includes('clear')) {
      return 'from-orange-400 to-yellow-400';
    } else if (conditionLower.includes('cloud')) {
      return 'from-gray-400 to-blue-400';
    } else if (conditionLower.includes('rain') || conditionLower.includes('drizzle')) {
      return 'from-blue-500 to-indigo-600';
    } else if (conditionLower.includes('snow')) {
      return 'from-blue-200 to-blue-400';
    } else if (conditionLower.includes('thunder') || conditionLower.includes('storm')) {
      return 'from-purple-600 to-indigo-800';
    } else if (conditionLower.includes('mist') || conditionLower.includes('fog')) {
      return 'from-gray-300 to-gray-500';
    }
    
    return 'from-blue-400 to-blue-600'; // default
  }

  /**
   * Format temperature with proper unit
   */
  formatTemperature(temp: number, unit: 'C' | 'F' = 'C'): string {
    return `${Math.round(temp)}°${unit}`;
  }

  /**
   * Check if weather is suitable for outdoor activities
   */
  isOutdoorFriendly(weatherData: WeatherData): boolean {
    const { current } = weatherData;
    const condition = current.condition.toLowerCase();
    
    // Check for bad weather conditions
    const badConditions = ['rain', 'storm', 'thunder', 'snow', 'drizzle'];
    const hasBadWeather = badConditions.some(bad => condition.includes(bad));
    
    // Check temperature (too hot or too cold)
    const temperature = current.temp;
    const tooHot = temperature > 35; // 35°C
    const tooCold = temperature < 0; // 0°C
    
    // Check wind speed (too windy)
    const tooWindy = current.windSpeed > 10; // 10 m/s
    
    return !hasBadWeather && !tooHot && !tooCold && !tooWindy;
  }
}

export const weatherService = new WeatherService();
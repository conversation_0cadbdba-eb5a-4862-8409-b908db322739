
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { businessType, targetAudience, businessName, dealCategories = [] } = await req.json();
    
    console.log('Received request:', { businessType, targetAudience, businessName, dealCategories });

    // Create a comprehensive system prompt that ensures specific, industry-relevant offers
    const systemPrompt = `Sei un esperto di marketing digitale specializzato nella creazione di offerte promozionali specifiche e accattivanti per il settore servizi. 

REGOLE FONDAMENTALI:
- NON creare mai offerte generiche come "sconto del 20% su tutto"
- Crea sempre offerte SPECIFICHE per servizi concreti nel settore indicato
- L'offerta deve essere legata alle categorie di offerta fornite
- Deve essere realistica e implementabile
- Il titolo deve essere accattivante e specifico (max 60 caratteri)
- La descrizione deve evidenziare il valore concreto e i benefici (max 200 caratteri)

ESEMPI DI OFFERTE SPECIFICHE PER SETTORE:
- Parrucchiere: "Taglio + Piega + Trattamento Ristrutturante"
- Ristorante: "Menu Degustazione 4 Portate con Vino"
- Centro Estetico: "Pulizia Viso Profonda + Maschera Idratante"
- Palestra: "Valutazione Posturale + 3 Allenamenti Personal"
- Meccanico: "Tagliando Completo + Controllo Climatizzatore"
- Dentista: "Visita + Pulizia Dentale + Fluoroprofilassi"`;

    const userPrompt = `Genera un'offerta promozionale specifica per:
- Attività: ${businessType}
- Nome business: ${businessName || 'l\'attività'}
- Target: ${targetAudience}
- Categorie offerta disponibili: ${dealCategories.join(', ') || 'Nessuna categoria specifica'}

L'offerta deve essere:
1. Un SERVIZIO SPECIFICO (non uno sconto generico)
2. Legata alle categorie di offerta indicate
3. Realistica per questo tipo di business
4. Attraente per il target indicato
5. Con un valore concreto e misurabile`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4.1-2025-04-14',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userPrompt
          }
        ],
        tools: [
          {
            type: "function",
            function: {
              name: "generate_deal_content",
              description: "Genera il contenuto di un'offerta promozionale specifica per servizi",
              parameters: {
                type: "object",
                properties: {
                  title: {
                    type: "string",
                    description: "Titolo specifico dell'offerta di servizio (max 60 caratteri). Deve descrivere un servizio concreto, non uno sconto generico."
                  },
                  description: {
                    type: "string",
                    description: "Descrizione dettagliata che spiega esattamente cosa include l'offerta di servizio e quale valore concreto offre al cliente (max 200 caratteri)"
                  },
                  serviceDetails: {
                    type: "string",
                    description: "Dettagli specifici su cosa comprende esattamente questo servizio"
                  },
                  suggestedPrice: {
                    type: "string", 
                    description: "Prezzo suggerito realistico per questo tipo di servizio nel mercato italiano"
                  }
                },
                required: ["title", "description", "serviceDetails", "suggestedPrice"]
              }
            }
          }
        ],
        tool_choice: {
          type: "function",
          function: { name: "generate_deal_content" }
        },
        temperature: 0.8,
        max_tokens: 1000
      }),
    });

    const data = await response.json();
    console.log('OpenAI response:', JSON.stringify(data, null, 2));

    if (data.error) {
      throw new Error(`OpenAI API error: ${data.error.message}`);
    }

    // Extract the content from the modern tools format
    const toolCall = data.choices[0].message.tool_calls[0];
    const generatedContent = JSON.parse(toolCall.function.arguments);

    console.log('Generated content:', generatedContent);

    return new Response(JSON.stringify(generatedContent), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error in generate-deal-content function:', error);
    return new Response(JSON.stringify({ 
      error: error.message,
      details: 'Failed to generate deal content'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});

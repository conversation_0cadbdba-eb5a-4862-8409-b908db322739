
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAuth } from "@/hooks/auth/useAuth";
import { UserPreference, RecommendedDeal } from "@/types/preferences";



// Note. Even if user_interactions is empty, the user preferences are still calculated based on the bookings. TODO: Check if this is correct.

export const useUserPreferences = () => {
  const { user } = useAuth();
  const [preferences, setPreferences] = useState<UserPreference[]>([]);
  const [recommendedDeals, setRecommendedDeals] = useState<RecommendedDeal[]>([]);
  const [isLoading, setIsLoading] = useState(true);




  useEffect(() => {
    if (!user) return;
    
    const analyzeUserBehavior = async () => {
      setIsLoading(true);
      try {
        // 1. Recupera le interazioni dell'utente con le offerte
        const { data: interactions, error: interactionsError } = await supabase
          .from('user_deal_interactions')
          .select('*')
          .eq('user_id', user.id);
       // console.log("interactions", interactions, "typeof interactions", typeof interactions);
        if (interactionsError) {
          console.error("Errore nel recupero delle interazioni:", interactionsError);
          return;
        }

        // 2. Recupera le prenotazioni passate dell'utente
        const { data: bookings, error: bookingsError } = await supabase
          .from('bookings')
          .select(`
            *,
            deals (
              *,
              businesses (
                *,
                categories (
                  id,
                  name
                )
              )
            )
          `)
          .eq('user_id', user.id);

        if (bookingsError) {
          console.error("Errore nel recupero delle prenotazioni:", bookingsError);
          return;
        }

        // 3. Calcola le preferenze dell'utente
        const userPreferences: Record<string, UserPreference> = {};
        
        // Analisi delle interazioni
        interactions?.forEach(interaction => {
          if (interaction.deal_id) {
            if (!userPreferences[interaction.deal_id]) {
              userPreferences[interaction.deal_id] = {
                categoryId: null,
                categoryName: null,
                businessId: null,
                businessName: null,
                priceRange: null,
                location: null,
                score: 0
              };
            }
            
            // Incrementa lo score in base al tipo di interazione
            if (interaction.interaction_type === 'view') {
              userPreferences[interaction.deal_id].score += 5;
            } else if (interaction.interaction_type === 'favorite') {
              userPreferences[interaction.deal_id].score += 20;
            } else if (interaction.interaction_type === 'share') {
              userPreferences[interaction.deal_id].score += 15;
            }
          }
        });
        
        // Analisi delle prenotazioni
        bookings?.forEach(booking => {
          const deal = booking.deals;
          const business = deal?.businesses;
          const category = business?.categories;
          
          if (deal && business) {
            const dealId = deal.id;
            
            if (!userPreferences[dealId]) {
              userPreferences[dealId] = {
                categoryId: category?.id || null,
                categoryName: category?.name || null,
                businessId: business.id,
                businessName: business.name,
                priceRange: {
                  min: deal.discounted_price * 0.8,
                  max: deal.discounted_price * 1.2
                },
                location: business.latitude && business.longitude ? {
                  latitude: business.latitude,
                  longitude: business.longitude,
                  address: business.address || ""
                } : null,
                score: 30 // Prenotazione = forte preferenza
              };
            } else {
              userPreferences[dealId].score += 30;
              userPreferences[dealId].categoryId = category?.id || null;
              userPreferences[dealId].categoryName = category?.name || null;
              userPreferences[dealId].businessId = business.id;
              userPreferences[dealId].businessName = business.name;
              
              if (!userPreferences[dealId].priceRange) {
                userPreferences[dealId].priceRange = {
                  min: deal.discounted_price * 0.8,
                  max: deal.discounted_price * 1.2
                };
              }
              
              if (business.latitude && business.longitude) {
                userPreferences[dealId].location = {
                  latitude: business.latitude,
                  longitude: business.longitude,
                  address: business.address || ""
                };
              }
            }
          }
        });
        
        // Converte l'oggetto in array e ordina per score
        const preferencesArray = Object.values(userPreferences)
          .filter(pref => pref.score > 0)
          .sort((a, b) => b.score - a.score);
        
        setPreferences(preferencesArray);
        
        // 4. Ottieni offerte consigliate basate sulle preferenze
        if (preferencesArray.length > 0) {
          await fetchRecommendedDeals(preferencesArray);
        }
        
      } catch (error) {
        console.error("Errore nell'analisi delle preferenze:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    const fetchRecommendedDeals = async (userPrefs: UserPreference[]) => {
      try {
        // Recupera le categorie preferite
        const favoriteCategories = userPrefs
          .filter(pref => pref.categoryId)
          .map(pref => pref.categoryId);
        
        // Recupera i business preferiti
        const favoriteBusinesses = userPrefs
          .filter(pref => pref.businessId)
          .map(pref => pref.businessId);
        
        // Recupera il range di prezzo medio
        const priceRanges = userPrefs
          .filter(pref => pref.priceRange)
          .map(pref => pref.priceRange!);
        
        let avgMinPrice = 0;
        let avgMaxPrice = 100;
        
        if (priceRanges.length > 0) {
          avgMinPrice = priceRanges.reduce((sum, range) => sum + range.min, 0) / priceRanges.length;
          avgMaxPrice = priceRanges.reduce((sum, range) => sum + range.max, 0) / priceRanges.length;
        }
        
        // Recupera offerte attive che corrispondono alle preferenze
        const { data: deals, error } = await supabase
          .from('deals')
          .select(`
            *,
            businesses (
              *,
              categories (
                id,
                name
              )
            )
          `)
          .eq('status', 'published')
          .gte('end_date', new Date().toISOString())
          .order('created_at', { ascending: false });
        
        if (error) {
          console.error("Errore nel recupero delle offerte consigliate:", error);
          return;
        }
        
        // Calcola uno score di corrispondenza per ogni offerta
        const scoredDeals = deals?.map(deal => {
          let matchScore = 0;
          
          // Punteggio per categoria
          if (deal.businesses?.categories?.id && favoriteCategories.includes(deal.businesses.categories.id)) {
            matchScore += 30;
          }
          
          // Punteggio per business
          if (favoriteBusinesses.includes(deal.business_id)) {
            matchScore += 25;
          }
          
          // Punteggio per prezzo
          if (deal.discounted_price >= avgMinPrice * 0.8 && deal.discounted_price <= avgMaxPrice * 1.2) {
            matchScore += 20;
          }
          
          // Punteggio per sconto elevato
          if (deal.discount_percentage >= 30) {
            matchScore += 15;
          }
          
          // Limita il punteggio massimo a 100
          matchScore = Math.min(matchScore, 100);
          
          return {
            id: deal.id,
            title: deal.title,
            discountedPrice: deal.discounted_price,
            originalPrice: deal.original_price,
            discountPercentage: deal.discount_percentage,
            businessName: deal.businesses?.name || "",
            images: deal.images || [],
            matchScore: matchScore,
            categoryName: deal.businesses?.categories?.name || null,
            endDate: deal.end_date
          } as RecommendedDeal;
        }) || [];
        
        // Filtra e ordina per punteggio di corrispondenza
        const recommendations = scoredDeals
          .filter(deal => deal.matchScore > 40) // Solo offerte con buona corrispondenza
          .sort((a, b) => b.matchScore - a.matchScore)
          .slice(0, 5); // Limita a 5 raccomandazioni
        
        setRecommendedDeals(recommendations);
      } catch (error) {
        console.error("Errore nel recupero delle offerte consigliate:", error);
      }
    };
    
    analyzeUserBehavior();
  }, [user]);

  return {
    preferences,
    recommendedDeals,
    isLoading
  };
};

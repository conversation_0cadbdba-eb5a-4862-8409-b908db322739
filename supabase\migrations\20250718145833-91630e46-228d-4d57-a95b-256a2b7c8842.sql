-- Check current RLS policies for user_location_history table
-- Add admin access to user_location_history table for full CRUD operations

-- Drop existing restrictive policies if any
DROP POLICY IF EXISTS "Users can only view their own location history" ON user_location_history;
DROP POLICY IF EXISTS "Users can only insert their own location history" ON user_location_history;

-- Enable RLS on the table
ALTER TABLE user_location_history ENABLE ROW LEVEL SECURITY;

-- Allow users to view their own location history
CREATE POLICY "Users can view their own location history" 
ON user_location_history 
FOR SELECT 
USING (auth.uid() = user_id);

-- Allow users to insert their own location history
CREATE POLICY "Users can insert their own location history" 
ON user_location_history 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Allow admins full access to all location data
CREATE POLICY "Admins have full access to location data" 
ON user_location_history 
FOR ALL 
USING (get_user_role(auth.uid()) = 'admin')
WITH CHECK (get_user_role(auth.uid()) = 'admin');
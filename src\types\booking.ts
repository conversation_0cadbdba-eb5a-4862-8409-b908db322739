
import type { Database } from "@/integrations/supabase/types";

export type QRData = {
  bookingId: string;
  dealTitle: string;
  businessName: string;
  date: string;
  time: string;
  status: string;
};

export type Booking = Database['public']['Tables']['bookings']['Row'] & {
  deals: {
    title: string;
    images: string[] | null;
    business_id: string;
    businesses: {
      name: string;
      address: string;
    } | null;
  } | null;
};

export type BookingWithDetails = Omit<Database['public']['Tables']['bookings']['Row'], 'qr_data'> & {
  deals?: {
    title: string;
    images: string[] | null;
    description: string | null;
    discounted_price: number;
    businesses?: {
      name: string;
      address: string | null;
    } | null;
  } | null;
  user_details?: {
    first_name: string | null;
    last_name: string | null;
    avatar_url: string | null;
  } | null;
  qr_data: QRData;
};

export const parseBookingFromDB = (bookingData: any): BookingWithDetails => {
  return {
    ...bookingData,
    qr_data: typeof bookingData.qr_data === 'string' 
      ? JSON.parse(bookingData.qr_data)
      : bookingData.qr_data
  };
};

export interface BookingDetails {
  date: string;
  day: number;
  start_time: string;
  end_time: string;
}

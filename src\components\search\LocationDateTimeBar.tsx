import React, { useState, useCallback, FormEvent } from "react";
import { MapPin, Calendar, Clock, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useMapsLibrary } from '@vis.gl/react-google-maps';
import { useAutocompleteSuggestions } from "@/hooks/useAutocompleteSuggestions";

interface LocationDateTimeBarProps {
  location?: string;
  date?: string;
  time?: string;
  onLocationChange?: (location: string, place?: google.maps.places.Place) => void;
}

export const LocationDateTimeBar: React.FC<LocationDateTimeBarProps> = ({
  location = "Posizione Attuale",
  date,
  time,
  onLocationChange,
}) => {
  const places = useMapsLibrary('places');
  const [isSearching, setIsSearching] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const { suggestions, resetSession } = useAutocompleteSuggestions(searchValue);

  const handleInput = useCallback((event: FormEvent<HTMLInputElement>) => {
    setSearchValue((event.target as HTMLInputElement).value);
  }, []);

  const handleSuggestionClick = useCallback(
    async (suggestion: google.maps.places.AutocompleteSuggestion) => {
      if (!places) return;
      if (!suggestion.placePrediction) return;

      const place = suggestion.placePrediction.toPlace();

      await place.fetchFields({
        fields: ['viewport', 'location', 'displayName', 'formattedAddress'],
      });

      const locationName = suggestion.placePrediction.text.text;
      console.log("Selezione location:", locationName);
      onLocationChange?.(locationName, place);
      setSearchValue('');
      setIsSearching(false);
      resetSession();
    },
    [places, onLocationChange, resetSession]
  );
  return (
    <div className="fixed top-20 left-4 right-4 z-50 bg-white rounded-full shadow-lg border border-gray-200">
      <div className="flex items-center divide-x divide-gray-200">
        {/* Location Section */}
        <div className="flex items-center gap-2 px-4 py-3 flex-1 min-w-0">
          <MapPin className="h-5 w-5 text-gray-400 flex-shrink-0" />
          {isSearching ? (
            <div className="relative flex-1">
              <div className="flex items-center gap-2">
                <Input
                  type="text"
                  placeholder="Cerca una posizione..."
                  value={searchValue}
                  onInput={handleInput}
                  className="h-8 text-sm border-none p-0 focus-visible:ring-0"
                  onKeyDown={(e) => {
                    if (e.key === 'Escape') {
                      setIsSearching(false);
                      setSearchValue("");
                    }
                  }}
                  autoFocus
                />
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={() => {
                    setIsSearching(false);
                    setSearchValue("");
                  }}
                >
                  <Search className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Suggestions dropdown */}
              {suggestions.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
                  {suggestions.map((suggestion, index) => (
                    <div
                      key={index}
                      className="px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm border-b border-gray-100 last:border-b-0"
                      onClick={() => handleSuggestionClick(suggestion)}
                    >
                      <div className="font-medium text-gray-900">
                        {suggestion.placePrediction?.text?.text}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <span 
              className="text-sm font-medium text-gray-900 truncate cursor-pointer hover:text-primary"
              onClick={() => setIsSearching(true)}
            >
              {location}
            </span>
          )}
        </div>

        {/* Date Section */}
        <div className="flex items-center gap-2 px-4 py-3">
          <Calendar className="h-5 w-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-900">
            {date || "--  --"}
          </span>
        </div>

        {/* Time Section */}
        <div className="flex items-center gap-2 px-4 py-3">
          <Clock className="h-5 w-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-900">
            {time || "--  --"}
          </span>
        </div>
      </div>
    </div>
  );
};
import { extractDatesFromTimeSlot } from '../services/availabilityService';

/**
 * Formats a date string to a user-friendly format
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    weekday: 'short',
    month: 'short', 
    day: 'numeric'
  }).format(date);
}


/**
 * Formats a time string (HH:MM) to a more readable format
 */
export function formatTime(timeString: string): string {
  // Se la stringa dell'orario è già formattata, restituiscila così com'è
  if (!timeString || timeString.includes(':')) {
    return timeString;
  }
  
  // Altrimenti, formatta l'orario nel formato HH:MM
  const hours = timeString.substring(0, 2);
  const minutes = timeString.substring(2, 4);
  return `${hours}:${minutes}`;
}


/**
 * Formats a date range from a time_slots field
 */
export function formatDateRange(timeSlots: any): string {
  const dates = extractDatesFromTimeSlot(timeSlots);
  
  if (dates.length === 0) {
    return 'No dates available';
  }
  
  if (dates.length === 1) {
    return formatDate(dates[0]);
  }
  
  // Sort dates chronologically
  dates.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
  
  const startDate = new Date(dates[0]);
  const endDate = new Date(dates[dates.length - 1]);
  
  // If dates are in the same month
  if (startDate.getMonth() === endDate.getMonth() && 
      startDate.getFullYear() === endDate.getFullYear()) {
    return `${startDate.getDate()} - ${endDate.getDate()} ${startDate.toLocaleString('default', { month: 'short' })}`;
  }
  
  // Different months
  return `${formatDate(dates[0])} - ${formatDate(dates[dates.length - 1])}`;
} 
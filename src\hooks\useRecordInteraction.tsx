
import { useAuth } from "@/hooks/auth/useAuth";
import { supabase } from "@/integrations/supabase/client";

type InteractionType = 'view' | 'favorite' | 'share' | 'booking';

// Interfaccia per il tipo di ritorno da Supabase
interface UserDealInteraction {
  id: string;
  user_id: string;
  deal_id: string;
  interaction_type: string;
  count?: number; // Rendiamo opzionale il campo count
  created_at: string;
  updated_at: string;
}

export const useRecordInteraction = () => {
  const { user } = useAuth();

  const recordInteraction = async (dealId: string, interactionType: InteractionType) => {
    if (!user) return;

    try {
      // Verifica se esiste già un'interazione dello stesso tipo
      const { data: existingInteraction, error: existingInteractionError } = await supabase
        .from('user_deal_interactions')
        .select('*')
        .eq('user_id', user.id)
        .eq('deal_id', dealId)
        .eq('interaction_type', interactionType)
        .maybeSingle();


if (existingInteractionError) {
  console.error('Errore nel recupero dell\'interazione esistente:', existingInteractionError);
  return;
}

      if (existingInteraction) {
        // Aggiorna il conteggio dell'interazione esistente
        await supabase
          .from('user_deal_interactions')
          .update({
            count: ((existingInteraction as UserDealInteraction).count || 0) + 1,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingInteraction.id);
      } else {
        // Crea una nuova interazione
        await supabase
          .from('user_deal_interactions')
          .insert({
            user_id: user.id,
            deal_id: dealId,
            interaction_type: interactionType,
            count: 1
          });
      }
    } catch (error) {
      console.error("Errore nella registrazione dell'interazione:", error);
    }
  };

  return {
    recordInteraction
  };
};

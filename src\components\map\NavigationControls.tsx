
import React from 'react';
import { cn } from "@/lib/utils";
import { Navigation2, ChevronRight, Car, PersonStanding, Bus, Bike } from "lucide-react";
import { Business, NavigationState, TransportMode } from './types';

interface NavigationControlsProps {
  selectedBusiness: Business | null;
  navigationState: NavigationState;
  showTransportMenu: boolean;
  setShowTransportMenu: (show: boolean) => void;
  calculateRoute: (business: Business, mode: TransportMode) => void;
  getTransportIcon: (mode: TransportMode) => JSX.Element;
  formatDuration: (seconds: number) => string;
}

export const NavigationControls = ({ 
  selectedBusiness, 
  navigationState, 
  showTransportMenu, 
  setShowTransportMenu,
  calculateRoute,
  getTransportIcon,
  formatDuration
}: NavigationControlsProps) => {
  if (!selectedBusiness) return null;

  return (
    <div className="fixed left-4 top-24 z-50">
      <div className="relative">
        <button
          onClick={() => {
            setShowTransportMenu(!showTransportMenu);
          }}
          className={cn(
            "fixed left-4 top-20 z-10 bg-brand-primary shadow-lg rounded-full px-5 py-2.5",
            navigationState.directions
              ? "hover:bg-brand-primary/90 flex items-center gap-2"
              : "hover:bg-brand-primary/50"
          )}
          title={
            navigationState.directions
              ? "Cambia mezzo di trasporto"
              : "Naviga verso l'attività"
          }
        >
          <Navigation2 className="h-6 w-6 text-white" />
          {navigationState.directions && (
            <span className="text-white text-sm font-medium flex items-center gap-2">
              {getTransportIcon(navigationState.mode)}
              {formatDuration(
                navigationState.directions.routes[0].legs[0].duration.value
              )}
            </span>
          )}
        </button>

        {showTransportMenu && (
          <div className="absolute top-full mb-2 left-0 bg-white rounded-lg shadow-lg p-2 min-w-[200px] z-50">
            <div className="flex flex-col gap-2">
              <div className="px-4 py-2 text-sm font-medium text-gray-600 border-b border-gray-100">
                {navigationState.directions
                  ? "Opzioni percorso"
                  : "Scegli il mezzo"}
              </div>
              {navigationState.directions ? (
                <>
                  <button
                    onClick={() => {
                      setShowTransportMenu(false);
                    }}
                    className="flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md"
                  >
                    <ChevronRight className="h-4 w-4" /> Mostra indicazioni
                  </button>
                  <div className="border-t border-gray-100 my-1"></div>
                </>
              ) : null}
              <button
                onClick={() => {
                  calculateRoute(selectedBusiness, "DRIVING");
                  setShowTransportMenu(false);
                }}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md",
                  navigationState.mode === "DRIVING" &&
                    "text-brand-primary font-medium"
                )}
              >
                <Car className="h-4 w-4" /> In auto
              </button>
              <button
                onClick={() => {
                  calculateRoute(selectedBusiness, "WALKING");
                  setShowTransportMenu(false);
                }}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md",
                  navigationState.mode === "WALKING" &&
                    "text-brand-primary font-medium"
                )}
              >
                <PersonStanding className="h-4 w-4" /> A piedi
              </button>
              <button
                onClick={() => {
                  calculateRoute(selectedBusiness, "TRANSIT");
                  setShowTransportMenu(false);
                }}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md",
                  navigationState.mode === "TRANSIT" &&
                    "text-brand-primary font-medium"
                )}
              >
                <Bus className="h-4 w-4" /> Mezzi pubblici
              </button>
              <button
                onClick={() => {
                  calculateRoute(selectedBusiness, "BICYCLING");
                  setShowTransportMenu(false);
                }}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md",
                  navigationState.mode === "BICYCLING" &&
                    "text-brand-primary font-medium"
                )}
              >
                <Bike className="h-4 w-4" /> In bicicletta
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};


import { Upload, Trash2 } from "lucide-react";
import type { Database } from "@/integrations/supabase/types";
import { useBusinessPhotos } from "@/hooks/useBusinessPhotos";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

type Business = Database['public']['Tables']['businesses']['Row'];

interface PhotosSectionProps {
  businessId: string;
  photos: string[] | null;
  onPhotosChange: (photos: string[]) => void;
  onSave: () => void;
}

export const PhotosSection = ({
  businessId,
  photos,
  onPhotosChange,
  onSave
}: PhotosSectionProps) => {
  const {
    photos: newPhotos,
    photoPreviews,
    isUploadingPhotos,
    handlePhotoChange,
    removePhoto,
    uploadPhotos
  } = useBusinessPhotos();

  const handleRemoveExistingPhoto = async (photoUrl: string, index: number) => {
    try {
      const path = photoUrl.split('/').pop();
      if (!path) return;

      const { error: storageError } = await supabase.storage
        .from('business-photos')
        .remove([path]);

      if (storageError) {
        console.error('Errore durante la rimozione dal bucket:', storageError);
        toast.error("Errore durante la rimozione della foto");
        return;
      }

      const updatedPhotos = [...(photos || [])];
      updatedPhotos.splice(index, 1);
      onPhotosChange(updatedPhotos);

 //     toast.success("Foto rimossa con successo");
    } catch (error) {
      console.error('Errore durante la rimozione della foto:', error);
      toast.error("Errore durante la rimozione della foto");
    }
  };

  const handleSavePhotos = async () => {
    try {
      if (newPhotos.length > 0) {
        console.log('Caricamento nuove foto...');
        const uploadedUrls = await uploadPhotos(businessId);
        console.log('URL foto caricate:', uploadedUrls);
        
        if (uploadedUrls.length > 0) {
          const currentPhotos = photos || [];
          console.log('Foto attuali:', currentPhotos);
          
          const updatedPhotos = [...currentPhotos, ...uploadedUrls];
          console.log('Array foto aggiornato:', updatedPhotos);
          
          onPhotosChange(updatedPhotos);
          
        //  toast.success("Foto caricate con successo");
        }
      }
    } catch (error) {
      console.error('Errore durante il salvataggio delle foto:', error);
      toast.error("Errore durante il salvataggio delle foto");
    }
  };

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Foto dell'attività
      </label>
      {/* Mostra le foto esistenti */}
      {photos && photos.length > 0 && (
        <div className="grid grid-cols-3 gap-2 mb-4">
          {photos.map((photo, index) => (
            <div key={index} className="relative group">
              <img
                src={photo}
                alt={`Foto ${index + 1}`}
                className="w-full h-24 object-cover rounded-lg"
              />
              <button
                onClick={() => handleRemoveExistingPhoto(photo, index)}
                className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
      )}
      
      {/* Mostra le nuove foto selezionate */}
      {photoPreviews.length > 0 && (
        <>
          <div className="grid grid-cols-3 gap-2 mb-4">
            {photoPreviews.map((preview, index) => (
              <div key={index} className="relative group">
                <img
                  src={preview}
                  alt={`Preview ${index + 1}`}
                  className="w-full h-24 object-cover rounded-lg"
                />
                <button
                  onClick={() => removePhoto(index)}
                  className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
          <button
            onClick={handleSavePhotos}
            className="w-full py-2 px-4 bg-brand-primary text-white rounded-lg mb-4"
            disabled={isUploadingPhotos}
          >
            {isUploadingPhotos ? "Caricamento..." : "Salva foto"}
          </button>
        </>
      )}
      
      {/* Pulsante di upload */}
      <div
        onClick={() => document.getElementById('photo-upload')?.click()}
        className="border-2 border-dashed border-gray-300 rounded-xl p-4 text-center cursor-pointer hover:border-brand-primary/50 transition-colors"
      >
        <input
          id="photo-upload"
          type="file"
          multiple
          accept="image/*"
          className="hidden"
          onChange={handlePhotoChange}
        />
        <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
        <p className="text-sm text-gray-500">
          Clicca per caricare nuove foto
        </p>
        <p className="text-xs text-gray-400 mt-1">
          Formato: JPG, PNG. Max 5MB per foto
        </p>
      </div>
    </div>
  );
};

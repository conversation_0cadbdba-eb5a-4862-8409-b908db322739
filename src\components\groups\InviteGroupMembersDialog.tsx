
import { useState } from 'react';
import { Search, UserPlus, X } from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useSearchUsers } from '@/hooks/useSearchUsers';
import { useInviteToGroup } from '@/hooks/group/useInviteToGroup';
import { toast } from 'sonner';

interface InviteGroupMembersDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  groupId: string;
  groupName: string;
}

export const InviteGroupMembersDialog = ({
  open,
  onOpenChange,
  groupId,
  groupName,
}: InviteGroupMembersDialogProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  const { data: searchResults, isLoading: isSearching } = useSearchUsers(searchQuery);
  const { mutate: inviteUsers, isPending: isInviting } = useInviteToGroup();

  const handleUserSelect = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSendInvites = () => {
    if (selectedUsers.length === 0) {
      toast.error('Seleziona almeno un utente da invitare');
      return;
    }

    inviteUsers({
      groupId,
      userIds: selectedUsers,
    }, {
      onSuccess: () => {
        toast.success(`Inviti inviati a ${selectedUsers.length} utenti`);
        setSelectedUsers([]);
        setSearchQuery('');
        onOpenChange(false);
      },
      onError: () => {
        toast.error('Errore nell\'invio degli inviti');
      }
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-xl z-50">
        <DialogHeader>
          <DialogTitle>Invita Membri</DialogTitle>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Invita utenti al gruppo "{groupName}"
          </p>
        </DialogHeader>

        <div className="space-y-4">
          {/* Barra di ricerca */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Cerca utenti per nome o email..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
            />
          </div>

          {/* Utenti selezionati */}
          {selectedUsers.length > 0 && (
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Utenti selezionati ({selectedUsers.length})
              </p>
              <div className="flex flex-wrap gap-2">
                {selectedUsers.map(userId => {
                  const user = searchResults?.find(u => u.id === userId);
                  return (
                    <div
                      key={userId}
                      className="flex items-center gap-1 bg-brand-light text-brand-primary px-2 py-1 rounded-full text-xs"
                    >
                      <span>{user?.first_name} {user?.last_name}</span>
                      <button
                        onClick={() => handleUserSelect(userId)}
                        className="hover:bg-brand-primary hover:text-white rounded-full p-0.5"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Lista risultati ricerca */}
          <div className="max-h-64 overflow-y-auto">
            {isSearching ? (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                Ricerca in corso...
              </div>
            ) : searchQuery && searchResults && searchResults.length === 0 ? (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                Nessun utente trovato
              </div>
            ) : searchResults && searchResults.length > 0 ? (
              <div className="space-y-2">
                {searchResults.map(user => (
                  <div
                    key={user.id}
                    className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors bg-white dark:bg-gray-700 ${
                      selectedUsers.includes(user.id)
                        ? 'border-brand-primary bg-brand-light dark:bg-brand-primary/20'
                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                    }`}
                    onClick={() => handleUserSelect(user.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.avatar_url || undefined} />
                        <AvatarFallback>
                          {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">
                          {user.first_name} {user.last_name}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{user.email}</p>
                      </div>
                    </div>
                    {selectedUsers.includes(user.id) && (
                      <div className="w-5 h-5 bg-brand-primary rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                Inizia a digitare per cercare utenti
              </div>
            )}
          </div>

          {/* Pulsanti azione */}
          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
            >
              Annulla
            </Button>
            <Button
              onClick={handleSendInvites}
              disabled={selectedUsers.length === 0 || isInviting}
              className="flex-1"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              {isInviting ? 'Invio...' : `Invita ${selectedUsers.length || ''}`}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

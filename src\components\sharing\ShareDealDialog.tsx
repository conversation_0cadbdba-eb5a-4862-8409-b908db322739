import { ShareDialog } from "./ShareDialog";
import { useShareDeal } from "@/hooks/sharing/useShareDeal";

interface ShareDealDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dealId: string;
  dealTitle: string;
}

export const ShareDealDialog = ({
  open,
  onOpenChange,
  dealId,
  dealTitle,
}: ShareDealDialogProps) => {
  const shareDeal = useShareDeal();

  const handleShare = (groupIds: string[], message?: string) => {
    shareDeal.mutate({
      dealId,
      groupIds,
      message,
    });
  };

  return (
    <ShareDialog
      open={open}
      onOpenChange={onOpenChange}
      title="Condividi Offerta"
      description={`Condividi "${dealTitle}" con i tuoi gruppi`}
      onShare={handleShare}
      isLoading={shareDeal.isPending}
    />
  );
};
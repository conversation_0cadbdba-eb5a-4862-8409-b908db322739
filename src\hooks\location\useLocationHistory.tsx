import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface LocationHistoryData {
  id: string;
  user_id: string;
  latitude: number;
  longitude: number;
  recorded_at: string;
  source: string;
  user_email?: string;
}

export const useLocationHistory = () => {
  const [locationData, setLocationData] = useState<LocationHistoryData[]>([]);
  const [users, setUsers] = useState<{id: string, email: string}[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch unique users with their emails
  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('user_location_history')
        .select('user_id');

      if (error) {
        console.error('Error fetching users:', error);
        return;
      }

      // Get unique user IDs
      if (data?.length > 0) {
        const uniqueUserIds = [...new Set(data.map(item => item.user_id))];
        
        // Get user details from user_details table
        const { data: userDetails, error: userError } = await supabase
          .from('user_details')
          .select('id, email')
          .in('id', uniqueUserIds);

        if (userError) {
          console.error('Error fetching user details:', userError);
          // Create fallback with masked emails
          const mockUsers = uniqueUserIds.map(id => ({
            id,
            email: `utente-${id.substring(0, 8)}`
          }));
          setUsers(mockUsers);
        } else {
          const usersWithEmails = userDetails?.map(user => ({
            id: user.id,
            email: user.email || `utente-${user.id.substring(0, 8)}`
          })) || [];
          setUsers(usersWithEmails);
        }
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  // Fetch location data with filters
  const fetchLocationData = async (selectedDate: string, selectedUser: string = 'all') => {
    setIsLoading(true);
    try {
      let query = supabase
        .from('user_location_history')
        .select('*')
        .gte('recorded_at', `${selectedDate}T00:00:00`)
        .lt('recorded_at', `${selectedDate}T23:59:59`)
        .order('recorded_at', { ascending: true });

      if (selectedUser !== 'all') {
        query = query.eq('user_id', selectedUser);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching location data:', error);
        return [];
      }

      // Enhance data with user emails
      const enhancedData = (data || []).map((location) => {
        const user = users.find(u => u.id === location.user_id);
        return {
          ...location,
          user_email: user?.email || `utente-${location.user_id.substring(0, 8)}`
        };
      });

      setLocationData(enhancedData);
      return enhancedData;
    } catch (error) {
      console.error('Error:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize users on mount
  useEffect(() => {
    fetchUsers();
  }, []);

  // Create new location record
  const createLocationRecord = async (record: Omit<LocationHistoryData, 'id' | 'user_email'>) => {
    try {
      const { data, error } = await supabase
        .from('user_location_history')
        .insert(record)
        .select()
        .single();

      if (error) {
        console.error('Error creating location record:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error:', error);
      return null;
    }
  };

  // Update location record
  const updateLocationRecord = async (id: string, updates: Partial<Omit<LocationHistoryData, 'id' | 'user_email'>>) => {
    try {
      const { data, error } = await supabase
        .from('user_location_history')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating location record:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error:', error);
      return null;
    }
  };

  // Delete location record
  const deleteLocationRecord = async (id: string) => {
    try {
      const { error } = await supabase
        .from('user_location_history')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting location record:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error:', error);
      return false;
    }
  };

  // Delete all records for a user
  const deleteUserLocationHistory = async (userId: string) => {
    try {
      const { error } = await supabase
        .from('user_location_history')
        .delete()
        .eq('user_id', userId);

      if (error) {
        console.error('Error deleting user location history:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error:', error);
      return false;
    }
  };

  return {
    locationData,
    users,
    isLoading,
    fetchLocationData,
    fetchUsers,
    createLocationRecord,
    updateLocationRecord,
    deleteLocationRecord,
    deleteUserLocationHistory
  };
};
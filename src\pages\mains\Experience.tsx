import { useState, useEffect } from "react";
import { Plus, MapPin, Clock, DollarSign, Calendar, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import ExperienceBuilder from "@/components/experience/ExperienceBuilder";
import ExperienceTemplates from "@/components/experience/ExperienceTemplates";
import SavedExperiences from "@/components/experience/SavedExperiences";
import { useAuth } from "@/hooks/auth/useAuth";
import ExperienceOptimizer from "@/components/experience/ExperienceOptimizer";

export interface ExperienceStop {
  id: string;
  dealId: string;
  businessId: string;
  dealTitle: string;
  businessName: string;
  address: string;
  latitude: number;
  longitude: number;
  startTime: string;
  endTime: string;
  estimatedDuration: number;
  price: number;
  category: string;
  travelTimeToNext?: number;
  order: number;
}

export interface Experience {
  id: string;
  name: string;
  description?: string;
  date: string;
  stops: ExperienceStop[];
  totalDuration: number;
  totalPrice: number;
  totalTravelTime: number;
  status: "draft" | "confirmed" | "completed";
  isTemplate?: boolean;
  userId?: string;
  createdAt: string;
  updatedAt: string;
}

const Experiences = () => {
  const { isAuthenticated, user } = useAuth();
  const [activeView, setActiveView] = useState<
    "builder" | "templates" | "saved"
  >("builder");
  const [currentExperience, setcurrentExperience] = useState<Experience | null>(
    null
  );
  const [isOptimizing, setIsOptimizing] = useState(false);

  // Sample journey data for demo
  const sampleExperience: Experience = {
    id: "sample-1",
    name: "Giornata Benessere Milano",
    description:
      "Una giornata dedicata al relax e al benessere nel centro di Milano",
    date: new Date().toISOString().split("T")[0],
    stops: [
      {
        id: "stop-1",
        dealId: "deal-spa-1",
        businessId: "business-spa-1",
        dealTitle: "Massaggio Rilassante 60min",
        businessName: "Spa Luxury Milano",
        address: "Via Montenapoleone 8, Milano",
        latitude: 45.4685,
        longitude: 9.1824,
        startTime: "10:00",
        endTime: "11:00",
        estimatedDuration: 60,
        price: 80,
        category: "Benessere",
        travelTimeToNext: 15,
        order: 1,
      },
      {
        id: "stop-2",
        dealId: "deal-lunch-1",
        businessId: "business-restaurant-1",
        dealTitle: "Menu Light Pranzo",
        businessName: "Bistrot Healthy",
        address: "Corso Buenos Aires 15, Milano",
        latitude: 45.4758,
        longitude: 9.2025,
        startTime: "12:30",
        endTime: "13:30",
        estimatedDuration: 60,
        price: 25,
        category: "Ristorazione",
        travelTimeToNext: 10,
        order: 2,
      },
      {
        id: "stop-3",
        dealId: "deal-yoga-1",
        businessId: "business-fitness-1",
        dealTitle: "Lezione Yoga Rilassante",
        businessName: "Studio Yoga Zen",
        address: "Via Brera 12, Milano",
        latitude: 45.4722,
        longitude: 9.1881,
        startTime: "15:00",
        endTime: "16:00",
        estimatedDuration: 60,
        price: 20,
        category: "Fitness",
        order: 3,
      },
    ],
    totalDuration: 180,
    totalPrice: 125,
    totalTravelTime: 25,
    status: "draft",
    userId: user?.id,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  useEffect(() => {
    if (isAuthenticated && !currentExperience) {
      setcurrentExperience(sampleExperience);
    }
  }, [isAuthenticated]);

  const handleCreateNewExperience = () => {
    const newExperience: Experience = {
      id: `experience-${Date.now()}`,
      name: "Nuova Esperienza",
      date: new Date().toISOString().split("T")[0],
      stops: [],
      totalDuration: 0,
      totalPrice: 0,
      totalTravelTime: 0,
      status: "draft",
      userId: user?.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setcurrentExperience(newExperience);
    setActiveView("builder");
  };

  const handleOptimizeExperience = async () => {
    if (!currentExperience) return;

    setIsOptimizing(true);
    try {
      // Simulate optimization process
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Here you would call the optimization service
      toast.success("Percorso ottimizzato con successo!");
    } catch (error) {
      toast.error("Errore durante l'ottimizzazione del percorso");
    } finally {
      setIsOptimizing(false);
    }
  };

  const handleSaveJourney = async () => {
    if (!currentExperience || !isAuthenticated) return;

    try {
      // Here you would save to database
      toast.success("Esperienza salvata con successo!");
    } catch (error) {
      toast.error("Errore durante il salvataggio");
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-8 text-center">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <MapPin className="w-8 h-8 text-primary" />
        </div>
        <h2 className="text-xl font-semibold mb-2">Pianifica la tua Espereinza</h2>
        <p className="text-muted-foreground mb-6 max-w-sm">
          Accedi per creare experience personalizzati con più servizi e
          ottimizzazioni intelligenti
        </p>
        <Button onClick={() => (window.location.href = "/login")}>
          Accedi per Iniziare
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header Section */}
      <div className="space-y-2">
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold">Pianificatore Esperienze</h1>
            <p className="text-muted-foreground">
              Crea esperienze perfette combinando più servizi e attività
            </p>
          </div>
          <Button
            onClick={handleCreateNewExperience}
            className="w-full sm:w-auto"
          >
            <Plus className="w-4 h-4 mr-2" />
            Nuova Esperienza
          </Button>
        </div>

        {/* Quick Stats */}
        {currentExperience && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4 text-primary" />
                  <div>
                    <p className="text-sm text-muted-foreground">Tappe</p>
                    <p className="text-lg font-semibold">
                      {currentExperience.stops.length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-primary" />
                  <div>
                    <p className="text-sm text-muted-foreground">Durata</p>
                    <p className="text-lg font-semibold">
                      {Math.round(currentExperience.totalDuration / 60)}h
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4 text-primary" />
                  <div>
                    <p className="text-sm text-muted-foreground">Costo</p>
                    <p className="text-lg font-semibold">
                      €{currentExperience.totalPrice}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={
                      currentExperience.status === "draft"
                        ? "secondary"
                        : currentExperience.status === "confirmed"
                        ? "default"
                        : "outline"
                    }
                  >
                    {currentExperience.status === "draft"
                      ? "Bozza"
                      : currentExperience.status === "confirmed"
                      ? "Confermato"
                      : "Completato"}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
        <Button
          variant={activeView === "builder" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveView("builder")}
        >
          Costruttore
        </Button>
        <Button
          variant={activeView === "templates" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveView("templates")}
        >
          Template
        </Button>
        <Button
          variant={activeView === "saved" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveView("saved")}
        >
          Salvati
        </Button>
      </div>

      {/* Content Area */}
      <div className="space-y-6">
        {activeView === "builder" && (
          <div className="space-y-4">
            {currentExperience && (
              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={handleOptimizeExperience}
                  disabled={isOptimizing || currentExperience.stops.length < 2}
                  variant="outline"
                >
                  {isOptimizing ? (
                    <>
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                      Ottimizzando...
                    </>
                  ) : (
                    <>
                      <MapPin className="w-4 h-4 mr-2" />
                      Ottimizza Percorso
                    </>
                  )}
                </Button>
                <Button onClick={handleSaveJourney} variant="outline">
                  Salva Esperienza
                </Button>
              </div>
            )}
            <ExperienceBuilder
              experience={currentExperience}
              onExperienceChange={setcurrentExperience}
            />
            {currentExperience && currentExperience.stops.length > 1 && (
              <ExperienceOptimizer
                experience={currentExperience}
                onOptimizedExperience={setcurrentExperience}
              />
            )}
          </div>
        )}

        {activeView === "templates" && (
          <ExperienceTemplates
            onSelectTemplate={(template) => {
              setcurrentExperience(template);
              setActiveView("builder");
            }}
          />
        )}

        {activeView === "saved" && (
          <SavedExperiences
            onSelectExperience={(experience) => {
              setcurrentExperience(experience);
              setActiveView("builder");
            }}
          />
        )}
      </div>
    </div>
  );
};

export default Experiences;

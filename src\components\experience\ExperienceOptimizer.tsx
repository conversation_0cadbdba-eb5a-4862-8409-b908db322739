import { useState } from "react";
import { MapPin, Clock, Route, DollarSign, Zap, AlertCircle } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import { Experience } from "@/pages/mains/Experience";


interface ExperienceOptimizerProps {
  experience: Experience;
  onOptimizedExperience: (experience: Experience) => void;
}

interface OptimizationSuggestion {
  type: 'route' | 'time' | 'cost' | 'efficiency';
  title: string;
  description: string;
  potential_saving: string;
  changes: OptimizationChange[];
}

interface OptimizationChange {
  stop_id: string;
  current_order: number;
  suggested_order: number;
  current_time: string;
  suggested_time: string;
  reason: string;
}

const ExperienceOptimizer = ({ experience, onOptimizedExperience }: ExperienceOptimizerProps) => {
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [suggestions, setSuggestions] = useState<OptimizationSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Mock optimization logic
  const generateOptimizationSuggestions = (): OptimizationSuggestion[] => {
    const mockSuggestions: OptimizationSuggestion[] = [];

    // Route optimization suggestion
    if (experience.stops.length >= 3) {
      mockSuggestions.push({
        type: 'route',
        title: 'Ottimizzazione Percorso',
        description: 'Riorganizzando le tappe potresti risparmiare tempo di viaggio',
        potential_saving: '25 minuti di viaggio',
        changes: [
          {
            stop_id: experience.stops[1]?.id || '',
            current_order: 2,
            suggested_order: 3,
            current_time: experience.stops[1]?.startTime || '',
            suggested_time: '15:00',
            reason: 'Riduce il tempo di viaggio complessivo'
          },
          {
            stop_id: experience.stops[2]?.id || '',
            current_order: 3,
            suggested_order: 2,
            current_time: experience.stops[2]?.startTime || '',
            suggested_time: '13:00',
            reason: 'Migliore posizione geografica'
          }
        ]
      });
    }

    // Time optimization suggestion
    if (experience.stops.length >= 2) {
      mockSuggestions.push({
        type: 'time',
        title: 'Ottimizzazione Orari',
        description: 'Alcuni orari potrebbero essere migliorati per evitare tempi morti',
        potential_saving: 'Eliminazione di 45min di attesa',
        changes: [
          {
            stop_id: experience.stops[0]?.id || '',
            current_order: 1,
            suggested_order: 1,
            current_time: experience.stops[0]?.startTime || '',
            suggested_time: '10:30',
            reason: 'Migliore connessione con la tappa successiva'
          }
        ]
      });
    }

    // Cost optimization suggestion
    mockSuggestions.push({
      type: 'cost',
      title: 'Opportunità di Risparmio',
      description: 'Trovate offerte alternative più convenienti per attività simili',
      potential_saving: 'Risparmio di €35',
      changes: [
        {
          stop_id: experience.stops[0]?.id || '',
          current_order: 1,
          suggested_order: 1,
          current_time: experience.stops[0]?.startTime || '',
          suggested_time: experience.stops[0]?.startTime || '',
          reason: 'Offerta alternativa con 30% di sconto'
        }
      ]
    });

    return mockSuggestions;
  };

  const handleOptimize = async () => {
    setIsOptimizing(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const optimizationSuggestions = generateOptimizationSuggestions();
      setSuggestions(optimizationSuggestions);
      setShowSuggestions(true);
      
      toast.success("Analisi di ottimizzazione completata!");
    } catch (error) {
      toast.error("Errore durante l'ottimizzazione");
    } finally {
      setIsOptimizing(false);
    }
  };

  const applyOptimization = (suggestion: OptimizationSuggestion) => {
    let optimizedStops = [...experience.stops];
    
    // Apply changes based on suggestion type
    suggestion.changes.forEach(change => {
      const stopIndex = optimizedStops.findIndex(stop => stop.id === change.stop_id);
      if (stopIndex !== -1) {
        if (suggestion.type === 'route') {
          // Reorder stops
          const stop = optimizedStops[stopIndex];
          optimizedStops.splice(stopIndex, 1);
          optimizedStops.splice(change.suggested_order - 1, 0, stop);
        } else if (suggestion.type === 'time') {
          // Update times
          optimizedStops[stopIndex] = {
            ...optimizedStops[stopIndex],
            startTime: change.suggested_time
          };
        }
      }
    });

    // Recalculate order numbers
    optimizedStops = optimizedStops.map((stop, index) => ({
      ...stop,
      order: index + 1
    }));

    const optimizedExperience: Experience = {
      ...experience,
      stops: optimizedStops,
      updatedAt: new Date().toISOString()
    };

    onOptimizedExperience(optimizedExperience);
    toast.success("Ottimizzazione applicata con successo!");
  };

  const getSuggestionIcon = (type: OptimizationSuggestion['type']) => {
    switch (type) {
      case 'route':
        return Route;
      case 'time':
        return Clock;
      case 'cost':
        return DollarSign;
      case 'efficiency':
        return Zap;
      default:
        return AlertCircle;
    }
  };

  const getSuggestionColor = (type: OptimizationSuggestion['type']) => {
    switch (type) {
      case 'route':
        return 'bg-blue-100 text-blue-800';
      case 'time':
        return 'bg-green-100 text-green-800';
      case 'cost':
        return 'bg-orange-100 text-orange-800';
      case 'efficiency':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Zap className="w-5 h-5" />
          <span>Ottimizzatore Intelligente</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-muted-foreground">
          L'AI può analizzare il tuo programma e suggerire ottimizzazioni per tempo, costi e percorsi.
        </div>

        {!showSuggestions ? (
          <div className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Clicca su "Analizza Programma" per ricevere suggerimenti personalizzati di ottimizzazione.
              </AlertDescription>
            </Alert>
            
            <Button 
              onClick={handleOptimize}
              disabled={isOptimizing || experience.stops.length < 2}
              className="w-full"
            >
              {isOptimizing ? (
                <>
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                  Analizzando...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  Analizza Programma
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="font-semibold">Suggerimenti di Ottimizzazione</h3>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowSuggestions(false)}
              >
                Nuova Analisi
              </Button>
            </div>

            {suggestions.length === 0 ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  La tua espereinza è già ottimizzato! Non sono stati trovati miglioramenti significativi.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-4">
                {suggestions.map((suggestion, index) => {
                  const Icon = getSuggestionIcon(suggestion.type);
                  return (
                    <Card key={index} className="border-l-4 border-l-primary">
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center space-x-2">
                              <Icon className="w-5 h-5 text-primary" />
                              <h4 className="font-semibold">{suggestion.title}</h4>
                              <Badge className={getSuggestionColor(suggestion.type)}>
                                {suggestion.potential_saving}
                              </Badge>
                            </div>
                          </div>
                          
                          <p className="text-sm text-muted-foreground">
                            {suggestion.description}
                          </p>
                          
                          <div className="space-y-2">
                            <h5 className="text-sm font-medium">Modifiche Proposte:</h5>
                            {suggestion.changes.map((change, changeIndex) => {
                              const stop = experience.stops.find(s => s.id === change.stop_id);
                              return (
                                <div key={changeIndex} className="text-xs bg-muted p-2 rounded">
                                  <div className="font-medium">{stop?.dealTitle}</div>
                                  <div className="text-muted-foreground">
                                    {suggestion.type === 'route' 
                                      ? `Posizione: ${change.current_order} → ${change.suggested_order}`
                                      : `Orario: ${change.current_time} → ${change.suggested_time}`
                                    }
                                  </div>
                                  <div className="text-muted-foreground italic">
                                    {change.reason}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                          
                          <Button 
                            size="sm" 
                            onClick={() => applyOptimization(suggestion)}
                            className="w-full"
                          >
                            Applica Ottimizzazione
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ExperienceOptimizer;
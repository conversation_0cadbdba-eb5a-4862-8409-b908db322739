import { motion } from "framer-motion";
import { ChevronRight, Calendar, Clock, MapPin, ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";
import type { Booking } from "@/types/booking";
import BookingStatus from "@/components/booking/BookingStatus";
import { ReviewBookingButton } from "./ReviewBookingButton";
import { Badge } from "@/components/ui/badge";
import { ShareButton } from "@/components/sharing/ShareButton";

interface BookingListItemProps {
  booking: Booking;
  onShareClick?: (booking: Booking) => void;
}

const BookingListItem = ({ booking, onShareClick }: BookingListItemProps) => {
  const navigate = useNavigate();
  
  // Get status-based accent color
  const getStatusAccent = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'border-l-emerald-500 bg-emerald-50/50 dark:bg-emerald-950/20';
      case 'pending':
        return 'border-l-amber-500 bg-amber-50/50 dark:bg-amber-950/20';
      case 'cancelled':
        return 'border-l-red-500 bg-red-50/50 dark:bg-red-950/20';
      case 'completed':
        return 'border-l-blue-500 bg-blue-50/50 dark:bg-blue-950/20';
      default:
        return 'border-l-muted bg-muted/20';
    }
  };

  // Format booking date with more context
  const formatBookingDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) {
      return "Oggi";
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return "Domani";
    }
    
    // Check if it's this week
    const weekFromNow = new Date(today);
    weekFromNow.setDate(today.getDate() + 7);
    
    if (date < weekFromNow) {
      return date.toLocaleDateString('it-IT', {
        weekday: 'long',
        day: 'numeric',
        month: 'short'
      });
    }
    
    return date.toLocaleDateString('it-IT', {
      day: 'numeric',
      month: 'long'
    });
  };

  const businessName = booking.deals?.businesses?.name || 'Attività';
  const dealTitle = booking.deals?.title || 'Servizio';
  const bookingDate = formatBookingDate(booking.booking_date);
  const bookingTime = booking.booking_time;
  const [hours, minutes] = bookingTime.split(':');
  const startTime = `${hours}:${minutes}`;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -2, scale: 1.01 }}
      whileTap={{ scale: 0.98 }}
      className={`group bg-card/80 backdrop-blur-sm rounded-2xl border-l-4 ${getStatusAccent(booking.status)} hover:shadow-lg hover:shadow-primary/5 transition-all duration-300 cursor-pointer overflow-hidden`}
      onClick={() => navigate(`/prenotazione/${booking.id}`)}
    >
      <div className="p-4">
        <div className="flex items-start gap-4">
          {/* Image with overlay */}
          <div className="relative w-20 h-20 rounded-xl overflow-hidden bg-muted shrink-0">
            <img 
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" 
              src={booking.deals?.images?.[0] || `https://picsum.photos/800/600?random=${booking.id}`}
              alt={dealTitle}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          </div>
          
          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-foreground truncate">{businessName}</h3>
                <p className="text-sm text-muted-foreground truncate">{dealTitle}</p>
              </div>
              <BookingStatus status={booking.status} />
            </div>
            
            {/* Date and Time */}
            <div className="flex items-center gap-4 mb-3">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="w-4 h-4" />
                <span className="font-medium">{bookingDate}</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="w-4 h-4" />
                <span>{startTime}</span>
              </div>
            </div>

            {/* Price and Action */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs font-medium">
                  €{booking.discounted_price}
                </Badge>
                {booking.original_price > booking.discounted_price && (
                  <span className="text-xs text-muted-foreground line-through">
                    €{booking.original_price}
                  </span>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                {onShareClick && (
                  <div onClick={(e) => e.stopPropagation()}>
                    <ShareButton
                      onClick={() => onShareClick(booking)}
                      size="icon"
                      variant="ghost"
                      className="h-8 w-8"
                    />
                  </div>
                )}
                {booking.status === 'completed' && (
                  <div onClick={(e) => e.stopPropagation()}>
                    <ReviewBookingButton booking={booking} />
                  </div>
                )}
                <ArrowRight className="w-5 h-5 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all duration-200" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default BookingListItem;
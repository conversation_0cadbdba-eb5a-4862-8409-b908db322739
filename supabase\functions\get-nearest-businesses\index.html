
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>get-nearest-businesses Edge Function</title>
  <style>
    body { font-family: system-ui, sans-serif; padding: 2rem; line-height: 1.5; }
    h1 { color: #333; margin-bottom: 1rem; }
    h2 { color: #555; margin-top: 2rem; margin-bottom: 0.5rem; }
    pre { background-color: #f4f4f4; padding: 1rem; border-radius: 0.5rem; overflow: auto; }
    code { font-family: monospace; }
    p { margin: 0.5rem 0; }
    .example { margin: 1rem 0; }
    .note { background-color: #fffde7; padding: 1rem; border-left: 4px solid #ffd600; margin: 1rem 0; }
    .warning { background-color: #ffebee; padding: 1rem; border-left: 4px solid #f44336; margin: 1rem 0; }
  </style>
</head>
<body>
  <h1>Edge Function: get-nearest-businesses</h1>
  <p>Questa edge function calcola i business più vicini all'utente utilizzando PostGIS per un calcolo veloce delle distanze geografiche e opzionalmente l'API Google Maps Directions per percorsi reali.</p>
  
  <div class="note">
    <strong>Nota:</strong> Per sistemi ad alta scalabilità, questa funzione implementa una cache in-memory, indici spaziali PostGIS per query geografiche ottimizzate e calcolo opzionale di percorsi reali.
  </div>

  <div class="warning">
    <strong>Importante:</strong> Il componente NearestBusinesses include una modalità di fallback che si attiva automaticamente se questa edge function non è disponibile. In modalità fallback, i calcoli di distanza sono meno precisi ma garantiscono comunque una user experience funzionale.
  </div>
  
  <h2>Parametri richiesti</h2>
  <pre><code>{
  "latitude": 45.4642, // Latitudine dell'utente
  "longitude": 9.1900, // Longitudine dell'utente
  "transportMode": "DRIVING", // Opzionale, valori: "DRIVING", "WALKING", "BICYCLING", "TRANSIT"
  "limit": 5, // Opzionale, numero massimo di risultati da restituire
  "useDirections": false // Opzionale, se true utilizza Google Maps API per calcoli precisi
}</code></pre>
  
  <h2>Esempio di risposta</h2>
  <pre><code>{
  "businesses": [
    {
      "id": "7f9e24c5-1ac4-4f23-8c38-5a33d9408a9c",
      "name": "Ristorante Da Mario",
      "latitude": 45.4645,
      "longitude": 9.1880,
      "deal_count": 2,
      "address": "Via Dante 15, Milano",
      "distance": 450, // Distanza in metri
      "distanceText": "450 m",
      "duration": 360, // Durata in secondi (solo se useDirections=true)
      "durationText": "6 min" // (solo se useDirections=true)
    },
    // altri business...
  ],
  "nearestBusiness": {
    // Il business più vicino (primo elemento dell'array)
  }
}</code></pre>

  <h2>Prestazioni e scalabilità</h2>
  <p>Questa funzione è ottimizzata per:
    <ul>
      <li>Cache dei risultati per 10 minuti (configurabile)</li>
      <li>Utilizzo di query geografiche native PostgreSQL (PostGIS)</li>
      <li>Indici spaziali per ricerche efficienti anche con 30.000+ business</li>
      <li>Ottimizzazione delle chiamate all'API Google Maps</li>
      <li>Gestione di fallback in caso di errori</li>
    </ul>
  </p>
</body>
</html>

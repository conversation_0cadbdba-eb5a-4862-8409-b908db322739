
import { useMemo } from 'react';

interface Notification {
  id: string;
  entity: string;
  entity_id: string;
  created_at: string;
}

interface Conversation {
  id: string;
  booking_id?: string;
}

export const useNotificationDeduplication = (
  notifications: Notification[], 
  bookingConversations: Conversation[]
) => {
  return useMemo(() => {
    // Crea un Set degli ID delle conversazioni di booking esistenti
    const bookingConversationIds = new Set(
      bookingConversations
        .filter(conv => conv.booking_id)
        .map(conv => conv.id)
    );

    // Filtra le notifiche per evitare duplicati
    const filteredNotifications = notifications.filter(notification => {
      // Se è una notifica di booking, controlla se esiste già una conversazione
      if (notification.entity === 'bookings') {
        return !bookingConversationIds.has(notification.entity_id);
      }
      
      // Per altri tipi di notifiche (come group_invites), mantienile
      return true;
    });

    // Separa le notifiche per tipo
    const groupInviteNotifications = filteredNotifications.filter(
      notification => notification.entity === 'group_invites'
    );
    
    const bookingNotifications = filteredNotifications.filter(
      notification => notification.entity === 'bookings'
    );
    
    const otherNotifications = filteredNotifications.filter(
      notification => !['group_invites', 'bookings'].includes(notification.entity)
    );

    return {
      filteredNotifications,
      groupInviteNotifications,
      bookingNotifications,
      otherNotifications,
      totalCount: filteredNotifications.length + bookingConversations.length
    };
  }, [notifications, bookingConversations]);
};

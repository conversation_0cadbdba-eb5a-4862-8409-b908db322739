
import type { Booking } from "@/types/booking";
import BookingListItem from "./BookingListItem";
import { motion } from "framer-motion";
import { Calendar, Coffee, Sparkles } from "lucide-react";

interface BookingsListProps {
  bookings: Booking[];
  onShareClick?: (booking: Booking) => void;
}

const BookingsList = ({ bookings, onShareClick }: BookingsListProps) => {
  if (bookings.length === 0) {
    return (
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-16"
      >
        <div className="w-24 h-24 mx-auto mb-6 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full animate-pulse" />
          <div className="absolute inset-2 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
            <Calendar className="w-8 h-8 text-primary-foreground" />
          </div>
        </div>
        
        <h3 className="text-lg font-semibold text-foreground mb-2">
          Nessuna prenotazione
        </h3>
        <p className="text-muted-foreground max-w-sm mx-auto">
          Quando effettuerai delle prenotazioni, le vedrai qui. Inizia esplorando le offerte disponibili!
        </p>
        
        <div className="flex items-center justify-center gap-2 mt-6 text-sm text-muted-foreground">
          <Sparkles className="w-4 h-4" />
          <span>Scopri nuove esperienze</span>
          <Coffee className="w-4 h-4" />
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="space-y-3"
    >
      {bookings.map((booking, index) => (
        <motion.div
          key={booking.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <BookingListItem booking={booking} onShareClick={onShareClick} />
        </motion.div>
      ))}
    </motion.div>
  );
};

export default BookingsList;

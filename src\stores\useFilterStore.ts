import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { NEARBY_DEFAULT_RADIUS_IN_KM } from '@/data/userSettings';

// Filter state interface
export interface FilterSettings {
  searchQuery: string;
  selectedCategoryIds: string[];
  withDeals: boolean;
  withBookings: boolean;
  radius: number;
  sortBy: 'relevance' | 'distance' | 'rating' | 'price';
  applicationAccess: boolean; // Track if user has accessed app (for preferences loading)
}

// Store state and actions interface
interface FilterStoreState {
  filters: FilterSettings;
  updateFilters: (filters: Partial<FilterSettings>) => void;
  resetFilters: () => void;
  setSearchQuery: (query: string) => void;
  setSelectedCategories: (categoryIds: string[]) => void;
  addCategory: (categoryId: string) => void;
  removeCategory: (categoryId: string) => void;
  toggleCategory: (categoryId: string) => void;
  setWithDeals: (withDeals: boolean) => void;
  setWithBookings: (withBookings: boolean) => void;
  setRadius: (radius: number) => void;
  setSortBy: (sortBy: FilterSettings['sortBy']) => void;
  setApplicationAccess: (accessed: boolean) => void;
}

// Default filter values
const defaultFilters: FilterSettings = {
  searchQuery: '',
  selectedCategoryIds: [],
  withDeals: false,
  withBookings: false,
  radius: NEARBY_DEFAULT_RADIUS_IN_KM,
  sortBy: 'relevance',
  applicationAccess: false, // User hasn't accessed app yet
};

// Create the Zustand store with persistence
export const useFilterStore = create<FilterStoreState>()(
  persist(
    (set) => ({
      filters: defaultFilters,
      
      // Update multiple filters at once
      updateFilters: (newFilters) =>
        set((state) => ({
          filters: { ...state.filters, ...newFilters },
        })),
      
      // Reset all filters to default values
      resetFilters: () =>
        set({ filters: defaultFilters }),
      
      // Individual filter setters for convenience
      setSearchQuery: (query) =>
        set((state) => ({
          filters: { ...state.filters, searchQuery: query },
        })),
      
      setSelectedCategories: (categoryIds) =>
        set((state) => ({
          filters: { ...state.filters, selectedCategoryIds: categoryIds },
        })),
      
      addCategory: (categoryId) =>
        set((state) => ({
          filters: { 
            ...state.filters, 
            selectedCategoryIds: [...state.filters.selectedCategoryIds, categoryId] 
          },
        })),
      
      removeCategory: (categoryId) =>
        set((state) => ({
          filters: { 
            ...state.filters, 
            selectedCategoryIds: state.filters.selectedCategoryIds.filter(id => id !== categoryId)
          },
        })),
      
      toggleCategory: (categoryId) =>
        set((state) => {
          const currentCategories = state.filters.selectedCategoryIds;
          const isSelected = currentCategories.includes(categoryId);
          
          return {
            filters: {
              ...state.filters,
              selectedCategoryIds: isSelected
                ? currentCategories.filter(id => id !== categoryId)
                : [...currentCategories, categoryId]
            }
          };
        }),
      
      setWithDeals: (withDeals) =>
        set((state) => ({
          filters: { ...state.filters, withDeals },
        })),
      
      setWithBookings: (withBookings) =>
        set((state) => ({
          filters: { ...state.filters, withBookings },
        })),
      
      setRadius: (radius) =>
        set((state) => ({
          filters: { ...state.filters, radius },
        })),
      
      setSortBy: (sortBy) =>
        set((state) => ({
          filters: { ...state.filters, sortBy },
        })),
      
      setApplicationAccess: (accessed) =>
        set((state) => ({
          filters: { ...state.filters, applicationAccess: accessed },
        })),
    }),
    {
      name: 'catchup-filter-store',
      migrate: (persistedState: any, version: number) => {
        // Handle migration from old selectedCategoryId to selectedCategoryIds
        if (persistedState && persistedState.filters) {
          if (persistedState.filters.selectedCategoryId !== undefined && 
              persistedState.filters.selectedCategoryIds === undefined) {
            persistedState.filters.selectedCategoryIds = 
              persistedState.filters.selectedCategoryId ? [persistedState.filters.selectedCategoryId] : [];
            delete persistedState.filters.selectedCategoryId;
          }
          // Ensure selectedCategoryIds is always an array
          if (!Array.isArray(persistedState.filters.selectedCategoryIds)) {
            persistedState.filters.selectedCategoryIds = [];
          }
        }
        return persistedState;
      },
    }
  )
);

// Convenience hooks for easier usage
export const useFilters = () => {
  const store = useFilterStore();
  const filters = store.filters;
  
  // Ensure selectedCategoryIds is always an array
  return {
    ...filters,
    selectedCategoryIds: Array.isArray(filters.selectedCategoryIds) ? filters.selectedCategoryIds : []
  };
};

export const useFilterActions = () => {
  const store = useFilterStore();
  return {
    updateFilters: store.updateFilters,
    resetFilters: store.resetFilters,
    setSearchQuery: store.setSearchQuery,
    setSelectedCategories: store.setSelectedCategories,
    addCategory: store.addCategory,
    removeCategory: store.removeCategory,
    toggleCategory: store.toggleCategory,
    setWithDeals: store.setWithDeals,
    setWithBookings: store.setWithBookings,
    setRadius: store.setRadius,
    setSortBy: store.setSortBy,
    setApplicationAccess: store.setApplicationAccess,
  };
};

// Helper hook to check if filters are active
export const useHasActiveFilters = () => {
  const filters = useFilters();
  return (
    filters.searchQuery !== '' ||
    (filters.selectedCategoryIds && filters.selectedCategoryIds.length > 0) ||
    filters.withDeals ||
    filters.withBookings ||
    filters.radius !== NEARBY_DEFAULT_RADIUS_IN_KM ||
    filters.sortBy !== 'relevance'
  );
};

// Helper hook to count active filters
export const useActiveFiltersCount = () => {
  const filters = useFilters();
  let count = 0;
  if (filters.searchQuery) count++;
  if (filters.selectedCategoryIds && filters.selectedCategoryIds.length > 0) count++;
  if (filters.withDeals) count++;
  if (filters.withBookings) count++;
  if (filters.radius !== NEARBY_DEFAULT_RADIUS_IN_KM) count++;
  if (filters.sortBy !== 'relevance') count++;
  return count;
}; 
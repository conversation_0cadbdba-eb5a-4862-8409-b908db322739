import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { BookingWithDetails } from "@/types/booking";
import { parseBookingFromDB } from "@/types/booking";

export const useBusinessBookings = (businessId: string | undefined) => {
  const [bookings, setBookings] = useState<BookingWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedBookingId, setSelectedBookingId] = useState<string | null>(null);
  const [isCancelling, setIsCancelling] = useState(false);

  // Old version
  /*
  const fetchBookings = async () => {
    if (!businessId) return;
    try {
      const { data: deals } = await supabase
        .from('deals')
        .select('id')
        .eq('business_id', businessId);

      if (!deals) return;

      const dealIds = deals.map(deal => deal.id);

      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          *,
          deals (
            title,
            images,
            description,
            discounted_price,
            businesses (
              name,
              address
            )
          )
        `)
        .in('deal_id', dealIds)
        .order('booking_date', { ascending: true });

      if (bookingsError) throw bookingsError;

      if (bookingsData) {
        const userIds = bookingsData.map(booking => booking.user_id);
        const { data: usersData } = await supabase
          .from('user_details')
          .select('id, first_name, last_name, avatar_url')
          .in('id', userIds);

        const usersMap = (usersData || []).reduce((acc, user) => {
          acc[user.id] = user;
          return acc;
        }, {} as Record<string, any>);

        const bookingsWithUsers = bookingsData.map(booking => parseBookingFromDB({
          ...booking,
          user_details: usersMap[booking.user_id] || null
        }));

        setBookings(bookingsWithUsers);
      }
    } catch (error) {
      console.error('Error fetching bookings:', error);
      toast.error("Errore nel caricamento delle prenotazioni");
    } finally {
      setIsLoading(false);
    }
  };
  */

  // New improved version
  const fetchBookings = async () => {
    if (!businessId) return;
    try {
      // Get all deals for this business
      const { data: deals, error: dealsError } = await supabase
        .from('deals')
        .select('id')
        .eq('business_id', businessId);

      if (dealsError) throw dealsError;
      if (!deals || deals.length === 0) {
        setBookings([]);
        return;
      }

      const dealIds = deals.map(deal => deal.id);

      // Get bookings for these deals
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          *,
          deals (
            title,
            images,
            description,
            discounted_price,
            businesses (
              name,
              address
            )
          )
        `)
        .in('deal_id', dealIds)
        .order('booking_date', { ascending: true });

      if (bookingsError) throw bookingsError;

      if (!bookingsData || bookingsData.length === 0) {
        setBookings([]);
        return;
      }

      // Get user details only if we have bookings
      const userIds = bookingsData.map(booking => booking.user_id);
      const { data: usersData, error: usersError } = await supabase
        .from('user_details')
        .select('id, first_name, last_name, avatar_url')
        .in('id', userIds);

      if (usersError) throw usersError;

      // Create users map for efficient lookup
      const usersMap = (usersData || []).reduce((acc, user) => {
        acc[user.id] = user;
        return acc;
      }, {} as Record<string, any>);

      // Combine booking data with user details
      const bookingsWithUsers = bookingsData.map(booking => parseBookingFromDB({
        ...booking,
        user_details: usersMap[booking.user_id] || null
      }));

      setBookings(bookingsWithUsers);
    } catch (error) {
      console.error('Error fetching bookings:', error);
      toast.error("Errore nel caricamento delle prenotazioni");
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async (bookingId: string) => {
    try {
      const { error } = await supabase
        .from('bookings')
        .update({ status: 'confirmed' })
        .eq('id', bookingId);

      if (error) throw error;
      toast.success("Prenotazione approvata con successo");
      fetchBookings();
    } catch (error) {
      console.error('Error approving booking:', error);
      toast.error("Errore nell'approvazione della prenotazione");
    }
  };

  const handleCancel = async (bookingId: string, note: string) => {
    if (!note) {
      toast.error("Inserisci una nota per la cancellazione");
      return;
    }
    setIsCancelling(true);
    try {
      const { error } = await supabase
        .from('bookings')
        .update({
          status: 'cancelled',
          cancellation_note: note
        })
        .eq('id', bookingId);

      if (error) throw error;
      toast.success("Prenotazione cancellata con successo");
      setSelectedBookingId(null);
      fetchBookings();
    } catch (error) {
      console.error('Error cancelling booking:', error);
      toast.error("Errore nella cancellazione della prenotazione");
    } finally {
      setIsCancelling(false);
    }
  };

  useEffect(() => {
    fetchBookings();
  }, [businessId]);

  return {
    bookings,
    isLoading,
    selectedBookingId,
    setSelectedBookingId,
    isCancelling,
    handleApprove,
    handleCancel
  };
};

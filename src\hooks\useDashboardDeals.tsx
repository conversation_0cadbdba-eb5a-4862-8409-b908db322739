import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAuth } from "@/hooks/auth/useAuth";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";
import { Deal } from "@/types/deals";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { NEARBY_DEFAULT_RADIUS_IN_METERS } from "@/data/userSettings";
import type { Database } from "@/integrations/supabase/types";
import { useNearbyDealsSearch } from "@/hooks/search/useNearbyDealsSearch";
import { mapNearbyDealsToDealFormat } from "@/utils/dealMappers";
import { useFeaturedDealsQuery } from "@/queries/useDealsQuery";

// Helper function to calculate current time as percentage of the day
const getCurrentTimePercentage = (): number => {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const totalMinutesSinceMidnight = hours * 60 + minutes;
  const totalMinutesInDay = 24 * 60;
  return (totalMinutesSinceMidnight / totalMinutesInDay) * 100;
};

export const useDashboardDeals = () => {
  const { user } = useAuth();
  const { userLocation } = useLocationManagement();

  // Time and date selection state
  const [selectedDate, setSelectedDate] = useState<string>(
    format(new Date(), "yyyy-MM-dd")
  );
  const [timeValue, setTimeValue] = useState<number[]>([
    getCurrentTimePercentage(),
  ]);
  const [searchParamsUpdated, setSearchParamsUpdated] =
    useState<boolean>(false);
  const [nearByRadius, setNearByRadius] = useState<number>(
    NEARBY_DEFAULT_RADIUS_IN_METERS
  );

  // Deals loading states
  const [nearbyDeals, setNearbyDeals] = useState<Deal[]>([]);
  const [recentlyViewedDeals, setRecentlyViewedDeals] = useState<Deal[]>([]);
  const [favoriteDeals, setFavoriteDeals] = useState<Deal[]>([]);
  //const [featuredDeals, setFeaturedDeals] = useState<Deal[]>([]);
  const [loadingRecentlyViewed, setLoadingRecentlyViewed] =
    useState<boolean>(true);
  const [loadingFavorites, setLoadingFavorites] = useState<boolean>(true);
  const [loadingFeatured, setLoadingFeatured] = useState<boolean>(true);
  const [businessCount, setBusinessCount] = useState<number>(0);

  // Featured deals
  const { data: featuredDeals, isLoading: isLoadingFeatured } =
    useFeaturedDealsQuery();

  const getTimeFromPercentage = useCallback((percentage: number): string => {
    const totalMinutes = (percentage / 100) * 24 * 60;
    const hours = Math.floor(totalMinutes / 60);
    const minutes = Math.floor(totalMinutes % 60);
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}`;
  }, []);

  const resetToNow = useCallback(() => {
    setSelectedDate(format(new Date(), "yyyy-MM-dd"));
    setTimeValue([getCurrentTimePercentage()]);
    setSearchParamsUpdated(true);
  }, []);

  // Utilizziamo il nuovo hook per la ricerca delle offerte vicine
  const {
    results: nearbyDealsResults,
    isLoading,
    updateSearchParams,
    metrics,
  } = useNearbyDealsSearch({
    date: selectedDate,
    time: getTimeFromPercentage(timeValue[0]),
    location: userLocation
      ? { lat: userLocation.lat, lng: userLocation.lng, radius: nearByRadius }
      : undefined,
  });

  useEffect(() => {
    if (searchParamsUpdated) {
      const timeString = getTimeFromPercentage(timeValue[0]);
      updateSearchParams({
        date: selectedDate,
        time: timeString,
        location: userLocation
          ? {
              lat: userLocation.lat,
              lng: userLocation.lng,
              radius: nearByRadius,
            }
          : undefined,
      });
      setSearchParamsUpdated(false);
    }
  }, [
    searchParamsUpdated,
    timeValue,
    selectedDate,
    getTimeFromPercentage,
    updateSearchParams,
  ]);

  useEffect(() => {
    setSearchParamsUpdated(true);
  }, [timeValue, selectedDate, nearByRadius]);

  // Aggiorniamo il conteggio delle attività disponibili
  useEffect(() => {
    if (nearbyDealsResults) {
      setBusinessCount(nearbyDealsResults.length);
    }
  }, [nearbyDealsResults]);

  // Trasformiamo i risultati nel formato accettato da DealCarousel
  useEffect(() => {
    if (nearbyDealsResults) {
      const transformedDeals = mapNearbyDealsToDealFormat(nearbyDealsResults);
      setNearbyDeals(transformedDeals);
    } else {
      setNearbyDeals([]);
    }
  }, [nearbyDealsResults]);

  // TODO Replace with useQuery (recentlyViewedDealsQuery, favoriteDealsQuery) but there is a problem with user.id as os not in sync wtih timing
  useEffect(() => {
    const fetchRecentlyViewed_and_FavoriteDeals = async () => {
      if (!user) return;

      setLoadingRecentlyViewed(true);
      setLoadingFavorites(true);
      try {
     
     
        const { data: favoriteDealIds, error: favoriteDealsError } =
          await supabase
            .from("user_details")
            .select("favorite_deals, recently_viewed_deals")
            .eq("id", user.id)
            .order("created_at", { ascending: false })
            .single();

        if (favoriteDealsError) throw favoriteDealsError;

        const { data: recentViewData, error: recentViewError } = await supabase
          .from("deals")
          .select("*, businesses(*)")
          .in("id", favoriteDealIds.recently_viewed_deals as string[])
          .eq("status", "published");

      if(recentViewError) throw recentViewError;
        // const { data: recentViewData, error: recentViewError } = await supabase
        //   .from('user_deal_interactions')
        //   .select('deal_id')
        //   .eq('user_id', user.id)
        //   .eq('interaction_type', 'view')
        //   .order('created_at', { ascending: false })
        //   .limit(10);

        // if (recentViewError) throw recentViewError;

        if (recentViewData && recentViewData.length > 0) {
          const dealIds = [...new Set(recentViewData.map((item) => item.id))];

          const { data: dealsData, error: dealsError } = await supabase
            .from("deals")
            .select("*, businesses(*)")
            .in("id", dealIds)
            .eq("status", "published");

          if (dealsError) throw dealsError;

          setRecentlyViewedDeals(dealsData as Deal[]);
        } else {
          setRecentlyViewedDeals([]);
        }

        const { data: favoriteData, error: favoriteError } = await supabase
          .from("deals")
          .select("*, businesses(*)")
          .in("id", favoriteDealIds.favorite_deals as string[])
          .eq("status", "published");

        if (favoriteError) throw favoriteError;

        if (favoriteData && favoriteData.length > 0) {
          const dealIds = [...new Set(favoriteData.map((item) => item.id))];

          const { data: dealsData, error: dealsError } = await supabase
            .from("deals")
            .select("*, businesses(*)")
            .in("id", dealIds)
            .eq("status", "published");

          if (dealsError) throw dealsError;

          setFavoriteDeals(dealsData as Deal[]);
        } else {
          setFavoriteDeals([]);
        }
      } catch (error) {
        console.error("Errore caricamento offerte viste di recente:", error);
        setRecentlyViewedDeals([]);
      } finally {
        setLoadingRecentlyViewed(false);
        setLoadingFavorites(false);
      }
    };

    fetchRecentlyViewed_and_FavoriteDeals();
    //  fetchFavoriteDeals();
  }, [user]);

  const getSelectedDayName = (): string => {
    const today = format(new Date(), "yyyy-MM-dd");
    if (selectedDate === today) {
      return "oggi";
    }

    const date = new Date(selectedDate);
    const dayName = format(date, "EEEE", { locale: it });
    return dayName;
  };

  const handleDateSelect = useCallback((date: string) => {
    setSelectedDate(date);
    setSearchParamsUpdated(true);
  }, []);

  const handleTimeChange = useCallback((newValue: number[]) => {
    setTimeValue(newValue);
    setSearchParamsUpdated(true);
  }, []);

  return {
    // Time and date state
    selectedDate,
    timeValue,
    nearByRadius,
    setNearByRadius,

    // Selection handlers
    handleDateSelect,
    handleTimeChange,
    resetToNow,
    getTimeFromPercentage,
    getSelectedDayName,

    // Deals data
    nearbyDeals,
    recentlyViewedDeals,
    favoriteDeals,
    featuredDeals,

    // Loading states
    isLoading,
    loadingDeals: isLoading, // Usiamo isLoading dal nuovo hook
    loadingRecentlyViewed,
    loadingFavorites,
    loadingFeatured: isLoadingFeatured,

    // Stats
    businessCount,

    // Metriche di ricerca
    metrics,
  };
};


import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface InviteToGroupParams {
  groupId: string;
  userIds: string[];
}

export const useInviteToGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ groupId, userIds }: InviteToGroupParams) => {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Create the invites
      const invites = userIds.map(userId => ({
        group_id: groupId,
        invited_user_id: userId,
        invited_by: user.id,
        status: 'pending'
      }));

      const { data: createdInvites, error } = await supabase
        .from('group_invites')
        .insert(invites)
        .select();

      if (error) {
        console.error('Error creating group invites:', error);
        throw error;
      }

      // Create notifications for each invite
      if (createdInvites && createdInvites.length > 0) {
        const notifications = createdInvites.map(invite => ({
          user_id: invite.invited_user_id,
          entity: 'group_invites',
          entity_id: invite.id
        }));

        const { error: notificationError } = await supabase
          .from('notifications')
          .insert(notifications);

        if (notificationError) {
          console.error('Error creating notifications:', notificationError);
          // Non blocchiamo l'operazione se le notifiche falliscono
        }
      }

      return createdInvites;
    },
    onSuccess: (_, { groupId }) => {
      // Invalida le query correlate per aggiornare i dati
      queryClient.invalidateQueries({ queryKey: ['group-details', groupId] });
      queryClient.invalidateQueries({ queryKey: ['group-invites'] });
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    }
  });
};

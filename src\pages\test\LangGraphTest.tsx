
import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";

import { useStream } from "@langchain/langgraph-sdk/react";
import UnifiedHeader from "@/components/toolbars/UnifiedHeader";
/**
 * ConversationTest Page
 *
 * A test page for the Conversation component that provides a simple interface
 * to test the functionality of the Conversation component in isolation.
 */

// https://langchain-ai.github.io/langgraphjs/how-tos/auth/custom_auth/
const LangGraphTest = () => {

   
  const thread = useStream({
    apiUrl: "https://lancatchup.onrender.com",
    assistantId: "catchup",
  
  });

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="fixed top-0 left-0 right-0 z-10">
        <UnifiedHeader title="LangGraph Test" isBusiness={false} />
      </div>

      <main className="flex-1 pt-16 pb-20 px-4 flex flex-col items-center justify-center">
        <div className="w-full max-w-md bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold mb-6 text-center">
            Test Conversation Component
          </h1>
          <p className="text-gray-600 mb-6 text-center">
            This page allows you to test the Conversation component in
            isolation. Use the buttons below to start and stop a conversation.
          </p>

          <div className="border-t border-b border-gray-200 py-6 my-4">
            <div>
              {thread.messages.map((message) => (
                <div key={message.id}>
                  {message.type} {": "}
                  {typeof message.content === "string"
                    ? message.content
                    : message.content.map((part) => {
                        if (part.type === "text")    return part.text;
                        return null;
                      })}
                </div>
              ))}
            </div>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                const form = e.target as HTMLFormElement;
                const message = new FormData(form).get("message") as string;
                form.reset();
                thread.submit({
                  messages: [  message ],
                });
              }}
            >
              <input
                type="text"
                name="message"
                id="message"
                className="w-full p-2 border rounded-lg"
              />
              <button
                type="submit"
                className="px-4 py-2 bg-blue-500 text-white rounded-lg"
              >
                Send
              </button>
            </form>
          </div>

          <div className="mt-6 text-sm text-gray-500">
            <p>Notes:</p>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>Make sure to grant microphone permissions when prompted</li>
              <li>The conversation uses ElevenLabs API for voice processing</li>
              <li>Check the console for additional debug information</li>
            </ul>
          </div>
        </div>
      </main>

      <div className="fixed bottom-0 left-0 right-0">
        <BottomNavigationBar isBusiness={false} />
      </div>
    </div>
  );
};

export default LangGraphTest;



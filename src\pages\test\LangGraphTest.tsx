
import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";
import { useStream } from "@langchain/langgraph-sdk/react";
import UnifiedHeader from "@/components/toolbars/UnifiedHeader";
import { useState, useRef, useEffect } from "react";
import { Send, Loader2 } from "lucide-react";
import ReactMarkdown from "react-markdown";

/**
 * LangGraph Chat Page
 *
 * A modern chat interface for testing LangGraph integration
 */

const LangGraphTest = () => {
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const thread = useStream({
    apiUrl: "https://lancatchup.onrender.com",
    assistantId: "catchup",
    messagesKey: "messages"
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [thread.messages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || thread.isLoading) return;

    thread.submit({
      messages: [{ type: "human", content: inputValue.trim() }],
      user_id: "fe95e629-0a4e-474b-97d1-fafe9d6863e3",
      email_address: "<EMAIL>",
      latitude: "45.4666",
      longitude: "9.1832",
      session_id: "1233",
      memory_lenght: "15",
      isChat: true,
    });

    setInputValue("");
  };

  const formatMessageContent = (content: string | Array<{ type: string; text?: string }>) => {
    if (typeof content === "string") {
      // Try to parse as JSON to extract llmResponse
      try {
        const parsed = JSON.parse(content);
        if (parsed.llmResponse) {
          return parsed.llmResponse;
        }
        return content;
      } catch {
        // If not valid JSON, return as is
        return content;
      }
    }
    if (Array.isArray(content)) {
      return content.map((part) => {
        if (part.type === "text") {
          // Try to parse text as JSON to extract llmResponse
          try {
            const parsed = JSON.parse(part.text || "");
            if (parsed.llmResponse) {
              return parsed.llmResponse;
            }
            return part.text;
          } catch {
            return part.text;
          }
        }
        return null;
      }).join("");
    }
    return "";
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="fixed top-0 left-0 right-0 z-10">
        <UnifiedHeader title="AI Assistant" isBusiness={false} />
      </div>

      {/* Chat Messages Area */}
      <main className="flex-1 pt-16 pb-24 overflow-hidden">
        <div className="h-full flex flex-col max-w-4xl mx-auto">
          <div className="flex-1 overflow-y-auto px-4 py-6 space-y-4">
            {thread.messages.length === 0 ? (
              <div  className="flex items-center justify-center h-full">
                <div className="text-center text-gray-500">
                  <div className="text-6xl mb-4">🤖</div>
                  <h3 className="text-lg font-medium mb-2">Start a conversation</h3>
                  <p className="text-sm">Ask me anything and I&apos;ll help you out!</p>
                </div>
              </div>
            ) : (
              thread.messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${
                    message.type === "human" ? "justify-end" : "justify-start"
                  }`}
                >
                  <div
                    className={`max-w-[280px] sm:max-w-sm lg:max-w-md px-4 py-2 rounded-lg ${
                      message.type === "human"
                        ? "bg-blue-500 text-white"
                        : "bg-white text-gray-800 shadow-sm border"
                    }`}
                  >
                    {message.type === "human" ? (
                      <div className="whitespace-pre-wrap break-words">
                        {formatMessageContent(message.content)}
                      </div>
                    ) : (
                      <div className="prose prose-sm max-w-none prose-headings:text-gray-800 prose-p:text-gray-800 prose-strong:text-gray-800 prose-code:text-gray-800 prose-pre:bg-gray-100 prose-pre:text-gray-800 prose-p:break-words">
                        <ReactMarkdown>
                          {formatMessageContent(message.content)}
                        </ReactMarkdown>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}

            {/* Loading indicator */}
            {thread.isLoading && (
              <div className="flex justify-start">
                <div className="bg-white text-gray-800 shadow-sm border px-4 py-2 rounded-lg flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm">Thinking...</span>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </div>
      </main>

      {/* Input Area */}
      <div className="fixed bottom-16 left-0 right-0 bg-white border-t border-gray-200 px-4 py-3 z-20">
        <div className="max-w-4xl mx-auto">
          <form onSubmit={handleSubmit} className="flex space-x-2">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Type your message..."
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={thread.isLoading}
            />
            <button
              type="submit"
              disabled={!inputValue.trim() || thread.isLoading}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {thread.isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </button>
          </form>
        </div>
      </div>

      {/* <div className="fixed bottom-0 left-0 right-0">
        <BottomNavigationBar isBusiness={false} />
      </div> */}
    </div>
  );
};

export default LangGraphTest;




import {  useLocation } from "react-router-dom";
import RewardsCard from "@/components/profile/RewardsCard";

import { motion } from "framer-motion";
import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";

import UnifiedHeader from "@/components/toolbars/UnifiedHeader";

const LoginRequired = () => {
  const location = useLocation();

  // Determine which section the user was trying to access
  const getSectionName = () => {
    const path = location.pathname;
    if (path.includes("prenotazioni") || path.includes("mybookings")) {
      return "prenotazioni";
    } else if (path.includes("conversations") || path.includes("chat")) {
      return "chat";
    }
    return "questa sezione";
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <UnifiedHeader title="" isBusiness={false} />

      <main className="pt-16 pb-4 px-4">
        <RewardsCard />
        <div className="bg-white rounded-xl p-6 shadow-md mt-8 text-center">
          <motion.h2
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-xl font-semibold text-gray-800 mb-3"
          >
            Accedi per visualizzare {getSectionName()}
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-gray-600"
          >
            Per accedere a {getSectionName()}, è necessario effettuare l'accesso
            o registrarsi.
          </motion.p>
        </div>
      </main>

      <BottomNavigationBar isBusiness={false} showVoiceButton={true} />
    </div>
  );
};

export default LoginRequired;

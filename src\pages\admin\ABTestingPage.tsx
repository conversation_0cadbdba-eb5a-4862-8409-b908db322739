import React from 'react';
import BottomNavigationBar from '@/components/toolbars/BottomNavigationBar';
import ABTestResults from '@/components/admin/ABTestResults';
import { useABTest } from '@/contexts/ABTestContext';
import { getPalette } from '@/styles/colorPalettes';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import UnifiedHeader from '@/components/toolbars/UnifiedHeader';

const ABTestingPage: React.FC = () => {
  const { variants } = useABTest();
  
  // Get the current palette
  const currentPalette = getPalette(variants.colorPalette);
  const defaultPalette = getPalette('default');
  const alternativePalette = getPalette('alternative');
  
  return (
    <div className="min-h-screen bg-background">
      <UnifiedHeader title="A/B Testing Dashboard" isBusiness={false} />
      
      <main className="container mx-auto py-6 px-4 pb-20">
        <h1 className="text-2xl font-bold mb-6">Color Palette A/B Testing</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Current Variant</CardTitle>
              <CardDescription>
                You are currently viewing the {variants.colorPalette} color palette
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col space-y-4">
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-6 h-6 rounded-full" 
                    style={{ backgroundColor: currentPalette.brandPrimary }}
                  />
                  <span>Primary: {currentPalette.brandPrimary}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-6 h-6 rounded-full" 
                    style={{ backgroundColor: currentPalette.brandSecondary }}
                  />
                  <span>Secondary: {currentPalette.brandSecondary}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-6 h-6 rounded-full" 
                    style={{ backgroundColor: currentPalette.brandLight }}
                  />
                  <span>Light: {currentPalette.brandLight}</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Test Variants</CardTitle>
              <CardDescription>
                Comparing default (pink) vs alternative (orange) color schemes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h3 className="font-medium">Default (Pink)</h3>
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: defaultPalette.brandPrimary }}
                    />
                    <span className="text-sm">{defaultPalette.brandPrimary}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: defaultPalette.brandSecondary }}
                    />
                    <span className="text-sm">{defaultPalette.brandSecondary}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium">Alternative (Orange)</h3>
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: alternativePalette.brandPrimary }}
                    />
                    <span className="text-sm">{alternativePalette.brandPrimary}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: alternativePalette.brandSecondary }}
                    />
                    <span className="text-sm">{alternativePalette.brandSecondary}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <ABTestResults />
        
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Implementation Details</CardTitle>
              <CardDescription>
                Technical information about the A/B test implementation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium">Test Configuration</h3>
                <p className="text-sm text-muted-foreground">
                  Users are randomly assigned to either the default (pink) or alternative (orange) color palette
                  with a 50/50 split. The assignment is stored in localStorage to ensure consistency across sessions.
                </p>
              </div>
              
              <div>
                <h3 className="font-medium">Tracked Metrics</h3>
                <ul className="list-disc list-inside text-sm text-muted-foreground">
                  <li>Page views and navigation patterns</li>
                  <li>User engagement (session duration, scroll depth, interaction count)</li>
                  <li>Conversion actions (bookings, deal views, business contacts)</li>
                  <li>Performance metrics (load times, rendering performance)</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium">Implementation Method</h3>
                <p className="text-sm text-muted-foreground">
                  The test is implemented using React Context API for state management and CSS variables for
                  dynamic styling. This approach ensures minimal performance impact while allowing for
                  comprehensive testing of color variations.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
      
      <BottomNavigationBar isBusiness={false} />
    </div>
  );
};

export default ABTestingPage;


import { useEffect, useRef, useState, useCallback } from 'react';
import { useMapsLibrary } from '@vis.gl/react-google-maps';

export type UseAutocompleteSuggestionsReturn = {
  suggestions: google.maps.places.AutocompleteSuggestion[];
  isLoading: boolean;
  resetSession: () => void;
};

export function useAutocompleteSuggestions(
  inputString: string,
  requestOptions: Partial<google.maps.places.AutocompleteRequest> = {}
): UseAutocompleteSuggestionsReturn {
  const placesLib = useMapsLibrary('places');

  // stores the current sessionToken
  const sessionTokenRef = useRef<google.maps.places.AutocompleteSessionToken | null>(null);

  // the suggestions based on the specified input
  const [suggestions, setSuggestions] = useState<google.maps.places.AutocompleteSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // reset the current session
  const resetSession = useCallback(() => {
    if (!placesLib) return;
    sessionTokenRef.current = new placesLib.AutocompleteSessionToken();
  }, [placesLib]);

  useEffect(() => {
    if (!placesLib) return;
    if (!sessionTokenRef.current) {
      sessionTokenRef.current = new placesLib.AutocompleteSessionToken();
    }
  }, [placesLib]);

  useEffect(() => {
    if (!placesLib || !inputString.trim()) {
      setSuggestions([]);
      return;
    }

    const request: google.maps.places.AutocompleteRequest = {
      input: inputString,
      sessionToken: sessionTokenRef.current!,
      language: 'it',
      ...requestOptions,
    };

    setIsLoading(true);

    placesLib.AutocompleteSuggestion.fetchAutocompleteSuggestions(request)
      .then(({ suggestions }) => {
        setSuggestions(suggestions);
        setIsLoading(false);
      })
      .catch((error) => {
        console.error('Error fetching autocomplete suggestions:', error);
        setSuggestions([]);
        setIsLoading(false);
      });
  }, [placesLib, inputString]); // Rimosse requestOptions dalle dipendenze

  return {
    suggestions,
    isLoading,
    resetSession,
  };
}
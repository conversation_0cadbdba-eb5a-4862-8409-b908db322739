# Supabase Edge Functions - UML Diagrams & Visual Documentation

## Architecture Overview

### System Component Diagram
```mermaid
flowchart TD
    %% Client Layer
    subgraph ClientApps["Client Applications"]
        WebApp["React Web App"]
        MobileApp["Mobile PWA"]
        API["External API Clients"]
    end
    
    %% Edge Functions Layer
    subgraph EdgeFunctions["Supabase Edge Functions"]
        GetBusinesses["get-nearest-businesses<br/>🏢 Business Discovery"]
        GetDeals["get-nearby-deals-by-location<br/>🎯 Deal Search"]
        SharedCORS["_shared/cors.ts<br/>🔗 CORS Configuration"]
    end
    
    %% Caching Layer
    subgraph CachingLayer["Caching Strategy"]
        MemoryCache["In-Memory Cache<br/>⚡ 10min TTL"]
        NoCaching["No Caching<br/>🔄 Real-time Data"]
    end
    
    %% Database Layer
    subgraph Database["Supabase Database"]
        PostGIS["PostGIS Extension<br/>🗺️ Spatial Queries"]
        BusinessTable["businesses table"]
        DealsTable["deals table"]
        StoredProc["get_nearby_deals()<br/>📊 Complex Query Logic"]
        RPCFunc["get_nearest_businesses_postgis()<br/>📍 Distance Calculations"]
    end
    
    %% External Services
    subgraph External["External Services"]
        GoogleMaps["Google Maps API<br/>🗺️ Future Integration"]
    end
    
    %% Connections
    ClientApps --> GetBusinesses
    ClientApps --> GetDeals
    
    GetBusinesses --> SharedCORS
    GetDeals --> SharedCORS
    
    GetBusinesses --> MemoryCache
    GetDeals --> NoCaching
    
    GetBusinesses --> RPCFunc
    GetDeals --> StoredProc
    
    RPCFunc --> PostGIS
    RPCFunc --> BusinessTable
    
    StoredProc --> DealsTable
    StoredProc --> BusinessTable
    StoredProc --> PostGIS
    
    GetBusinesses -.-> GoogleMaps
    
    %% Styling
    classDef clientStyle fill:#e1f5fe
    classDef edgeStyle fill:#f3e5f5
    classDef cacheStyle fill:#fff3e0
    classDef dbStyle fill:#e8f5e8
    classDef externalStyle fill:#fce4ec
    
    class WebApp,MobileApp,API clientStyle
    class GetBusinesses,GetDeals,SharedCORS edgeStyle
    class MemoryCache,NoCaching cacheStyle
    class PostGIS,BusinessTable,DealsTable,StoredProc,RPCFunc dbStyle
    class GoogleMaps externalStyle
```

---

## Sequence Diagrams

### get-nearest-businesses Flow
```mermaid
sequenceDiagram
    participant Client as "Client App"
    participant EdgeFunc as "get-nearest-businesses"
    participant Cache as "Memory Cache"
    participant Supabase as "Supabase DB"
    participant PostGIS as "PostGIS Function"

    Client->>EdgeFunc: POST /get-nearest-businesses<br/>{lat, lng, limit, require_deals}
    
    alt CORS Preflight
        Client->>EdgeFunc: OPTIONS
        EdgeFunc-->>Client: CORS Headers
    end
    
    EdgeFunc->>EdgeFunc: Validate Parameters<br/>(lat, lng required)
    
    alt Invalid Parameters
        EdgeFunc-->>Client: 400 Bad Request
    end
    
    EdgeFunc->>EdgeFunc: Generate Cache Key<br/>(lat_lng_limit_transportMode_useDirections_require_deals)
    
    EdgeFunc->>Cache: Check Cache
    
    alt Cache Hit
        Cache-->>EdgeFunc: Cached Data
        EdgeFunc-->>Client: Cached Response
    else Cache Miss
        Cache-->>EdgeFunc: Cache Miss
        
        EdgeFunc->>Supabase: Create Client Connection
        EdgeFunc->>PostGIS: RPC call: get_nearest_businesses_postgis<br/>{user_lat, user_lon, max_distance_km, max_results, require_deals}
        
        alt Database Error
            PostGIS-->>EdgeFunc: Error
            EdgeFunc-->>Client: 500 Internal Server Error
        else Success
            PostGIS-->>EdgeFunc: Business Data
            EdgeFunc->>EdgeFunc: Format Response<br/>(distance text, photos, etc.)
            EdgeFunc->>Cache: Store in Cache<br/>(TTL: 10 minutes)
            EdgeFunc-->>Client: Formatted Business List
        end
    end
```

### get-nearby-deals-by-location Flow
```mermaid
sequenceDiagram
    participant Client as "Client App"
    participant EdgeFunc as "get-nearby-deals-by-location"
    participant Supabase as "Supabase DB"
    participant StoredProc as "get_nearby_deals<br/>Stored Procedure"

    Client->>EdgeFunc: POST /get-nearby-deals-by-location<br/>{date, time?, location?, category_id?, cursor?, limit?}
    
    alt CORS Preflight
        Client->>EdgeFunc: OPTIONS
        EdgeFunc-->>Client: CORS Headers
    end
    
    EdgeFunc->>EdgeFunc: Parse JSON Body
    
    alt Invalid JSON
        EdgeFunc-->>Client: 400 Bad Request<br/>"Invalid parameters"
    end
    
    EdgeFunc->>EdgeFunc: Validate Required Parameters<br/>(date is required)
    
    alt Missing Date
        EdgeFunc-->>Client: 400 Bad Request<br/>"Date is required"
    end
    
    EdgeFunc->>EdgeFunc: Prepare Payload<br/>{p_date, p_time, p_lat, p_lng,<br/>p_radius, p_category_id, p_cursor, p_limit}
    
    EdgeFunc->>Supabase: Create Client Connection
    EdgeFunc->>StoredProc: RPC call: get_nearby_deals<br/>with all parameters
    
    alt Database Error
        StoredProc-->>EdgeFunc: Query Error
        EdgeFunc-->>Client: 500 Internal Server Error<br/>"Database query failed"
    else Success
        StoredProc-->>EdgeFunc: Deal Data with Business Info
        EdgeFunc->>EdgeFunc: Process & Validate Deal Status
        EdgeFunc->>EdgeFunc: Format Response<br/>(deals, business info, pagination)
        EdgeFunc->>EdgeFunc: Generate Next Cursor<br/>(if more results available)
        EdgeFunc-->>Client: Formatted Deal List<br/>with Pagination Info
    end
```

---

## Class Diagram - Data Structures

```mermaid
classDiagram
    class NearestBusinessesRequest {
        +number latitude
        +number longitude
        +number? limit
        +string? transportMode
        +boolean? useDirections
        +number? maxDistance_meters
        +boolean? require_deals
    }
    
    class Business {
        +string id
        +string name
        +number latitude
        +number longitude
        +number deal_count
        +string address
        +number distance
        +string distanceText
        +string[] photos
    }
    
    class NearestBusinessesResponse {
        +Business[] businesses
        +Business? nearestBusiness
    }
    
    class LocationParams {
        +number lat
        +number lng
        +number? radius
    }
    
    class NearbyDealsRequest {
        +string date
        +string? time
        +LocationParams? location
        +string? cityName
        +string? category_id
        +string? cursor
        +number? limit
    }
    
    class DealAvailability {
        +string id
        +string name
        +string description
        +number original_price
        +number discounted_price
        +number discount_percentage
        +string[]? images
        +any time_slots
        +string status
        +string created_at
        +string updated_at
        +boolean auto_confirm
        +string category_id
        +string start_date
        +string end_date
        +Business business
    }
    
    class NearbyDealsResponse {
        +string date
        +string? time
        +number deal_count
        +LocationParams? user_location
        +DealAvailability[] deals
        +string? next_cursor
        +boolean? cached
    }
    
    class CacheItem {
        +number timestamp
        +any data
    }
    
    %% Relationships
    NearestBusinessesResponse --> Business : contains
    NearbyDealsRequest --> LocationParams : uses
    NearbyDealsResponse --> LocationParams : contains
    NearbyDealsResponse --> DealAvailability : contains
    DealAvailability --> Business : includes
    CacheItem --> NearestBusinessesResponse : stores
```

---

## Function Comparison Diagram

```mermaid
graph TD
    %% Function Comparison
    subgraph GetBusinesses["🏢 get-nearest-businesses"]
        B1["🎯 Purpose: Find Businesses"]
        B2["📊 Data: Business Info"]
        B3["⚡ Caching: 10min TTL"]
        B4["📍 Query: PostGIS RPC"]
        B5["🔍 Filter: Location + Distance"]
        B6["📄 Pagination: Limit Only"]
        B7["⚡ Response Time: Fast (cached)"]
        
        B1 --> B2 --> B3 --> B4 --> B5 --> B6 --> B7
    end
    
    subgraph GetDeals["🎯 get-nearby-deals-by-location"]
        D1["🎯 Purpose: Find Available Deals"]
        D2["📊 Data: Deal + Business Info"]
        D3["🔄 Caching: No Cache"]
        D4["📍 Query: Stored Procedure"]
        D5["🔍 Filter: Date/Time/Location/Category"]
        D6["📄 Pagination: Cursor-based"]
        D7["⏱️ Response Time: Variable"]
        
        D1 --> D2 --> D3 --> D4 --> D5 --> D6 --> D7
    end
    
    %% Common Features
    subgraph CommonFeatures["🔗 Shared Features"]
        C1["✅ CORS Support"]
        C2["✅ TypeScript Types"]
        C3["✅ Error Handling"]
        C4["✅ Input Validation"]
        C5["✅ Supabase Integration"]
        
        C1 --> C2 --> C3 --> C4 --> C5
    end
    
    %% Use Cases
    subgraph UseCases["📋 Primary Use Cases"]
        U1["'Show nearby businesses'<br/>→ get-nearest-businesses"]
        U2["'Find deals for Friday 2PM'<br/>→ get-nearby-deals-by-location"]
        U3["'Business discovery'<br/>→ get-nearest-businesses"]
        U4["'Time-specific booking'<br/>→ get-nearby-deals-by-location"]
    end
    
    GetBusinesses -.-> CommonFeatures
    GetDeals -.-> CommonFeatures
    
    %% Styling
    classDef businessStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef dealStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef commonStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef useCaseStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class B1,B2,B3,B4,B5,B6,B7 businessStyle
    class D1,D2,D3,D4,D5,D6,D7 dealStyle
    class C1,C2,C3,C4,C5 commonStyle
    class U1,U2,U3,U4 useCaseStyle
```

---

## Performance & Scalability Analysis

### Performance Characteristics

| Metric | get-nearest-businesses | get-nearby-deals-by-location |
|--------|------------------------|------------------------------|
| **Average Response Time** | 50-200ms (cached)<br/>200-500ms (uncached) | 300-800ms |
| **Cache Strategy** | ✅ In-memory (10min TTL) | ❌ None |
| **Database Complexity** | Low (PostGIS distance query) | High (complex joins + filtering) |
| **Scalability** | High (cache reduces DB load) | Medium (database-dependent) |
| **Memory Usage** | Higher (cache storage) | Lower (no caching) |

### Optimization Opportunities

```mermaid
graph LR
    subgraph BusinessesOpt["🏢 get-nearest-businesses Optimizations"]
        BO1["Geographic Cache Keys<br/>📍 Location-based caching"]
        BO2["Redis Integration<br/>🔄 Distributed caching"]
        BO3["Index Optimization<br/>📊 Spatial index tuning"]
    end
    
    subgraph DealsOpt["🎯 get-nearby-deals-by-location Optimizations"]
        DO1["Query Caching<br/>⚡ Redis for popular queries"]
        DO2["Index Strategy<br/>📊 Composite indexes"]
        DO3["Connection Pooling<br/>🔗 Database optimization"]
        DO4["Result Streaming<br/>📡 Large dataset handling"]
    end
    
    classDef optStyle fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    class BO1,BO2,BO3,DO1,DO2,DO3,DO4 optStyle
```

---

## Database Integration Patterns

### PostGIS Integration (get-nearest-businesses)
```mermaid
graph TD
    A["📍 User Location<br/>(lat, lng)"] --> B["🔍 PostGIS Query<br/>ST_DWithin()"]
    B --> C["📊 Distance Calculation<br/>ST_Distance()"]
    C --> D["📋 Results Sorting<br/>ORDER BY distance"]
    D --> E["📄 Limit Results<br/>LIMIT clause"]
    E --> F["✅ Formatted Response"]
    
    classDef dbStyle fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    class A,B,C,D,E,F dbStyle
```

### Stored Procedure Integration (get-nearby-deals-by-location)
```mermaid
graph TD
    A["📅 Request Parameters<br/>(date, time, location, filters)"] --> B["🔧 Parameter Processing<br/>Validation & defaults"]
    B --> C["🗄️ Complex Query Logic<br/>Joins, filtering, availability"]
    C --> D["📊 Availability Checking<br/>Time slot validation"]
    D --> E["📄 Cursor Pagination<br/>Efficient large result handling"]
    E --> F["✅ Structured Response<br/>Deals + Business data"]
    
    classDef procStyle fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    class A,B,C,D,E,F procStyle
```

---

*Visual documentation for CatchUp MVP Supabase Edge Functions - Version 1.0* 
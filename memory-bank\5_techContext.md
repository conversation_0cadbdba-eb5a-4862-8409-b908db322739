# CatchUp - Technical Context

## Technology Stack

### Frontend Framework
- **React 18.3.1**: Latest stable version with concurrent features
- **TypeScript 5.8.2**: Strict type checking enabled
- **Vite 5.4.14**: Fast build tool with HMR and optimized production builds
- **SWC**: Fast TypeScript/React compilation

### UI Framework and Styling
- **Tailwind CSS 3.4.17**: Utility-first CSS framework
- **shadcn/ui 0.0.4**: High-quality component library built on Radix UI
- **Radix UI**: Unstyled, accessible UI primitives
- **Framer Motion 12.4.3**: Animation library for smooth interactions
- **Lucide React 0.507.0**: Icon library with consistent design

### Backend and Database
- **Supabase 2.49.4**: PostgreSQL database with real-time capabilities
- **Supabase Auth**: User authentication and authorization
- **Row Level Security (RLS)**: Database-level security policies
- **Real-time Subscriptions**: Live data updates via WebSockets

### State Management
- **React Query 5.56.2**: Server state management and caching
- **Zustand 5.0.5**: Lightweight client state management
- **React Hook Form 7.53.0**: Performant form handling
- **Zod 3.23.8**: TypeScript-first schema validation

### Maps and Location
- **Google Maps API**: Interactive maps with places and geocoding
- **@react-google-maps/api 2.19.3**: React wrapper for Google Maps
- **@vis.gl/react-google-maps 1.5.1**: Modern React Google Maps library
- **@googlemaps/markerclusterer 2.5.3**: Marker clustering for performance

### AI and Language Processing
- **LangChain Core 0.3.55**: AI/ML framework for recommendations
- **LangGraph 0.2.72**: Graph-based AI workflows
- **LangChain SDK 0.0.74**: SDK for LangChain integration

### Development Tools
- **ESLint 9.21.0**: Code linting with TypeScript and React rules
- **TypeScript ESLint 8.26.0**: TypeScript-specific linting rules
- **Vitest 3.2.3**: Fast unit testing framework
- **React Testing Library**: Component testing utilities

### Build and PWA
- **Vite PWA Plugin 1.0.0**: Progressive Web App capabilities
- **Service Workers**: Offline functionality and caching
- **Web App Manifest**: Native app-like installation

### Utilities and Helpers
- **date-fns 3.6.0**: Date manipulation and formatting
- **Lodash 4.17.21**: Utility functions
- **uuid 9.0.1**: Unique identifier generation
- **clsx 2.1.1**: Conditional className composition
- **@faker-js/faker 8.4.1**: Synthetic data generation

## Development Environment

### Required Software
```bash
Node.js >= 18.0.0
npm >= 9.0.0 (or pnpm/bun)
Git >= 2.30.0
```

### Environment Variables
```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Google Maps Configuration
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# App Configuration
VITE_APP_URL=http://localhost:5173
VITE_APP_NAME=CatchUp
```

### Project Structure
```
src/
├── components/          # Reusable UI components
│   ├── ui/             # shadcn/ui components
│   ├── layout/         # Layout components
│   └── forms/          # Form components
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── services/           # API service classes
├── types/              # TypeScript type definitions
├── utils/              # Helper functions
├── stores/             # Zustand stores
├── queries/            # React Query hooks
├── contexts/           # React contexts
├── lib/                # Library configurations
├── data/               # Static data and constants
├── styles/             # Global styles
└── integrations/       # Third-party integrations
```

## Database Architecture

### Supabase Configuration
- **Database**: PostgreSQL 15 with extensions enabled
- **Authentication**: Built-in auth with email/password and social providers
- **Row Level Security**: Enabled on all tables
- **Real-time**: Enabled for live updates
- **Storage**: For user uploads and business images

### Key Tables Schema
```sql
-- Deals table
CREATE TABLE deals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  price NUMERIC(10,2) NOT NULL,
  original_price NUMERIC(10,2),
  business_name TEXT NOT NULL,
  business_logo TEXT,
  image_url TEXT,
  time_slot JSONB, -- Flexible multi-day availability
  capacity INTEGER DEFAULT 1,
  category TEXT,
  location POINT,
  address TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Time slot bookings table
CREATE TABLE time_slot_bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  deal_id UUID REFERENCES deals(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  booking_date DATE NOT NULL,
  count INTEGER DEFAULT 1,
  status TEXT DEFAULT 'confirmed',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users profile table
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  preferences JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Database Policies (RLS)
```sql
-- Users can only access their own profile
CREATE POLICY "Users can view own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = id);

-- All users can view published deals
CREATE POLICY "Anyone can view deals" ON deals
  FOR SELECT USING (true);

-- Users can only access their own bookings
CREATE POLICY "Users can view own bookings" ON time_slot_bookings
  FOR SELECT USING (auth.uid() = user_id);
```

## Build Configuration

### Vite Configuration
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/api\.supabase\.co\/.*/i,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'supabase-cache',
            }
          }
        ]
      }
    })
  ],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          maps: ['@react-google-maps/api'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu']
        }
      }
    }
  }
});
```

### TypeScript Configuration
```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

### Tailwind Configuration
```typescript
// tailwind.config.ts
export default {
  content: ['./index.html', './src/**/*.{ts,tsx}'],
  theme: {
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        primary: 'hsl(var(--primary))',
        secondary: 'hsl(var(--secondary))',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      }
    }
  },
  plugins: [require('tailwindcss-animate')]
};
```

## Performance Considerations

### Bundle Optimization
- **Code Splitting**: Route-based and component-based splitting
- **Tree Shaking**: Unused code elimination
- **Compression**: Gzip/Brotli compression in production
- **Caching**: Aggressive caching of static assets

### Runtime Performance
- **React 18 Features**: Concurrent rendering and automatic batching
- **Memoization**: Strategic use of useMemo and useCallback
- **Virtual Scrolling**: For large lists (if needed)
- **Image Optimization**: WebP format with fallbacks

### Network Optimization
- **React Query**: Intelligent caching and background updates
- **Supabase Caching**: PostgreSQL query optimization
- **CDN**: Static asset delivery via CDN
- **Lazy Loading**: Components and images loaded on demand

## Security Implementation

### Authentication Security
- **JWT Tokens**: Secure token-based authentication
- **Refresh Tokens**: Automatic token renewal
- **Session Management**: Secure session handling
- **Password Policies**: Strong password requirements

### Data Security
- **Row Level Security**: Database-level access control
- **Input Validation**: Zod schemas for all user inputs
- **SQL Injection Protection**: Parameterized queries via Supabase
- **XSS Protection**: Content Security Policy headers

### API Security
- **Rate Limiting**: Protection against abuse
- **CORS Configuration**: Proper cross-origin resource sharing
- **HTTPS Only**: SSL/TLS encryption for all connections
- **Environment Variables**: Secure secret management

## Testing Strategy

### Unit Testing
```typescript
// Example test structure
describe('DealService', () => {
  it('should fetch deals with filters', async () => {
    const filters = { category: 'restaurant' };
    const deals = await dealService.getDeals(filters);
    
    expect(deals).toHaveLength(3);
    expect(deals[0]).toMatchObject({
      id: expect.any(String),
      title: expect.any(String),
      price: expect.any(Number)
    });
  });
});
```

### Component Testing
```typescript
// Example component test
test('SearchBar updates on input change', () => {
  const handleChange = vi.fn();
  render(<SearchBar value="" onChange={handleChange} />);
  
  const input = screen.getByRole('textbox');
  fireEvent.change(input, { target: { value: 'pizza' } });
  
  expect(handleChange).toHaveBeenCalledWith('pizza');
});
```

### E2E Testing (Planned)
- **User Registration**: Complete signup and onboarding flow
- **Deal Discovery**: Search, filter, and view deals
- **Booking Process**: End-to-end booking with payment
- **Business Management**: Deal creation and management

## Deployment and Infrastructure

### Current Hosting
- **Platform**: Lovable (development and preview)
- **Custom Domain**: Not yet configured
- **SSL**: Automatic HTTPS via platform

### Planned Production Setup
- **Hosting**: Netlify or Vercel for static hosting
- **CDN**: Built-in CDN for global distribution
- **Database**: Supabase hosted PostgreSQL
- **Monitoring**: Error tracking and performance monitoring

### Environment Management
- **Development**: Local Vite dev server
- **Staging**: Lovable preview environments
- **Production**: TBD based on deployment platform choice

## Integration Requirements

### Google Maps API
- **APIs Enabled**: Maps JavaScript API, Places API, Geocoding API
- **Usage Limits**: Monitor and optimize API calls
- **Billing**: Pay-per-use pricing model
- **Security**: API key restrictions by domain/IP

### Supabase Integration
- **Plan**: Pro plan for production with higher limits
- **Backup**: Automated database backups
- **Monitoring**: Built-in database and API monitoring
- **Scaling**: Auto-scaling based on usage

### Third-party Dependencies
- **Regular Updates**: Security patches and feature updates
- **Vulnerability Scanning**: Automated dependency checking
- **License Compliance**: Open source license compatibility
- **Performance Impact**: Bundle size and runtime performance monitoring 
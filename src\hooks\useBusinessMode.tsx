
import { useState, useEffect } from 'react';

export const useBusinessMode = () => {
  const [isBusinessMode, setIsBusinessMode] = useState(() => {
    return localStorage.getItem('isBusinessMode') === 'true';
  });

  const toggleBusinessMode = () => {
    const newValue = !isBusinessMode;
    setIsBusinessMode(newValue);
    localStorage.setItem('isBusinessMode', String(newValue));
  };

  return { isBusinessMode, toggleBusinessMode };
};

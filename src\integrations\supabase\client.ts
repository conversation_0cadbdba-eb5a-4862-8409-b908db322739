// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://pnrldflljkfxiefryvsu.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBucmxkZmxsamtmeGllZnJ5dnN1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzk4NzM5MDcsImV4cCI6MjA1NTQ0OTkwN30.suCPrvUexJOmX5Cf3FLWM82mqgTK32WrEETtKd65fBg";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});

import { motion } from "framer-motion";
import { useState } from "react";
import { TermsModal } from "./TermsModal";
import { PrivacyModal } from "./PrivacyModal";

interface SignupFormProps {
  firstName: string;
  lastName: string;
  displayName: string;
  phoneNumber: string;
  email: string;
  password: string;
  confirmPassword: string;
  acceptedTerms: boolean;
  isLoading: boolean;
  onFirstNameChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onLastNameChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onDisplayNameChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onPhoneNumberChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onEmailChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onPasswordChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onConfirmPasswordChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onTermsChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: (e: React.FormEvent) => void;
}

export const SignupForm = ({
  firstName,
  lastName,
  displayName,
  phoneNumber,
  email,
  password,
  confirmPassword,
  acceptedTerms,
  isLoading,
  onFirstNameChange,
  onLastNameChange,
  onDisplayNameChange,
  onPhoneNumberChange,
  onEmailChange,
  onPasswordChange,
  onConfirmPasswordChange,
  onTermsChange,
  onSubmit
}: SignupFormProps) => {
  const [isTermsOpen, setIsTermsOpen] = useState(false);
  const [isPrivacyOpen, setIsPrivacyOpen] = useState(false);

  return (
    <>
      <motion.form 
        initial={{ opacity: 0, y: 20 }} 
        animate={{ opacity: 1, y: 0 }} 
        transition={{ delay: 0.2 }} 
        className="space-y-4" 
        onSubmit={onSubmit}
      >
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Nome
          </label>
          <input 
            type="text" 
            value={firstName}
            onChange={onFirstNameChange}
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-transparent"
            required 
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Cognome
          </label>
          <input 
            type="text" 
            value={lastName}
            onChange={onLastNameChange}
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-transparent"
            required 
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Nome visualizzato
          </label>
          <input 
            type="text" 
            value={displayName}
            onChange={onDisplayNameChange}
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-transparent"
            required 
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Numero di telefono (opzionale)
          </label>
          <input 
            type="tel" 
            value={phoneNumber}
            onChange={onPhoneNumberChange}
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input 
            type="email" 
            value={email}
            onChange={onEmailChange}
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-transparent"
            required 
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Password
          </label>
          <input 
            type="password" 
            value={password}
            onChange={onPasswordChange}
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-transparent"
            required 
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Conferma Password
          </label>
          <input 
            type="password"
            value={confirmPassword}
            onChange={onConfirmPasswordChange}
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-transparent"
            required 
          />
        </div>

        <div className="flex items-start gap-3 mt-6">
          <input 
            type="checkbox" 
            checked={acceptedTerms}
            onChange={onTermsChange}
            className="mt-1 rounded border-gray-300 text-brand-primary focus:ring-brand-primary" 
          />
          <p className="text-sm text-gray-600">
            Accetto i{" "}
            <button 
              type="button" 
              onClick={() => setIsTermsOpen(true)} 
              className="text-brand-primary"
            >
              Termini e Condizioni
            </button> e la{" "}
            <button 
              type="button" 
              onClick={() => setIsPrivacyOpen(true)} 
              className="text-brand-primary"
            >
              Privacy Policy
            </button>
          </p>
        </div>

        <motion.button 
          type="submit"
          whileHover={{ scale: 1.02 }} 
          whileTap={{ scale: 0.98 }} 
          className="w-full bg-gradient-to-r from-brand-secondary to-brand-primary text-white py-4 rounded-xl font-medium mb-4 disabled:opacity-50"
          disabled={isLoading}
        >
          {isLoading ? "Registrazione in corso..." : "Salva"}
        </motion.button>
      </motion.form>

      <TermsModal 
        isOpen={isTermsOpen} 
        onClose={() => setIsTermsOpen(false)} 
      />
      
      <PrivacyModal 
        isOpen={isPrivacyOpen} 
        onClose={() => setIsPrivacyOpen(false)} 
      />
    </>
  );
};

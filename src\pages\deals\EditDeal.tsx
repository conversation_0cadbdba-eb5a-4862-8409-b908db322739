import { useNavigate, useParams } from "react-router-dom";

import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { FormData, WeeklySchedule } from "@/types/deals";
import { INITIAL_SCHEDULE } from "@/data/daysNames";
import CreateDealForm from "@/components/deals/creation/CreateDealForm";
import UnifiedHeader from "@/components/toolbars/UnifiedHeader";
import DealFooter from "@/components/deals/ui/DealFooter";


const EditDeal = () => {
  const { dealId } = useParams();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [existingImages, setExistingImages] = useState<string[]>([]);
  const [businessId, setBusinessId] = useState<string>("");
  const [formData, setFormData] = useState<FormData>({
    title: "",
    description: "",
    original_price: "",
    discount_percentage: "",
    discounted_price: "",
    start_date: "",
    end_date: "",
    time_slots: INITIAL_SCHEDULE,
    images: [],
    status: "draft",
    deal_categories: []
  });

  useEffect(() => {
    if (formData.original_price && formData.discount_percentage) {
      const original = parseFloat(formData.original_price);
      const discount = parseFloat(formData.discount_percentage);
      if (!isNaN(original) && !isNaN(discount)) {
        const discounted = original * (1 - discount / 100);
        setFormData(prev => ({
          ...prev,
          discounted_price: discounted.toFixed(2)
        }));
      }
    }
  }, [formData.original_price, formData.discount_percentage]);

  useEffect(() => {
    const fetchDeal = async () => {
      if (!dealId) return;

      const { data: deal, error } = await supabase
        .from('deals')
        .select('*, business_id')
        .eq('id', dealId)
        .single();

      if (error) {
        toast.error("Errore nel caricamento dell'offerta");
        return;
      }

      setBusinessId(deal.business_id);

      const startDate = new Date(deal.start_date);
      const endDate = new Date(deal.end_date);
      
      const formatDateForInput = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day}T${hours}:${minutes}`;
      };

      const timeSlots = deal.time_slots ? 
        (typeof deal.time_slots === 'string' ? 
          JSON.parse(deal.time_slots) : deal.time_slots) as WeeklySchedule : 
        INITIAL_SCHEDULE;

      setFormData({
        ...formData,
        status: deal.status,
        title: deal.title,
        description: deal.description || "",
        original_price: deal.original_price.toString(),
        discount_percentage: deal.discount_percentage?.toString() || "",
        discounted_price: deal.discounted_price.toString(),
        start_date: formatDateForInput(startDate),
        end_date: formatDateForInput(endDate),
        time_slots: timeSlots,
        images: [],
      });

      if (deal.images) {
        setExistingImages(deal.images as string[]);
      }
    };

    fetchDeal();
  }, [dealId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsSubmitting(true);

      if (!dealId) {
        toast.error("ID offerta non valido");
        return;
      }

      if (!formData.start_date || !formData.end_date) {
        toast.error("Le date di inizio e fine sono obbligatorie");
        return;
      }

      const startDate = new Date(formData.start_date);
      const endDate = new Date(formData.end_date);
      
      if (endDate <= startDate) {
        toast.error("La data di fine deve essere successiva alla data di inizio");
        return;
      }

      const imageUrls = [];
      for (const image of formData.images) {
        const fileExt = image.name.split('.').pop();
        const filePath = `${Date.now()}.${fileExt}`;
        
        const { error: uploadError } = await supabase.storage
          .from('deals')
          .upload(filePath, image);

        if (uploadError) {
          throw uploadError;
        }

        const { data: { publicUrl } } = supabase.storage
          .from('deals')
          .getPublicUrl(filePath);

        imageUrls.push(publicUrl);
      }

      const finalImages = [...existingImages, ...imageUrls];
      const timeSlots = JSON.parse(JSON.stringify(formData.time_slots));
      console.log("timeSlots", timeSlots);
      // TODO: Check if we need to check. Right now we chack for migration adding seats
      let availableSeats = 1;
      timeSlots.schedule.forEach((day: any) => {
        day.time_slots.forEach((slot: any) => {
          // Se non c'è il campo available_seats, lo impostiamo a 1
          if (!slot.available_seats) {
            slot.available_seats = 1;
          }
         
          // Aggiorniamo il numero massimo di posti disponibili
          if (slot.available_seats > availableSeats) {
            availableSeats = slot.available_seats;
          }
        });
      });
      console.log("timeSlots checled", timeSlots);

      const { error } = await supabase
        .from('deals')
        .update({
          title: formData.title,
          description: formData.description,
          original_price: parseFloat(formData.original_price),
          discount_percentage: parseFloat(formData.discount_percentage),
          discounted_price: parseFloat(formData.discounted_price),
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          time_slots: timeSlots,
          images: finalImages,
          status: formData.status
        })
        .eq('id', dealId);

      if (error) throw error;

  //    toast.success("Offerta aggiornata con successo!");
      navigate(`/business/${businessId}`);
    } catch (error) {
      toast.error("Errore durante l'aggiornamento dell'offerta");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Handle form submission
   */
  const onSubmitDeal = () => {
    // Simulate form submission
    const formEvent = new Event('submit', { cancelable: true }) as unknown as React.FormEvent;
    handleSubmit(formEvent);
  };

  /**
   * Handle form cancellation
   */
  const onCancelDeal = () => {
    // Navigate back to business page
    navigate(`/business/${businessId}`);
  };

  if (!businessId) return null;

  return (
    <div className="bg-gray-50 max-w-md mx-auto min-h-screen">
      <UnifiedHeader variant="minimal" showBackButton={true} title="Modifica Offerta" />

      <main className="px-4 pt-20 pb-32">
        <div className="space-y-6">
          <CreateDealForm 
            formData={formData}
            setFormData={setFormData}
            existingImages={existingImages}
            onRemoveExistingImage={(index) => {
              setExistingImages(prev => prev.filter((_, i) => i !== index));
            }}
            businessId={businessId}
            onSubmit={onSubmitDeal}
            onCancel={onCancelDeal}
          />
        </div>
      </main>
    </div>
  );
};

export default EditDeal;

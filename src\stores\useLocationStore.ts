import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { supabase } from '@/integrations/supabase/client';

// Types
export interface Coordinates {
  lat: number;
  lng: number;
}

export type LocationSource = 'geolocation' | 'demo' | 'fallback' | 'database';

export interface LocationState {
  // Current location
  coordinates: Coordinates | null;
  source: LocationSource | null;
  isLoading: boolean;
  error: string | null;
  
  // Demo mode
  isDemoMode: boolean;
  demoCoordinates: Coordinates;
  
  // Fallback
  fallbackCoordinates: Coordinates;
  
  // Permissions
  isPermissionDenied: boolean;
  
  // Timestamps
  lastUpdated: number | null;
}

export interface LocationActions {
  // Core actions
  setCoordinates: (coords: Coordinates, source: LocationSource) => void;
  setDemoMode: (enabled: boolean) => void;
  setDemoCoordinates: (coords: Coordinates) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Async actions
  getCurrentLocation: () => Promise<boolean>;
  loadUserSettings: (userId?: string) => Promise<void>;
  saveDemoCoordinates: (userId: string, coords: Coordinates) => Promise<boolean>;
  updateDemoMode: (userId: string, enabled: boolean) => Promise<boolean>;
  
  // Utilities
  reset: () => void;
  getDisplayCoordinates: () => Coordinates | null;
}

// Default values
const DEFAULT_DEMO_COORDS: Coordinates = { lat: 45.4666, lng: 9.1832 }; // Milan
const DEFAULT_FALLBACK_COORDS: Coordinates = { lat: 45.4671, lng: 9.1526 }; // Milan center

const initialState: LocationState = {
  coordinates: null,
  source: null,
  isLoading: false,
  error: null,
  isDemoMode: false,
  demoCoordinates: DEFAULT_DEMO_COORDS,
  fallbackCoordinates: DEFAULT_FALLBACK_COORDS,
  isPermissionDenied: false,
  lastUpdated: null,
};

// Create store with persistence
export const useLocationStore = create<LocationState & LocationActions>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Core actions
      setCoordinates: (coords, source) => set({
        coordinates: coords,
        source,
        lastUpdated: Date.now(),
        isLoading: false,
        error: null,
      }),

      setDemoMode: (enabled) => {
        const state = get();
        set({ isDemoMode: enabled });
        
        if (enabled) {
          // Use demo coordinates
          set({
            coordinates: state.demoCoordinates,
            source: 'demo',
            lastUpdated: Date.now(),
          });
        } else {
          // Try to get real location
          get().getCurrentLocation();
        }
      },

      setDemoCoordinates: (coords) => {
        const state = get();
        set({ demoCoordinates: coords });
        
        // If demo mode is active, also update current coordinates
        if (state.isDemoMode) {
          set({
            coordinates: coords,
            source: 'demo',
            lastUpdated: Date.now(),
          });
        }
      },

      setLoading: (loading) => set({ isLoading: loading }),

      setError: (error) => set({ error, isLoading: false }),

      // Async actions
      getCurrentLocation: async () => {
        const { setCoordinates, setError, setLoading } = get();
        
        if (!navigator.geolocation) {
          setError('Geolocation not supported');
          return false;
        }

        setLoading(true);
        
        try {
          const position = await new Promise<GeolocationPosition>((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 300000, // 5 minutes
            });
          });

          const coords = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };

          setCoordinates(coords, 'geolocation');
          set({ isPermissionDenied: false });
          return true;

        } catch (error: any) {
          console.error('Geolocation error:', error);
          
          if (error.code === 1) { // Permission denied
            set({ isPermissionDenied: true });
            setError('Location permission denied');
          } else {
            setError('Failed to get location');
          }

          // Use fallback
          const { fallbackCoordinates } = get();
          setCoordinates(fallbackCoordinates, 'fallback');
          return false;
        }
      },

      loadUserSettings: async (userId) => {
        const { setDemoMode, setDemoCoordinates, setError } = get();
        
        try {
          if (!userId) {
            // Load global settings for non-authenticated users
            const { data: globalData, error } = await supabase
              .from('globalsetting')
              .select('map_demo, demo_location')
              .eq('id', 1)
              .maybeSingle();

            if (error) throw error;

            if (globalData) {
              setDemoMode(globalData.map_demo ?? false);
              
              if (globalData.demo_location) {
                const demoLocation = typeof globalData.demo_location === 'string'
                  ? JSON.parse(globalData.demo_location)
                  : globalData.demo_location;
                
                if (demoLocation?.lat && demoLocation?.lng) {
                  setDemoCoordinates(demoLocation);
                }
              }
            }
          } else {
            // Load user settings for authenticated users
            const { data: userDetails, error } = await supabase
              .from('user_details')
              .select('map_demo, demo_location')
              .eq('id', userId)
              .maybeSingle();

            if (error) throw error;

            if (userDetails) {
              setDemoMode(userDetails.map_demo ?? false);
              
              if (userDetails.demo_location) {
                const demoLocation = typeof userDetails.demo_location === 'string'
                  ? JSON.parse(userDetails.demo_location)
                  : userDetails.demo_location;
                
                if (demoLocation?.lat && demoLocation?.lng) {
                  setDemoCoordinates(demoLocation);
                }
              }
            }
          }
        } catch (error) {
          console.error('Error loading user settings:', error);
          setError('Failed to load location settings');
        }
      },

      saveDemoCoordinates: async (userId, coords) => {
        const { setDemoCoordinates, setError } = get();
        
        try {
          const { error } = await supabase
            .from('user_details')
            .update({ demo_location: JSON.stringify(coords) })
            .eq('id', userId);

          if (error) throw error;

          setDemoCoordinates(coords);
          return true;

        } catch (error) {
          console.error('Error saving demo coordinates:', error);
          setError('Failed to save demo coordinates');
          return false;
        }
      },

      updateDemoMode: async (userId, enabled) => {
        const { setDemoMode, setError } = get();
        
        try {
          const { error } = await supabase
            .from('user_details')
            .update({ map_demo: enabled })
            .eq('id', userId);

          if (error) throw error;

          setDemoMode(enabled);
          return true;

        } catch (error) {
          console.error('Error updating demo mode:', error);
          setError('Failed to update demo mode');
          return false;
        }
      },

      // Utilities
      reset: () => set(initialState),

      getDisplayCoordinates: () => {
        const state = get();
        return state.coordinates || state.fallbackCoordinates;
      },
    }),
    {
      name: 'catchup-location-store',
      partialize: (state) => ({
        isDemoMode: state.isDemoMode,
        demoCoordinates: state.demoCoordinates,
        coordinates: state.coordinates,
        source: state.source,
        lastUpdated: state.lastUpdated,
      }),
    }
  )
);

// Convenience hooks
export const useCurrentLocation = () => {
  const store = useLocationStore();
  return {
    coordinates: store.getDisplayCoordinates(),
    source: store.source,
    isLoading: store.isLoading,
    error: store.error,
  };
};

export const useDemoMode = () => {
  const store = useLocationStore();
  return {
    isDemoMode: store.isDemoMode,
    demoCoordinates: store.demoCoordinates,
    setDemoMode: store.setDemoMode,
    setDemoCoordinates: store.setDemoCoordinates,
  };
};

export const useLocationActions = () => {
  const store = useLocationStore();
  return {
    getCurrentLocation: store.getCurrentLocation,
    loadUserSettings: store.loadUserSettings,
    saveDemoCoordinates: store.saveDemoCoordinates,
    updateDemoMode: store.updateDemoMode,
    setLoading: store.setLoading,
    setError: store.setError,
    reset: store.reset,
  };
}; 
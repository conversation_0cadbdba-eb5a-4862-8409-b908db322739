
import { useSession } from "./useSession";
import { useUserDetails } from "./useUserDetails";
import { useLogout } from "./useLogout";

export const useAuth = () => {
  const { user, isLoading, isAuthenticated } = useSession();
  const { userDetails, getFullName, updateLocationEnabled } = useUserDetails(user?.id);
  const { logout } = useLogout();

  return {
    user,
    userDetails,
    isLoading,
    isAuthenticated,
    logout,
    getFullName,
    updateLocationEnabled
  };
};

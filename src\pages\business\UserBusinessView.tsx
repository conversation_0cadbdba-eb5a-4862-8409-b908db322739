import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ArrowLeft, MapPin, Phone, Mail, Globe, Star } from "lucide-react";
import { motion } from "framer-motion";
import { useBusinessById } from "@/hooks/useBusinesses";
import { useBusinessDeals } from "@/hooks/useBusinessDeals";
import DealCard from "@/components/deals/core/DealCard";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import BusinessAddress from "@/components/business/BusinessAddress";
import { useBusinessMode } from "@/hooks/useBusinessMode";
import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";
import { BusinessReviews } from "@/components/reviews/BusinessReviews";

const UserBusinessView = () => {
  const { businessId } = useParams<{ businessId: string }>();
  const navigate = useNavigate();
  const { business, isLoading: businessLoading } = useBusinessById(businessId!);
  const { deals, isLoading: dealsLoading } = useBusinessDeals(businessId);

  if (businessLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Caricamento attività...</p>
        </div>
      </div>
    );
  }

  if (!business) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Attività non trovata
          </h2>
          <p className="text-gray-600 mb-4">
            L'attività che stai cercando non esiste.
          </p>
          <Button onClick={() => navigate("/")} variant="outline">
            Torna alla home
          </Button>
        </div>
      </div>
    );
  }

  const publishedDeals =
    deals?.filter((deal) => deal.status === "published") || [];

  return (
    <main className="min-h-screen bg-gray-50">
      <header className="fixed top-0 left-0 right-0 bg-white z-50 px-4 py-3 flex justify-between items-center border-b">
        <button onClick={() => navigate(-1)} className="p-1">
          <ArrowLeft className="h-5 w-5 text-gray-700" />
        </button>
        <div className="flex justify-center gap-2">
          <h1 className="text-xl text-gray-800 text-center w-full font-semibold">Dettagli Attività</h1>
        </div>
      </header>

      <main className="pt-16 pb-20 px-4">
        {/* Business Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }} 
          transition={{ duration: 0.5 }}
        >
          <Card className="mb-8 overflow-hidden">
            <div className="relative h-64 bg-gradient-to-r from-brand-primary to-purple-600">
              {business.photos && business.photos.length > 0 ? (
                <img
                  src={business.photos[0]}
                  alt={business.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center text-white">
                    <Star className="h-16 w-16 mx-auto mb-4 opacity-50" />
                    <p className="text-lg opacity-75">
                      Nessuna immagine disponibile
                    </p>
                  </div>
                </div>
              )}
              <div className="absolute inset-0 bg-black bg-opacity-30"></div>
            </div>

            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
                <div className="flex-1">
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {business.name}
                  </h1>

                  {business.description && (
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {business.description}
                    </p>
                  )}

                  <div className="space-y-3">
                    {business.address && (
                      <div className="flex items-start gap-2">
                        <MapPin className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">
                          {business.address}
                        </span>
                      </div>
                    )}

                     {business.phone && (
                       <div className="flex items-center gap-2">
                         <Phone className="h-5 w-5 text-gray-500" />
                         <a
                           href={`tel:${business.phone}`}
                           className="text-gray-700 hover:text-brand-primary transition-colors"
                         >
                           {business.phone}
                         </a>
                       </div>
                     )}

                      <div className="flex items-center gap-2">
                        <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                        <span className="text-gray-700">
                          {business.score ? Number(business.score).toFixed(1) : '0'} 
                          {` (${business.review_count || 0} ${
                            (business.review_count || 0) === 1 ? 'recensione' : 'recensioni'
                          })`}
                        </span>
                      </div>

                    {business.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="h-5 w-5 text-gray-500" />
                        <a
                          href={`mailto:${business.email}`}
                          className="text-gray-700 hover:text-brand-primary transition-colors"
                        >
                          {business.email}
                        </a>
                      </div>
                    )}

                    {business.website && (
                      <div className="flex items-center gap-2">
                        <Globe className="h-5 w-5 text-gray-500" />
                        <a
                          href={business.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-700 hover:text-brand-primary transition-colors"
                        >
                          Visita il sito web
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Deals Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Offerte Disponibili
            </h2>
            <p className="text-gray-600">
              Scopri tutte le offerte speciali di {business.name}
            </p>
          </div>

          {dealsLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div
                  key={i}
                  className="bg-white rounded-3xl p-6 shadow-sm animate-pulse"
                >
                  <div className="h-48 bg-gray-200 rounded-2xl mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              ))}
            </div>
          ) : publishedDeals.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {publishedDeals.map((deal) => (
                <motion.div
                  key={deal.id}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <DealCard
                    deal={deal}
                    onClick={() => navigate(`/deal/${deal.id}`)}
                    variant="full"
                    showVisitBusiness={false}
                    showAddress={false}
                    
                  />
                </motion.div>
              ))}
            </div>
          ) : (
            <Card className="p-12 text-center">
              <div className="max-w-md mx-auto">
                <Star className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Nessuna offerta disponibile
                </h3>
                <p className="text-gray-600">
                  Al momento {business.name} non ha offerte attive. Torna a
                  trovarci presto!
                </p>
              </div>
            </Card>
          )}
        </motion.div>

        {/* Reviews Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mt-8"
        >
          <BusinessReviews businessId={businessId!} businessName={business.name} />
        </motion.div>
      </main>
      <BottomNavigationBar isBusiness={false} />
    </main>
  );
};

export default UserBusinessView;

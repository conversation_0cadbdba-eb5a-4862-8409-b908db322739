// Service Worker Push Notification Handler
// This file extends the auto-generated service worker with push notification support

// Handle push notifications
self.addEventListener('push', function(event) {
  console.log('Push event received:', event);
  
  let title = 'CatchUp';
  let body = 'You have a new notification';
  let icon = '/icon-192x192.png';
  let badge = '/icon-192x192.png';
  let tag = 'catchup-notification';
  
  // Parse push data if available
  if (event.data) {
    try {
      const data = event.data.json();
      title = data.title || title;
      body = data.body || body;
      icon = data.icon || icon;
      badge = data.badge || badge;
      tag = data.tag || tag;
    } catch (e) {
      console.log('Push data is not JSON:', event.data.text());
      body = event.data.text() || body;
    }
  }
  
  const options = {
    body: body,
    icon: icon,
    badge: badge,
    tag: tag,
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: '1'
    },
    actions: [
      {
        action: 'explore',
        title: 'Open CatchUp',
        icon: '/icon-192x192.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/icon-192x192.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification(title, options)
  );
});

// Handle notification clicks
self.addEventListener('notificationclick', function(event) {
  console.log('Notification click received:', event);
  
  event.notification.close();
  
  if (event.action === 'close') {
    // User clicked close
    return;
  }
  
  // Default action or 'explore' action - open the app
  event.waitUntil(
    clients.openWindow('/')
  );
});

// Handle background sync for offline notifications
self.addEventListener('sync', function(event) {
  if (event.tag === 'background-sync') {
    console.log('Background sync triggered');
    // Handle offline notifications when back online
  }
});

// Handle messages from main thread
self.addEventListener('message', function(event) {
  console.log('SW received message:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
}); 
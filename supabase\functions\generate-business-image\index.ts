
// Questo file implementa una Edge Function che utilizza l'API di Replicate.ai
// per generare immagini di attività commerciali utilizzando il modello Flux.

import { corsHeaders } from "../_shared/cors.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4';

const REPLICATE_API_TOKEN = Deno.env.get('REPLICATE_API_TOKEN');
const SUPABASE_URL = Deno.env.get('SUPABASE_URL');
const SUPABASE_ANON_KEY = Deno.env.get('SUPABASE_ANON_KEY');

interface RequestBody {
  prompt: string;
  businessType?: string;
  businessName?: string;
}

Deno.serve(async (req) => {
  // Gestisce le richieste OPTIONS per CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Verifica se il token API di Replicate è disponibile
    if (!REPLICATE_API_TOKEN) {
      throw new Error("REPLICATE_API_TOKEN non configurato.");
    }

    if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
      throw new Error("Credenziali di Supabase mancanti.");
    }

    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

    // Ottieni i dati dalla richiesta
    const { prompt, businessType = '', businessName = '' } = await req.json() as RequestBody;

    if (!prompt && !businessType) {
      throw new Error("È necessario fornire un prompt o un tipo di attività.");
    }

    // Costruisci un prompt migliorato
    let enhancedPrompt = prompt;
    
    if (!prompt && businessType) {
      enhancedPrompt = `Fotografia professionale di ${businessType} chiamato "${businessName}", illuminazione professionale, alta qualità, fotografia commerciale, realistico`;
    } else if (businessType && businessName) {
      enhancedPrompt = `${prompt}, ${businessType} chiamato "${businessName}", illuminazione professionale, alta qualità`;
    }

    // Prepara la richiesta per Replicate.ai
    const response = await fetch("https://api.replicate.com/v1/models/black-forest-labs/flux-1.1-pro-ultra/predictions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${REPLICATE_API_TOKEN}`,
        "Prefer": "wait"
      },
      body: JSON.stringify({
        input: {
         
            "raw": false,
            "prompt": enhancedPrompt,
            "aspect_ratio": "3:2",
            "output_format": "jpg",
            "safety_tolerance": 2
         
        },
      }),
    });

    const prediction = await response.json();
    
    // Log per debug
    console.log("Risposta di Replicate:", prediction);
    
    // Controlla se c'è un errore nella risposta di Replicate
    if (prediction.error) {
      throw new Error(`Errore Replicate: ${prediction.error}`);
    }
   
    // Verifica lo stato della generazione
    if (prediction.status === "succeeded" || prediction.status === "processing") {
      return new Response(JSON.stringify({ 
        success: true, 
        imageUrl: prediction.output
      }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    } else if (prediction.status === "starting") {
      // Per generazioni che richiedono più tempo, restituisci l'ID per polling
      return new Response(JSON.stringify({
        success: true,
        id: prediction.id,
        status: prediction.status,
        message: "Generazione in corso. Controlla lo stato più tardi."
      }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    } else {
      // Per altri stati (errore, annullato, ecc.)
      return new Response(JSON.stringify({
        success: false,
        message: `Stato imprevisto: ${prediction.status}`,
        details: prediction
      }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

  } catch (error) {
    console.error("Errore durante la generazione dell'immagine:", error);
    
    return new Response(JSON.stringify({
      success: false,
      message: error.message
    }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});

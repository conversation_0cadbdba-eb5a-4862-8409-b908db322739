import { motion } from "framer-motion";

interface ServiceCategory {
  id: string;
  name: string;
  icon: string;
  selected: boolean;
}

interface CategoriesStepProps {
  categories: ServiceCategory[];
  onToggleCategory: (id: string) => void;
}

export const CategoriesStep = ({ categories, onToggleCategory }: CategoriesStepProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -50 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">
          Quali servizi ti interessano?
        </h1>
        <p className="text-gray-600">
          Seleziona tutti quelli che ti interessano per personalizzare la tua esperienza
        </p>
      </div>

      <div className="relative">
        {/* Scroll hint text */}
        <div className="text-center mb-4">
          <p className="text-sm text-gray-500 flex items-center justify-center gap-2">
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
            Scorri per vedere altre categorie
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </p>
        </div>
        
        {/* Scrollable container with fade indicators */}
        <div className="relative max-h-96 overflow-y-auto scrollbar-hide">
          {/* Top fade indicator */}
          <div className="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-white to-transparent z-10 pointer-events-none"></div>
          
          {/* Bottom fade indicator */}
          <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-white to-transparent z-10 pointer-events-none"></div>
          
          <div className="grid grid-cols-2 gap-3 pb-4">
            {categories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => onToggleCategory(category.id)}
                className={`p-4 rounded-xl border-2 flex flex-col items-center gap-3 cursor-pointer transition-all ${
                  category.selected
                    ? "border-brand-primary bg-brand-primary/5 shadow-md"
                    : "border-gray-200 bg-white hover:border-gray-300"
                }`}
              >
                <div
                  className={`w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold transition-all ${
                    category.selected
                      ? "bg-brand-primary text-white"
                      : "bg-gray-100 text-gray-500"
                  }`}
                >
                  {category.name.charAt(0)}
                </div>
                <span
                  className={`font-medium text-center text-sm ${
                    category.selected ? "text-brand-primary" : "text-gray-700"
                  }`}
                >
                  {category.name}
                </span>
                {category.selected && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute top-2 right-2"
                  >
                    <svg
                      className="w-5 h-5 text-brand-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </motion.div>
  );
};
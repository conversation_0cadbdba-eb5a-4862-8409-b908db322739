import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { BookingWithDetails } from "@/types/booking";
import BookingStatus from "@/components/booking/BookingStatus";
import BookingInfo from "@/components/booking/BookingInfo";
import BookingActions from "@/components/booking/BookingActions";
import { QRCodeSVG } from "qrcode.react";
import { parseBookingFromDB } from "@/types/booking";
import { ShareButton } from "@/components/sharing/ShareButton";
import { ShareBookingDialog } from "@/components/sharing/ShareBookingDialog";

const BookingDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [booking, setBooking] = useState<BookingWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);

  useEffect(() => {
    const fetchBooking = async () => {
      if (!id) return;

      try {
        const { data: bookingData, error: bookingError } = await supabase
          .from('bookings')
          .select(`
            *,
            deals (
              title,
              images,
              description,
              discounted_price,
              businesses (
                name,
                address
              )
            )
          `)
          .eq('id', id)
          .maybeSingle();

        if (bookingError) {
          toast.error("Errore nel caricamento della prenotazione");
          return;
        }

        if (bookingData) {
          const { data: userData } = await supabase
            .from('user_details')
            .select('first_name, last_name, avatar_url')
            .eq('id', bookingData.user_id)
            .maybeSingle();

          setBooking(parseBookingFromDB({
            ...bookingData,
            user_details: userData || null
          }));
        }
      } catch (error) {
        console.error('Error fetching booking:', error);
        toast.error("Si è verificato un errore");
      } finally {
        setIsLoading(false);
      }
    };

    fetchBooking();
  }, [id]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-pulse">Caricamento...</div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <p className="text-gray-500">Prenotazione non trovata</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50 px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <button onClick={() => navigate(-1)} className="text-gray-700">
              <ArrowLeft className="h-6 w-6" />
            </button>
            <h1 className="text-xl font-semibold ml-4">Dettagli Prenotazione</h1>
          </div>
          <ShareButton
            onClick={() => setIsShareDialogOpen(true)}
            showText={false}
            size="icon"
            variant="outline"
          />
        </div>
      </header>

      <main className="pt-20 pb-24 px-4">
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="h-48 relative">
            <img 
              src={booking.deals?.images?.[0] || `https://picsum.photos/800/600?random=${booking.id}`}
              alt={booking.deals?.title || 'Prenotazione'}
              className="w-full h-full object-cover"
            />
            <div className="absolute top-4 right-4">
              <BookingStatus status={booking.status} />
            </div>
          </div>

          <div className="p-4">
            <h2 className="text-xl font-semibold mb-4">{booking.deals?.title}</h2>
            <BookingInfo booking={booking} />
            
            {booking.qr_data && (
              <div className="mt-6 flex flex-col items-center">
                <p className="text-sm text-gray-600 mb-4">
                  Mostra questo QR code al tuo arrivo
                </p>
                <div className="bg-white p-4 rounded-xl shadow-sm border">
                  <QRCodeSVG 
                    value={JSON.stringify(booking.qr_data)} 
                    size={200} 
                  />
                </div>
              </div>
            )}
            
            <BookingActions booking={booking} onBookingUpdate={setBooking} />
          </div>
        </div>
      </main>
      
      <ShareBookingDialog
        open={isShareDialogOpen}
        onOpenChange={setIsShareDialogOpen}
        bookingId={booking.id}
        dealTitle={booking.deals?.title || 'Prenotazione'}
      />
    </div>
  );
};

export default BookingDetails;

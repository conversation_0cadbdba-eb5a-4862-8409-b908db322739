import { useState } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { Plus, Search, MapPin, Euro, GripVertical, X, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { toast } from "sonner";
import { useDashboardDeals } from "@/hooks/useDashboardDeals";
import { Experience, ExperienceStop } from "@/pages/mains/Experience";
import { CSS } from '@dnd-kit/utilities';

interface ExperienceBuilderProps {
  experience: Experience | null;
  onExperienceChange: (experience: Experience) => void;
}

const SortableStopItem = ({ stop, index, onRemove, onTimeChange }: { 
  stop: ExperienceStop; 
  index: number; 
  onRemove: (stopId: string) => void;
  onTimeChange: (stopId: string, field: 'startTime' | 'endTime', value: string) => void;
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id: stop.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <Card ref={setNodeRef} style={style}>
      <CardContent className="p-4">
        <div className="flex items-start space-x-4">
          <div
            {...attributes}
            {...listeners}
            className="flex items-center space-x-2 text-muted-foreground mt-1 cursor-grab active:cursor-grabbing"
          >
            <GripVertical className="w-4 h-4" />
            <Badge variant="outline">{index + 1}</Badge>
          </div>
          
          <div className="flex-1 space-y-3">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-semibold">{stop.dealTitle}</h3>
                <p className="text-sm text-muted-foreground">{stop.businessName}</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemove(stop.id)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
            
            <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <MapPin className="w-3 h-3" />
                <span>{stop.address}</span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-xs text-muted-foreground">Ora Inizio</label>
                <Input
                  type="time"
                  value={stop.startTime}
                  onChange={(e) => onTimeChange(stop.id, 'startTime', e.target.value)}
                  className="h-8"
                />
              </div>
              <div>
                <label className="text-xs text-muted-foreground">Ora Fine</label>
                <Input
                  type="time"
                  value={stop.endTime}
                  onChange={(e) => onTimeChange(stop.id, 'endTime', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center space-x-1">
                  <Euro className="w-3 h-3" />
                  <span>{stop.price}</span>
                </div>
                <Badge variant="secondary">{stop.category}</Badge>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const ExperienceBuilder = ({ experience, onExperienceChange }: ExperienceBuilderProps) => {
  const [showDealSelector, setShowDealSelector] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDate, setSelectedDate] = useState(experience?.date || new Date().toISOString().split('T')[0]);
  
  const { nearbyDeals, isLoading } = useDashboardDeals();

  // Filter deals based on search query
  const filteredDeals = nearbyDeals.filter(deal => 
    deal.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    deal.business_name?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (!over || !experience || active.id === over.id) return;

    const oldIndex = experience.stops.findIndex(stop => stop.id === active.id);
    const newIndex = experience.stops.findIndex(stop => stop.id === over.id);

    const updatedStops = arrayMove(experience.stops, oldIndex, newIndex).map((stop, index) => ({
      ...stop,
      order: index + 1
    }));

    onExperienceChange({
      ...experience,
      stops: updatedStops,
      updatedAt: new Date().toISOString()
    });
  };

  // Add sensors for better touch/mouse support
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleAddDeal = (deal: any) => {
    if (!experience) return;

    const newStop: ExperienceStop = {
      id: `stop-${Date.now()}`,
      dealId: deal.id,
      businessId: deal.business_id,
      dealTitle: deal.title,
      businessName: deal.business_name || deal.businesses?.name || 'Business sconosciuto',
      address: deal.address || deal.businesses?.address || 'Indirizzo non disponibile',
      latitude: deal.latitude || deal.businesses?.latitude || 0,
      longitude: deal.longitude || deal.businesses?.longitude || 0,
      startTime: '09:00',
      endTime: '10:00',
      estimatedDuration: 60,
      price: deal.discounted_price || 0,
      category: deal.category || 'Generale',
      order: experience.stops.length + 1
    };

    const updatedExperience = {
      ...experience,
      stops: [...experience.stops, newStop],
      totalPrice: experience.totalPrice + newStop.price,
      totalDuration: experience.totalDuration + newStop.estimatedDuration,
      updatedAt: new Date().toISOString()
    };

    onExperienceChange(updatedExperience);
    setShowDealSelector(false);
    toast.success("Attività aggiunta al programma!");
  };

  const handleRemoveStop = (stopId: string) => {
    if (!experience) return;

    const stopToRemove = experience.stops.find(stop => stop.id === stopId);
    if (!stopToRemove) return;

    const updatedStops = experience.stops
      .filter(stop => stop.id !== stopId)
      .map((stop, index) => ({ ...stop, order: index + 1 }));

    const updatedExperience = {
      ...experience,
      stops: updatedStops,
      totalPrice: experience.totalPrice - stopToRemove.price,
      totalDuration: experience.totalDuration - stopToRemove.estimatedDuration,
      updatedAt: new Date().toISOString()
    };

    onExperienceChange(updatedExperience);
    toast.success("Attività rimossa dal progtamma");
  };

  const handleTimeChange = (stopId: string, field: 'startTime' | 'endTime', value: string) => {
    if (!experience) return;

    const updatedStops = experience.stops.map(stop => 
      stop.id === stopId ? { ...stop, [field]: value } : stop
    );

    onExperienceChange({
      ...experience,
      stops: updatedStops,
      updatedAt: new Date().toISOString()
    });
  };

  const handleDateChange = (date: string) => {
    setSelectedDate(date);
    if (experience) {
      onExperienceChange({
        ...experience,
        date,
        updatedAt: new Date().toISOString()
      });
    }
  };

  if (!experience) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Nessuna esperienza selezionato</h3>
          <p className="text-muted-foreground">Crea una nuova esperienza per iniziare</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Experience Header */}
      <Card>
        <CardHeader>
          <CardTitle>Dettagli programma</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">Nome Programma</label>
            <Input
              value={experience.name}
              onChange={(e) => onExperienceChange({
                ...experience,
                name: e.target.value,
                updatedAt: new Date().toISOString()
              })}
              placeholder="Nome dell'esperienza"
            />
          </div>
          
          <div>
            <label className="text-sm font-medium">Data</label>
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 text-muted-foreground" />
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => handleDateChange(e.target.value)}
              />
            </div>
          </div>

          {experience.description !== undefined && (
            <div>
              <label className="text-sm font-medium">Descrizione</label>
              <Input
                value={experience.description}
                onChange={(e) => onExperienceChange({
                  ...experience,
                  description: e.target.value,
                  updatedAt: new Date().toISOString()
                })}
                placeholder="Descrizione dell'esperienza"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Experience Timeline */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Timeline Programma</CardTitle>
          <Button onClick={() => setShowDealSelector(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Aggiungi Attività
          </Button>
        </CardHeader>
        <CardContent>
          {experience.stops.length === 0 ? (
            <div className="text-center py-8">
              <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Nessuna attività aggiunta</h3>
              <p className="text-muted-foreground mb-4">
                Inizia aggiungendo la tua prima attività al programma
              </p>
              <Button onClick={() => setShowDealSelector(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Aggiungi Prima Attività
              </Button>
            </div>
          ) : (
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={experience.stops.map(stop => stop.id)}
                strategy={verticalListSortingStrategy}
              >
                <div className="space-y-4">
                  {experience.stops.map((stop, index) => (
                    <SortableStopItem 
                      key={stop.id} 
                      stop={stop} 
                      index={index}
                      onRemove={handleRemoveStop}
                      onTimeChange={handleTimeChange}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          )}
        </CardContent>
      </Card>

      {/* Deal Selector Dialog */}
      <Dialog open={showDealSelector} onOpenChange={setShowDealSelector}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Seleziona Attività</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Search className="w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Cerca attività..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-64 bg-muted animate-pulse rounded-lg" />
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                {filteredDeals.map((deal) => (
                  <Card key={deal.id} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div>
                          <h3 className="font-semibold line-clamp-1">{deal.title}</h3>
                          <p className="text-sm text-muted-foreground line-clamp-1">
                            {deal.business_name || deal.businesses?.name}
                          </p>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <div className="text-lg font-bold text-primary">
                            €{deal.discounted_price}
                          </div>
                          <Button
                            size="sm"
                            onClick={() => handleAddDeal(deal)}
                          >
                            Aggiungi
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
            
            {!isLoading && filteredDeals.length === 0 && (
              <div className="text-center py-8">
                <Search className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Nessuna attività trovata</h3>
                <p className="text-muted-foreground">
                  Prova a cambiare i termini di ricerca
                </p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ExperienceBuilder;

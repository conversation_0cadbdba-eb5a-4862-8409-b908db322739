
import { motion } from "framer-motion";

interface VoiceRecordingFeedbackProps {
  isRecording: boolean;
}

const VoiceRecordingFeedback = ({ isRecording }: VoiceRecordingFeedbackProps) => {
  if (!isRecording) return null;

  return (
    <div className="fixed inset-0 bottom-16 flex items-center justify-center bg-black/30 backdrop-blur-sm z-40">
      <motion.div 
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        exit={{ scale: 0 }}
        className="relative"
      >
        {/* Cerchi animati di background */}
        {[1, 2, 3].map((index) => (
          <motion.div
            key={index}
            className="absolute inset-0 rounded-full border-2 border-brand-primary"
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.5, 0, 0.5],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: index * 0.3,
            }}
          />
        ))}

        {/* Cerchio centrale con microfono */}
        <motion.div
          className="relative w-24 h-24 rounded-full bg-gradient-to-r from-brand-primary to-brand-secondary flex items-center justify-center"
          animate={{
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          {/* Onde sonore animate */}
          <motion.div
            className="absolute inset-0 flex items-center justify-center"
            animate={{
              rotate: 360,
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "linear",
            }}
          >
            {[...Array(12)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-6 bg-white/20"
                style={{
                  transform: `rotate(${i * 30}deg)`,
                  transformOrigin: "50% 50%",
                }}
                animate={{
                  height: ["24px", "16px", "24px"],
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: i * 0.1,
                }}
              />
            ))}
          </motion.div>

          {/* Icona del microfono */}
          <motion.svg
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="w-8 h-8 text-white"
            animate={{
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
            }}
          >
            <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z" />
            <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
            <line x1="12" x2="12" y1="19" y2="22" />
          </motion.svg>
        </motion.div>

        {/* Testo pulsante */}
        <motion.p 
          className="absolute -bottom-12 left-1/2 -translate-x-1/2 text-white font-medium text-center whitespace-nowrap"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
        >
        
        </motion.p>
      </motion.div>
    </div>
  );
};

export default VoiceRecordingFeedback;

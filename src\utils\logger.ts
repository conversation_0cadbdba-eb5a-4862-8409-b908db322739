type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogOptions {
  tags?: string[];
  metadata?: Record<string, any>;
}

/**
 * Logger utility class that centralizes logging functionality.
 * This allows for easy extension to other logging systems like Sentry.
 */
export class Logger {
  private static instance: Logger;
  private isEnabled: boolean = true;
  private logLevel: LogLevel = 'info';
  
  private hasExternalServices: boolean = false;

  private constructor() {}

  /**
   * Get the singleton instance of Logger
   */
  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * Initialize logger with environment-specific settings
   */
  public static initializeLogger(options: {
    enabled?: boolean;
    logLevel?: LogLevel;

    externalServices?: boolean;
  } = {}): void {
    const logger = Logger.getInstance();
    
    // Apply settings with defaults
    if (options.enabled !== undefined) logger.setEnabled(options.enabled);
    if (options.logLevel) logger.setLogLevel(options.logLevel);
  
    if (options.externalServices !== undefined) logger.configureExternalServices(options.externalServices);
    
    // Log initialization
    logger.info('Logger initialized', { 
      tags: ['system'],
      metadata: { 
        logLevel: logger.logLevel,
        
        externalServices: logger.hasExternalServices
      }
    });
  }

  /**
   * Enable or disable logging
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * Set the minimum log level
   */
  public setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  /**
   * Log a debug message
   * @param args - Arguments in console.log format or message with options
   */
  public debug(...args: any[]): void {
    this.processArgs('debug', args);
  }

  /**
   * Log an info message
   * @param args - Arguments in console.log format or message with options
   */
  public info(...args: any[]): void {
    this.processArgs('info', args);
  }

  /**
   * Log a warning message
   * @param args - Arguments in console.log format or message with options
   */
  public warn(...args: any[]): void {
    this.processArgs('warn', args);
  }

  /**
   * Log an error message
   * @param args - Arguments in console.log format or message with options
   */
  public error(...args: any[]): void {
    this.processArgs('error', args);
  }

  /**
   * Process arguments to support both formats:
   * - console.log style: logger.info('User', user, 'logged in')
   * - structured style: logger.info('User logged in', { metadata: { user } })
   */
  private processArgs(level: LogLevel, args: any[]): void {
    // If no args, do nothing
    if (args.length === 0) return;

    // Check if the last argument is LogOptions
    const lastArg = args[args.length - 1];
    const hasOptions = lastArg && 
      (typeof lastArg === 'object' && 
       (lastArg.tags || lastArg.metadata) && 
       !(lastArg instanceof Error));

    let message: any;
    let options: LogOptions | undefined;

    if (args.length === 1 && args[0] instanceof Error) {
      // Handle single Error argument
      const error = args[0] as Error;
      message = error.message;
      options = {
        metadata: {
          stack: error.stack,
          name: error.name
        }
      };
    } else if (hasOptions && args.length === 2 && typeof args[0] === 'string') {
      // Handle structured format: (message, options)
      message = args[0];
      options = args[args.length - 1] as LogOptions;
    } else {
      // Handle console.log style format with multiple arguments
      // Convert all non-object arguments to strings, extract data from objects
      let metadata: Record<string, any> = {};
      
      message = args
        .map((arg, index) => {
          if (arg === null) return 'null';
          if (arg === undefined) return 'undefined';
          
          if (typeof arg === 'object' && !(arg instanceof Error)) {
            const objKey = `obj_${index}`;
            metadata[objKey] = arg;
            return `%o`; // Placeholder for object in console methods
          }
          
          return String(arg);
        })
        .join(' ');
      
      options = { metadata };
    }

    // Handle Error objects specially
    if (message !== null &&  typeof message === 'object' && message instanceof Error) {
      this.log(level, message.message, {
        ...options,
        metadata: {
          ...options?.metadata,
          stack: message.stack,
          name: message.name
        }
      });
    } else {
      this.log(level, message, options);
    }
  }

  /**
   * Performance optimization for the logger
   */
  private log(level: LogLevel, message: string, options?: LogOptions): void {
    // Early return if logging is disabled or below minimum level
    if (!this.isEnabled) return;
    
    const levelPriority = { 'debug': 0, 'info': 1, 'warn': 2, 'error': 3 };
    if (levelPriority[level] < levelPriority[this.logLevel]) return;

   
  
    // Only generate timestamp and prepare data if we're actually logging
    const timestamp = new Date().toISOString();
    const metadata = options?.metadata || {};
    const metadataArgs = Object.keys(metadata).length > 0 ? [metadata] : [];
   
    // Console logging with minimal processing
    switch (level) {
      case 'debug':
        console.debug(`[${timestamp}] ${message}`, ...metadataArgs);
        break;
      case 'info':
        console.info(`[${timestamp}] ${message}`, ...metadataArgs);
        break;
      case 'warn':
        console.warn(`[${timestamp}] ${message}`, ...metadataArgs);
        break;
      case 'error':
        console.error(`[${timestamp}] ${message}`, ...metadataArgs);
        break;
    }

    // Only prepare full logData object if external services are configured
    if (this.hasExternalServices) {
      const logData = {
        timestamp,
        level,
        message,
        ...options
        
      };
      this.sendToExternalServices(logData);
    }
  }

  /**
   * Configure external logging services
   */
  public configureExternalServices(enabled: boolean): void {
    this.hasExternalServices = enabled;
  }

  /**
   * Send logs to external services (placeholder for future implementation)
   */
  private sendToExternalServices(logData: any): void {
    // Example: Integration with Sentry or other logging services
    // if (Sentry.isInitialized()) {
    //   Sentry.captureMessage(logData.message, {
    //     level: logData.level,
    //     tags: logData.tags,
    //     extra: logData.metadata
    //   });
    // }
  }

 
}

// Create the singleton instance
export const logger = Logger.getInstance();

// Add to global namespace
declare global {
  interface Window {
    logger: typeof logger;
  }
  var logger: Logger;
}

// Assign to global object
if (typeof window !== 'undefined') {
  window.logger = logger;
} else {
  (globalThis as any).logger = logger;
}

// NEVER REMOVE
// Example usage:


// // 1. Console.log style (direct replacement for existing code)
// logger.info("Fetching business deals for business ID:", businessId);
// logger.debug("User data:", userData, "Status:", status);
// logger.warn("API rate limit:", rateLimit, "requests remaining");
// logger.error("Failed to process transaction:", transactionId, error);

// // 2. Structured style with LogOptions
// logger.info('Payment processed', {
//   tags: ['payment', 'transaction'],
//   metadata: { 
//     amount: 50.99, 
//     currency: 'EUR',
//     userId: 'user-123'
//   }
// });

// // 3. Error objects are handled specially
// try {
//   throw new Error('Database connection failed');
// } catch (error) {
//   // Both of these work:
//   logger.error(error); // Simple version
//   logger.error(error, {  // With additional context
//     tags: ['database'],
//     metadata: { 
//       connectionId: 'db-conn-1',
//       retryCount: 3
//     }
//   });
// }

// // 4. Mixed objects and primitives
// const user = { id: 123, name: 'John' };
// logger.info("User logged in:", user, "at", new Date());














# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/be08eb59-168f-411b-b6de-7d852ac675a3

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/be08eb59-168f-411b-b6de-7d852ac675a3) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with .

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## Features

### Search Functionality
The application includes a robust search feature that allows users to filter deals by:
- Deal title
- Business name
- Business address

The search component (`SearchBar`) is reusable across the application and can be easily integrated into any page by passing the search query state and a callback function:

```tsx
// Example usage
const [searchQuery, setSearchQuery] = useState<string>("");

<SearchBar 
  value={searchQuery}
  onChange={setSearchQuery}
  placeholder="Custom placeholder text" // Optional
/>
```

### Category Filtering
Users can also filter deals by categories, allowing for a more refined search experience.

### Map View
The application offers both list and map views for deals, providing users with different ways to discover offers.

#### Custom Business Marker
The map view includes a custom business marker component (`CustomBusinessMarker`) that enhances the user experience by:
- Displaying business name and deal count in a visually appealing way
- Expanding to show business details and available deals when clicked
- Providing interactive hover and click animations
- Supporting custom click handlers for both businesses and individual deals

```tsx
// Example usage
<CustomBusinessMarker 
  business={businessData}
  onBusinessClick={handleBusinessClick}
  onDealClick={handleDealClick}
/>
```

### Admin Data Generator

The application includes a synthetic data generation tool that allows you to:

1. Generate random businesses for selected categories
2. Generate random deals for selected businesses

The Admin Data Generator includes these key features:
- City selection for businesses: Generate businesses with realistic addresses and coordinates within specific Italian cities
- Category selection for businesses: Create businesses in your chosen category
- Quantity control: Generate multiple businesses or deals at once
- Realistic data generation: Creates sensible data for testing including business details, pricing, and schedules

To access this feature:
1. Navigate to the Profile page
2. Click on "Admin Data Generator" in the menu
3. Select a city, category, and number of businesses to generate
4. Select a business and number of deals to generate

This feature is useful for testing and demonstration purposes.

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/be08eb59-168f-411b-b6de-7d852ac675a3) and click on Share -> Publish.

## I want to use a custom domain - is that possible?

We don't support custom domains (yet). If you want to deploy your project under your own domain then we recommend using Netlify. Visit our docs for more details: [Custom domains](https://docs.lovable.dev/tips-tricks/custom-domain/)

# CatchUp - Multi-Day Deal Availability

This document outlines the implementation of multi-day deal availability in the CatchUp application.

## Overview

CatchUp deals can span multiple days, and each day has its own availability. This implementation provides a user-friendly way to display and interact with availability information across different parts of the application.

## Key Components

### 1. Availability Service

The `availabilityService` provides methods for fetching and calculating availability:

- `getAvailableDates(dealId)`: Gets all available dates for a deal
- `getAvailabilityForDate(dealId, date)`: Gets availability for a specific date
- `getAvailabilitySummary(dealId)`: Gets a summary of availability across all dates

### 2. UI Components

- **AvailabilityStatus**: Displays a badge indicating availability status (Available, Limited, Sold Out)
- **DateAvailabilitySelector**: Allows users to select a date and view availability
- **AvailabilitySummary**: Shows a summary of availability across all dates

### 3. Integration Points

- **DealCard**: Shows availability summary with option to expand and select dates
- **DealDetailPage**: Prominently displays date selector with availability
- **BookingPage**: Uses date selector as first step in booking process

## Data Structure

### Deals Table

```
deals
├── id: string
├── title: string
├── description: string
├── price: number
├── original_price: number
├── image_url: string
├── business_name: string
├── business_logo: string
├── time_slot: object/array (contains all valid dates)
└── capacity: number (seats available per day)
```

### Time Slot Bookings Table

```
time_slot_bookings
├── id: string
├── deal_id: string (reference to deals.id)
├── user_id: string (reference to users.id)
├── booking_date: string (ISO date string)
├── count: number (number of seats booked)
├── status: string ('confirmed', 'cancelled', etc.)
└── created_at: string (ISO datetime string)
```

## User Flow

1. **Browse Deals**: User sees availability status on deal cards
2. **View Deal Details**: User selects a date to check availability
3. **Book Deal**: User confirms date and quantity, then completes booking

## Implementation Details

### Availability Calculation

Availability for a date is calculated as:
```
available = deal.capacity - sum(bookings.count)
```

Where `bookings` are filtered by `deal_id` and `booking_date`.

### Availability Status

- **Available**: At least one seat is available
- **Limited**: Less than 20% of capacity is available
- **Sold Out**: No seats available

## Performance Considerations

- Availability data is fetched only when needed
- Summary information is used in list views to minimize API calls
- Detailed availability is loaded on demand when user interacts with components

## Future Improvements

- Implement caching with React Query
- Add batch fetching for multiple deals
- Implement real-time updates using Supabase subscriptions

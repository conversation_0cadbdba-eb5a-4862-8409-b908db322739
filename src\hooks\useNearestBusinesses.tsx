
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useLocationManagement } from "./location/useLocationManagement";

interface Business {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  address: string;
  distance: number;
  distanceText: string;
  deal_count: number;
  photos: string[] | null;
}

interface UseNearestBusinessesOptions {
  maxDistance_meters?: number;
  limit?: number;
  transportMode?: "DRIVING" | "WALKING" | "BICYCLING" | "TRANSIT";
  useDirections?: boolean;
  require_deals?: boolean;
}

export function useNearestBusinesses(options: UseNearestBusinessesOptions = {}) {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { userLocation } = useLocationManagement();

  useEffect(() => {
    const fetchNearestBusinesses = async () => {
      if (!userLocation) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const { data, error } = await supabase.functions.invoke(
          "get-nearest-businesses",
          {
            body: {
              latitude: userLocation.lat,
              longitude: userLocation.lng,
              maxDistance_meters: options.maxDistance_meters || 5000,
              limit: options.limit || 5,
              transportMode: options.transportMode || "DRIVING",
              useDirections: options.useDirections || false,
              require_deals: options.require_deals || false,
            },
          }
        );

        if (error) {
          throw error;
        }

        setBusinesses(data.businesses || []);
      } catch (error) {
        console.error("Error fetching nearest businesses:", error);
        setError("Errore nel caricamento delle attività vicine");
      } finally {
        setLoading(false);
      }
    };

    fetchNearestBusinesses();
  }, [
    userLocation,
    options.maxDistance_meters,
    options.limit,
    options.transportMode,
    options.useDirections,
    options.require_deals,
  ]);

  return { businesses, loading, error };
}

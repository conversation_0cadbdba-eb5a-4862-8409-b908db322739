import { Tag } from "lucide-react";
import FlexibleDealCarousel from "./FlexibleDealCarousel";
import { useFlexibleDeals } from "@/hooks/useFlexibleDeals";

interface CategoryDealsCarouselProps {
  className?: string;
}

const CategoryDealsCarousel = ({ className = "" }: CategoryDealsCarouselProps) => {
  const { sections, isLoading } = useFlexibleDeals('categories', {
    maxSections: 6,
    dealsPerSection: 10,
    userPreferences: true
  });

  return (
    <FlexibleDealCarousel
      title="Scopri per Categoria"
      titleIcon={Tag}
      sections={sections}
      isLoading={isLoading}
      className={className}
    />
  );
};

export default CategoryDealsCarousel;
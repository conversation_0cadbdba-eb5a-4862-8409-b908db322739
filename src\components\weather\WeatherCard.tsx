import { <PERSON><PERSON>resh<PERSON><PERSON>, MapPin, Wind, Droplets, Eye, Thermometer, Gauge, Sun, Cloud, CloudRain, CloudSnow, Zap } from 'lucide-react';
import { WeatherData, weatherService } from '@/services/weatherService';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface WeatherCardProps {
  weatherData: WeatherData;
  onRefresh: () => void;
  isLoading?: boolean;
  lastUpdated?: Date | null;
}

const getWeatherIcon = (condition: string, size = 'h-20 w-20') => {
  const conditionLower = condition.toLowerCase();
  
  if (conditionLower.includes('sun') || conditionLower.includes('clear')) {
    return <Sun className={cn(size, 'text-yellow-200')} />;
  } else if (conditionLower.includes('rain') || conditionLower.includes('drizzle')) {
    return <CloudRain className={cn(size, 'text-blue-200')} />;
  } else if (conditionLower.includes('snow')) {
    return <CloudSnow className={cn(size, 'text-blue-100')} />;
  } else if (conditionLower.includes('thunder') || conditionLower.includes('storm')) {
    return <Zap className={cn(size, 'text-yellow-300')} />;
  } else {
    return <Cloud className={cn(size, 'text-gray-200')} />;
  }
};

const getEnhancedWeatherGradient = (condition: string) => {
  const conditionLower = condition.toLowerCase();
  
  if (conditionLower.includes('sun') || conditionLower.includes('clear')) {
    return 'from-amber-400 via-orange-500 to-yellow-600';
  } else if (conditionLower.includes('cloud')) {
    return 'from-slate-400 via-slate-500 to-blue-600';
  } else if (conditionLower.includes('rain') || conditionLower.includes('drizzle')) {
    return 'from-blue-500 via-indigo-600 to-purple-700';
  } else if (conditionLower.includes('snow')) {
    return 'from-blue-200 via-blue-400 to-indigo-600';
  } else if (conditionLower.includes('thunder') || conditionLower.includes('storm')) {
    return 'from-purple-600 via-indigo-800 to-slate-900';
  } else if (conditionLower.includes('mist') || conditionLower.includes('fog')) {
    return 'from-gray-300 via-gray-500 to-slate-600';
  }
  
  return 'from-blue-400 via-blue-600 to-indigo-700';
};

export const WeatherCard = ({ weatherData, onRefresh, isLoading, lastUpdated }: WeatherCardProps) => {
  const { current, location } = weatherData;
  const gradientClass = getEnhancedWeatherGradient(current.condition);
  const isOutdoorFriendly = weatherService.isOutdoorFriendly(weatherData);
  const WeatherIcon = getWeatherIcon(current.condition);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('it-IT', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className="relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/20 to-indigo-100/20 rounded-xl"></div>
      
      <Card className={cn(
        "relative overflow-hidden border-0 shadow-xl backdrop-blur-sm",
        `bg-gradient-to-br ${gradientClass}`,
        "before:absolute before:inset-0 before:bg-black/10 before:backdrop-blur-sm"
      )}>
        <CardContent className="relative z-10 p-8 text-white">
          {/* Decorative Elements */}
          <div className="absolute top-4 right-4 opacity-10">
            {getWeatherIcon(current.condition, 'h-32 w-32')}
          </div>
          
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="bg-white/20 rounded-full p-2">
                <MapPin className="h-5 w-5" />
              </div>
              <div>
                <span className="text-lg font-semibold">{location.city}</span>
                <div className="text-sm opacity-90">{location.country}</div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              {isOutdoorFriendly && (
                <Badge variant="secondary" className="bg-green-500/20 text-white border-green-400/30 hover:bg-green-500/30">
                  <Sun className="h-3 w-3 mr-1" />
                  Ideale per uscire
                </Badge>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={onRefresh}
                disabled={isLoading}
                className="text-white hover:bg-white/20 h-10 w-10 p-0 rounded-full bg-white/10"
              >
                <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
              </Button>
            </div>
          </div>

          {/* Main Weather Display */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex-1">
              <div className="text-6xl font-light mb-2 tracking-tight">
                {weatherService.formatTemperature(current.temp)}
              </div>
              <div className="text-lg opacity-90 mb-1">
                Percepita {weatherService.formatTemperature(current.feelsLike)}
              </div>
              <div className="text-lg font-medium capitalize">
                {current.description}
              </div>
            </div>
            
            <div className="flex flex-col items-center">
              <div className="mb-2">
                {WeatherIcon}
              </div>
            </div>
          </div>

          {/* Weather Details Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center gap-3 mb-2">
                <Wind className="h-5 w-5 text-blue-200" />
                <span className="text-sm font-medium">Vento</span>
              </div>
              <div className="text-lg font-semibold">
                {current.windSpeed.toFixed(1)} m/s
              </div>
              <div className="text-sm opacity-80">
                {weatherService.getWindDirection(current.windDirection)}
              </div>
            </div>
            
            <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center gap-3 mb-2">
                <Droplets className="h-5 w-5 text-blue-200" />
                <span className="text-sm font-medium">Umidità</span>
              </div>
              <div className="text-lg font-semibold">{current.humidity}%</div>
            </div>
            
            <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center gap-3 mb-2">
                <Gauge className="h-5 w-5 text-blue-200" />
                <span className="text-sm font-medium">Pressione</span>
              </div>
              <div className="text-lg font-semibold">{current.pressure}</div>
              <div className="text-sm opacity-80">hPa</div>
            </div>
            
            <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center gap-3 mb-2">
                <Eye className="h-5 w-5 text-blue-200" />
                <span className="text-sm font-medium">Visibilità</span>
              </div>
              <div className="text-lg font-semibold">{current.visibility.toFixed(1)}</div>
              <div className="text-sm opacity-80">km</div>
            </div>
          </div>

          {/* UV Index */}
          {current.uvIndex > 0 && (
            <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm mb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Thermometer className="h-5 w-5 text-orange-200" />
                  <span className="text-sm font-medium">Indice UV</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-lg font-semibold">{current.uvIndex.toFixed(1)}</span>
                  {current.uvIndex > 7 && (
                    <Badge variant="destructive" className="bg-red-500/20 text-white border-red-400/30">
                      Alto
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Last Updated */}
          {lastUpdated && (
            <div className="text-sm opacity-75 text-center">
              Ultimo aggiornamento: {formatTime(lastUpdated)}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
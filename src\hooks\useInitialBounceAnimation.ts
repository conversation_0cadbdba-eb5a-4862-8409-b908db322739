
import { useEffect, useState } from 'react';

interface UseInitialBounceAnimationProps {
  delay?: number;
  duration?: number;
  enabled?: boolean;
}

export const useInitialBounceAnimation = ({
  delay = 1000,
  duration = 800,
  enabled = true
}: UseInitialBounceAnimationProps = {}) => {
  const [shouldAnimate, setShouldAnimate] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [showSwipeIcon, setShowSwipeIcon] = useState(false);

  useEffect(() => {
    if (!enabled) return;

    const timer = setTimeout(() => {
      setShouldAnimate(true);
      setIsAnimating(true);
      setShowSwipeIcon(true);
      
      // Hide swipe icon after animation
      const iconTimer = setTimeout(() => {
        setShowSwipeIcon(false);
      }, duration - 200);
      
      // Reset animation state after duration
      const resetTimer = setTimeout(() => {
        setIsAnimating(false);
        setShouldAnimate(false);
      }, duration);

      return () => {
        clearTimeout(iconTimer);
        clearTimeout(resetTimer);
      };
    }, delay);

    return () => clearTimeout(timer);
  }, [delay, duration, enabled]);

  return {
    shouldAnimate,
    isAnimating,
    showSwipeIcon,
    animationClasses: shouldAnimate 
      ? 'animate-bounce-hint' 
      : '',
  };
};

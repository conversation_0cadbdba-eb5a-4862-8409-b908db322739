
-- Crea la tabella per i gruppi
CREATE TABLE public.groups (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  avatar_url TEXT,
  created_by UUID REFERENCES auth.users NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Crea la tabella per i membri dei gruppi
CREATE TABLE public.group_members (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  group_id UUID REFERENCES public.groups(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users NOT NULL,
  role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('admin', 'member')),
  joined_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(group_id, user_id)
);

-- Crea la tabella per le condivisioni di offerte nei gruppi
CREATE TABLE public.group_deal_shares (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  group_id UUID REFERENCES public.groups(id) ON DELETE CASCADE NOT NULL,
  deal_id UUID REFERENCES public.deals(id) ON DELETE CASCADE NOT NULL,
  shared_by UUID REFERENCES auth.users NOT NULL,
  message TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(group_id, deal_id, shared_by)
);

-- Crea la tabella per le condivisioni di prenotazioni nei gruppi
CREATE TABLE public.group_booking_shares (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  group_id UUID REFERENCES public.groups(id) ON DELETE CASCADE NOT NULL,
  booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE NOT NULL,
  shared_by UUID REFERENCES auth.users NOT NULL,
  message TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(group_id, booking_id, shared_by)
);

-- Abilita RLS per tutte le tabelle
ALTER TABLE public.groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_deal_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_booking_shares ENABLE ROW LEVEL SECURITY;

-- Policy per i gruppi: gli utenti possono vedere solo i gruppi di cui sono membri
CREATE POLICY "Users can view groups they are members of" 
  ON public.groups 
  FOR SELECT 
  USING (
    id IN (
      SELECT group_id FROM public.group_members 
      WHERE user_id = auth.uid()
    )
  );

-- Policy per creare gruppi: tutti gli utenti autenticati possono creare gruppi
CREATE POLICY "Authenticated users can create groups" 
  ON public.groups 
  FOR INSERT 
  WITH CHECK (auth.uid() = created_by);

-- Policy per aggiornare gruppi: solo i creatori e admin possono modificare
CREATE POLICY "Group creators and admins can update groups" 
  ON public.groups 
  FOR UPDATE 
  USING (
    created_by = auth.uid() OR 
    id IN (
      SELECT group_id FROM public.group_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Policy per eliminare gruppi: solo i creatori possono eliminare
CREATE POLICY "Group creators can delete groups" 
  ON public.groups 
  FOR DELETE 
  USING (created_by = auth.uid());

-- Policy per i membri: possono vedere i membri dei loro gruppi
CREATE POLICY "Users can view members of their groups" 
  ON public.group_members 
  FOR SELECT 
  USING (
    group_id IN (
      SELECT group_id FROM public.group_members 
      WHERE user_id = auth.uid()
    )
  );

-- Policy per aggiungere membri: solo admin e creatori possono aggiungere membri
CREATE POLICY "Group admins can add members" 
  ON public.group_members 
  FOR INSERT 
  WITH CHECK (
    group_id IN (
      SELECT g.id FROM public.groups g
      LEFT JOIN public.group_members gm ON g.id = gm.group_id
      WHERE g.created_by = auth.uid() OR (gm.user_id = auth.uid() AND gm.role = 'admin')
    )
  );

-- Policy per rimuovere membri: admin, creatori, o l'utente stesso possono rimuovere
CREATE POLICY "Group admins and users can remove members" 
  ON public.group_members 
  FOR DELETE 
  USING (
    user_id = auth.uid() OR
    group_id IN (
      SELECT g.id FROM public.groups g
      LEFT JOIN public.group_members gm ON g.id = gm.group_id
      WHERE g.created_by = auth.uid() OR (gm.user_id = auth.uid() AND gm.role = 'admin')
    )
  );

-- Policy per le condivisioni di offerte: i membri possono vedere e creare condivisioni
CREATE POLICY "Group members can view deal shares" 
  ON public.group_deal_shares 
  FOR SELECT 
  USING (
    group_id IN (
      SELECT group_id FROM public.group_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Group members can share deals" 
  ON public.group_deal_shares 
  FOR INSERT 
  WITH CHECK (
    auth.uid() = shared_by AND
    group_id IN (
      SELECT group_id FROM public.group_members 
      WHERE user_id = auth.uid()
    )
  );

-- Policy per le condivisioni di prenotazioni: i membri possono vedere e creare condivisioni
CREATE POLICY "Group members can view booking shares" 
  ON public.group_booking_shares 
  FOR SELECT 
  USING (
    group_id IN (
      SELECT group_id FROM public.group_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Group members can share bookings" 
  ON public.group_booking_shares 
  FOR INSERT 
  WITH CHECK (
    auth.uid() = shared_by AND
    group_id IN (
      SELECT group_id FROM public.group_members 
      WHERE user_id = auth.uid()
    )
  );

-- Trigger per aggiornare updated_at sui gruppi
CREATE OR REPLACE FUNCTION update_groups_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_groups_updated_at_trigger
  BEFORE UPDATE ON public.groups
  FOR EACH ROW
  EXECUTE FUNCTION update_groups_updated_at();

-- Trigger per aggiungere automaticamente il creatore come admin del gruppo
CREATE OR REPLACE FUNCTION add_group_creator_as_admin()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.group_members (group_id, user_id, role)
  VALUES (NEW.id, NEW.created_by, 'admin');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER add_group_creator_as_admin_trigger
  AFTER INSERT ON public.groups
  FOR EACH ROW
  EXECUTE FUNCTION add_group_creator_as_admin();

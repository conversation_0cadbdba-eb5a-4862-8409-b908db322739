# CatchUp Edge Functions - Quick Reference

## 📋 Overview

This directory contains comprehensive documentation for two critical Supabase edge functions used in the CatchUp application.

## 📁 Documentation Files

| File | Description |
|------|-------------|
| [`supabase-edge-functions-documentation.md`](./supabase-edge-functions-documentation.md) | Complete API documentation, specifications, and technical details |
| [`supabase-edge-functions-uml-diagrams.md`](./supabase-edge-functions-uml-diagrams.md) | UML diagrams, sequence flows, and visual architecture |

## 🚀 Quick Function Summary

### 🏢 get-nearest-businesses
**Purpose**: Find businesses near a location  
**Best for**: Business discovery, location-based search  
**Key feature**: Fast responses with 10-minute caching  

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/get-nearest-businesses' \
  -H 'Content-Type: application/json' \
  -d '{"latitude": 45.4642, "longitude": 9.1900, "limit": 10}'
```

### 🎯 get-nearby-deals-by-location
**Purpose**: Find available deals for specific dates/times  
**Best for**: Deal search, time-based booking  
**Key feature**: Advanced filtering with pagination  

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/get-nearby-deals-by-location' \
  -H 'Content-Type: application/json' \
  -d '{"date": "2024-01-15", "time": "14:30", "location": {"lat": 45.4642, "lng": 9.1900}}'
```

## ⚡ When to Use Which Function

| Scenario | Use Function |
|----------|--------------|
| "Show me restaurants nearby" | `get-nearest-businesses` |
| "Find deals for Friday at 2 PM" | `get-nearby-deals-by-location` |
| "Discover businesses in this area" | `get-nearest-businesses` |
| "Available appointments tomorrow" | `get-nearby-deals-by-location` |
| "Browse local services" | `get-nearest-businesses` |
| "Book a specific time slot" | `get-nearby-deals-by-location` |

## 🔧 Technical Differences

| Aspect | get-nearest-businesses | get-nearby-deals-by-location |
|--------|------------------------|------------------------------|
| **Response Time** | ⚡ Fast (cached) | ⏱️ Variable |
| **Data Freshness** | 🕒 10min cache | 🔄 Real-time |
| **Complexity** | 🟢 Simple | 🟡 Complex |
| **Pagination** | 📄 Basic limit | 📄 Cursor-based |

## 🛠️ Development Setup

### Prerequisites
```bash
# Environment variables needed
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### Database Dependencies
- **get-nearest-businesses**: Requires `get_nearest_businesses_postgis()` RPC function
- **get-nearby-deals-by-location**: Requires `get_nearby_deals()` stored procedure

## 📊 Performance Notes

### get-nearest-businesses
- ✅ **Pros**: Fast responses, reduced DB load
- ⚠️ **Cons**: Potentially stale data (10min cache)
- 🎯 **Optimization**: Geographic cache keys

### get-nearby-deals-by-location
- ✅ **Pros**: Real-time data, advanced filtering
- ⚠️ **Cons**: Higher latency, no caching
- 🎯 **Optimization**: Query result caching

## 🐛 Common Issues & Solutions

### get-nearest-businesses
```javascript
// ❌ Missing required parameters
{"error": "Latitudine e longitudine sono obbligatorie"}

// ✅ Correct request
{
  "latitude": 45.4642,
  "longitude": 9.1900,
  "limit": 10
}
```

### get-nearby-deals-by-location
```javascript
// ❌ Missing required date
{"error": "Missing required parameter", "details": "Date is required"}

// ✅ Correct request
{
  "date": "2024-01-15",
  "location": {"lat": 45.4642, "lng": 9.1900}
}
```

## 📈 Monitoring

Both functions include comprehensive logging for:
- Request parameters and validation
- Database query performance
- Cache hit/miss rates (businesses function)
- Error tracking and response times

## 🔮 Future Enhancements

### Planned Features
- [ ] Google Maps Directions API integration (get-nearest-businesses)
- [ ] Redis caching layer (get-nearby-deals-by-location)
- [ ] Real-time availability updates
- [ ] Enhanced geographic filtering

---

*For detailed documentation, see the complete files in this directory.* 
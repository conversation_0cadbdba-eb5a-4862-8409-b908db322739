
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { X, Mail, Phone, User } from 'lucide-react';
import { Contact } from '@/hooks/useContactPicker';

interface ContactListProps {
  contacts: Contact[];
  onRemoveContact: (index: number) => void;
  title?: string;
}

export const ContactList: React.FC<ContactListProps> = ({
  contacts,
  onRemoveContact,
  title = 'Contatti selezionati'
}) => {
  if (contacts.length === 0) {
    return null;
  }

  const getInitials = (name?: string) => {
    if (!name) return 'C';
    const words = name.split(' ');
    return words.map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);
  };

  const getContactDisplay = (contact: Contact) => {
    if (contact.name) return contact.name;
    if (contact.email) return contact.email;
    if (contact.tel) return contact.tel;
    return 'Contatto';
  };

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium text-gray-700">
        {title} ({contacts.length})
      </h3>
      <div className="space-y-2 max-h-60 overflow-y-auto">
        {contacts.map((contact, index) => (
          <Card key={index} className="bg-gray-50">
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback>
                      {getInitials(contact.name)}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <User className="h-3 w-3 text-gray-500" />
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {getContactDisplay(contact)}
                      </p>
                    </div>
                    
                    {contact.email && (
                      <div className="flex items-center space-x-2 mb-1">
                        <Mail className="h-3 w-3 text-gray-500" />
                        <p className="text-xs text-gray-600 truncate">
                          {contact.email}
                        </p>
                      </div>
                    )}
                    
                    {contact.tel && (
                      <div className="flex items-center space-x-2">
                        <Phone className="h-3 w-3 text-gray-500" />
                        <p className="text-xs text-gray-600 truncate">
                          {contact.tel}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onRemoveContact(index)}
                  className="h-8 w-8 p-0 hover:bg-red-100 hover:text-red-600"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

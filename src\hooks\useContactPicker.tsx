
import { useState, useCallback } from 'react';
import { toast } from 'sonner';

export interface Contact {
  name?: string;
  email?: string;
  tel?: string;
}

export interface ContactPickerResult {
  contacts: Contact[];
  error?: string;
}

export const useContactPicker = () => {
  const [isSupported, setIsSupported] = useState(() => {
    return 'contacts' in navigator && 'ContactsManager' in window;
  });

  const pickContacts = useCallback(async (options?: {
    multiple?: boolean;
    properties?: string[];
  }): Promise<ContactPickerResult> => {
    if (!isSupported) {
      return {
        contacts: [],
        error: 'Contact Picker API non supportato su questo dispositivo'
      };
    }

    try {
      const properties = options?.properties || ['name', 'email', 'tel'];
      const multiple = options?.multiple !== false;

      // Type assertion for Contact Picker API
      const contacts = await (navigator as any).contacts.select(properties, { multiple });
      
      return {
        contacts: contacts.map((contact: any) => ({
          name: contact.name?.[0] || '',
          email: contact.email?.[0] || '',
          tel: contact.tel?.[0] || ''
        }))
      };
    } catch (error: any) {
      if (error.name === 'AbortError') {
        return { contacts: [] }; // User cancelled
      }
      
      console.error('Error picking contacts:', error);
      return {
        contacts: [],
        error: 'Errore durante la selezione dei contatti'
      };
    }
  }, [isSupported]);

  return {
    isSupported,
    pickContacts
  };
};

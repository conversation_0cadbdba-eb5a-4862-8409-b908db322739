import { WeeklySchedule } from "@/types/deals";

export const daysNames = {
    
    1: "luned<PERSON>", 
    2: "martedì",
    3: "mercoledì",
    4: "gioved<PERSON>",
    5: "venerd<PERSON>",
    6: "sabato",
    7: "domenica"
  };

  export const DAYS_OF_WEEK = [
    
    { value: 1, label: "Lunedì" },
    { value: 2, label: "Martedì" },
    { value: 3, label: "Mercoledì" },
    { value: 4, label: "Giovedì" },
    { value: 5, label: "Venerdì" },
    { value: 6, label: "Sabato" },
    { value: 7, label: "Domenica" }
  ];

  export const INITIAL_SCHEDULE: WeeklySchedule = {
    schedule: [
      { day: 1, day_name: "Lunedì", time_slots: [] },
      { day: 2, day_name: "<PERSON>ed<PERSON>", time_slots: [] },
      { day: 3, day_name: "Mercoledì", time_slots: [] },
      { day: 4, day_name: "<PERSON><PERSON>vedì", time_slots: [] },
      { day: 5, day_name: "Venerd<PERSON>", time_slots: [] },
      { day: 6, day_name: "Sabat<PERSON>", time_slots: [] },
      { day: 7, day_name: "Domenica", time_slots: [] }
    ],
    exceptions: []
  };


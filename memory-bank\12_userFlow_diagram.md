# CatchUp - User Flow Diagrams

## Customer User Flow - Deal Discovery to Booking

```mermaid
flowchart TD
    A[Open CatchUp App] --> B{Location Access?}
    B -->|Granted| C[Load Nearby Deals]
    B -->|Denied| D[Manual Location Entry]
    D --> C
    
    C --> E[Display Deal Map/List]
    E --> F{User Action}
    
    F -->|Search| G[Enter Search Query]
    F -->|Filter| H[Apply Category Filter]
    F -->|View Deal| I[Deal Detail Page]
    
    G --> J[Display Search Results]
    H --> K[Filter Results]
    J --> E
    K --> E
    
    I --> L{Check Availability}
    L -->|Available| M[Select Date/Time]
    L -->|Limited| N[Show Limited Slots]
    L -->|Sold Out| O[Suggest Alternatives]
    
    N --> M
    O --> P[View Alternative Deals]
    P --> I
    
    M --> Q{User Logged In?}
    Q -->|No| R[Login/Register]
    Q -->|Yes| S[Booking Form]
    
    R --> S
    S --> T[Enter Booking Details]
    T --> U[Review Booking]
    U --> V[Payment Processing]
    V --> W{Payment Success?}
    
    W -->|Yes| X[Booking Confirmation]
    W -->|No| Y[Payment Error]
    Y --> Z[Retry Payment]
    Z --> V
    
    X --> AA[Receive Confirmation Email]
    AA --> BB[Generate QR Code]
    BB --> CC[Navigate to Venue]
    CC --> DD[Show QR at Venue]
    DD --> EE[Enjoy Experience]
    EE --> FF[Leave Review]
```

## Business User Flow - Deal Creation and Management

```mermaid
flowchart TD
    A[Business Owner Access] --> B{Account Exists?}
    B -->|No| C[Business Registration]
    B -->|Yes| D[Business Login]
    
    C --> E[Complete Business Profile]
    E --> F[Upload Business Documents]
    F --> G[Verification Process]
    G --> H[Account Approved]
    
    D --> I[Business Dashboard]
    H --> I
    
    I --> J{Choose Action}
    J -->|Create Deal| K[Deal Creation Form]
    J -->|Manage Deals| L[Deal Management]
    J -->|View Analytics| M[Analytics Dashboard]
    J -->|Manage Bookings| N[Booking Management]
    
    K --> O[Enter Deal Details]
    O --> P[Set Pricing & Availability]
    P --> Q[Upload Deal Images]
    Q --> R[Configure Time Slots]
    R --> S[Review Deal Settings]
    S --> T[Publish Deal]
    T --> U[Deal Goes Live]
    U --> I
    
    L --> V[View Active Deals]
    V --> W{Deal Action}
    W -->|Edit| X[Modify Deal Settings]
    W -->|Pause| Y[Temporarily Disable]
    W -->|Delete| Z[Remove Deal]
    W -->|Duplicate| AA[Create Similar Deal]
    
    X --> BB[Update Deal Information]
    BB --> CC[Save Changes]
    CC --> DD[Real-time Update]
    DD --> V
    
    N --> EE[View Current Bookings]
    EE --> FF{Booking Action}
    FF -->|Confirm| GG[Confirm Booking]
    FF -->|Cancel| HH[Cancel & Refund]
    FF -->|Modify| II[Reschedule Booking]
    
    GG --> JJ[Send Confirmation]
    HH --> KK[Process Refund]
    II --> LL[Update Booking Details]
    JJ --> EE
    KK --> EE
    LL --> EE
```

## Authentication Flow

```mermaid
flowchart TD
    A[User Wants to Book/Create] --> B{Authenticated?}
    B -->|Yes| C[Proceed to Action]
    B -->|No| D[Show Auth Options]
    
    D --> E{Auth Method}
    E -->|Email/Password| F[Login Form]
    E -->|Social Login| G[OAuth Provider]
    E -->|New User| H[Registration Form]
    
    F --> I[Enter Credentials]
    I --> J{Valid Credentials?}
    J -->|Yes| K[Create Session]
    J -->|No| L[Show Error]
    L --> F
    
    G --> M[OAuth Flow]
    M --> N{OAuth Success?}
    N -->|Yes| O[Create/Link Account]
    N -->|No| P[OAuth Error]
    P --> D
    
    H --> Q[Enter User Details]
    Q --> R[Password Creation]
    R --> S[Email Verification]
    S --> T[Account Created]
    
    K --> C
    O --> C
    T --> U[Welcome Flow]
    U --> C
```

## Search and Discovery Flow

```mermaid
flowchart TD
    A[User Opens App] --> B[Load Homepage]
    B --> C{User Intent}
    
    C -->|Browse Nearby| D[Show Location-based Deals]
    C -->|Search Specific| E[Search Interface]
    C -->|Category Browse| F[Category Selection]
    
    D --> G[Display Map View]
    G --> H[Show Business Markers]
    H --> I{Marker Interaction}
    I -->|Click Marker| J[Show Business Popup]
    I -->|Cluster| K[Zoom to Cluster]
    
    J --> L[Display Deal List]
    L --> M[Select Deal]
    
    E --> N[Enter Search Terms]
    N --> O[Apply Search Filters]
    O --> P{Search Type}
    P -->|Text| Q[Full-text Search]
    P -->|Location| R[Geographic Search]
    P -->|Category| S[Category Filter]
    
    Q --> T[Ranked Results]
    R --> U[Distance-based Results]
    S --> V[Category Results]
    
    T --> W[Display Results List]
    U --> W
    V --> W
    W --> M
    
    F --> X[Show Categories]
    X --> Y[Select Category]
    Y --> Z[Category-filtered Deals]
    Z --> M
    
    M --> AA[Deal Detail View]
```

## Booking Process Flow

```mermaid
flowchart TD
    A[User Selects Deal] --> B[Deal Detail Page]
    B --> C[Check Real-time Availability]
    C --> D{Availability Status}
    
    D -->|Available| E[Show Available Slots]
    D -->|Limited| F[Show Limited Slots]
    D -->|Sold Out| G[Show Waitlist Option]
    
    E --> H[Date/Time Selection]
    F --> H
    G --> I[Join Waitlist]
    I --> J[Waitlist Confirmation]
    
    H --> K[Quantity Selection]
    K --> L{User Authenticated?}
    L -->|No| M[Auth Required]
    L -->|Yes| N[Booking Form]
    
    M --> O[Login/Register]
    O --> N
    
    N --> P[Enter Booking Details]
    P --> Q[Special Requests]
    Q --> R[Booking Summary]
    R --> S[Terms & Conditions]
    S --> T[Payment Method]
    
    T --> U{Payment Type}
    U -->|Credit Card| V[Card Payment Form]
    U -->|Digital Wallet| W[Wallet Integration]
    U -->|BNPL| X[Buy Now Pay Later]
    
    V --> Y[Process Payment]
    W --> Y
    X --> Y
    
    Y --> Z{Payment Result}
    Z -->|Success| AA[Booking Confirmed]
    Z -->|Failed| BB[Payment Error]
    Z -->|Pending| CC[Pending Confirmation]
    
    BB --> DD[Error Handling]
    DD --> T
    
    AA --> EE[Send Confirmations]
    CC --> FF[Monitor Payment Status]
    FF --> EE
    
    EE --> GG[Generate QR Code]
    GG --> HH[Booking Complete]
```

## Real-time Updates Flow

```mermaid
flowchart TD
    A[System Event Occurs] --> B{Event Type}
    
    B -->|Booking Created| C[Update Availability]
    B -->|Booking Cancelled| D[Restore Capacity]
    B -->|Deal Modified| E[Update Deal Info]
    B -->|Business Changes| F[Update Business Data]
    
    C --> G[Calculate New Availability]
    D --> G
    E --> H[Propagate Deal Changes]
    F --> I[Update Business Displays]
    
    G --> J[Update Database]
    H --> J
    I --> J
    
    J --> K[Trigger Real-time Subscriptions]
    K --> L{Active Connections}
    
    L -->|Customer Apps| M[Update Deal Displays]
    L -->|Business Dashboards| N[Update Management Views]
    L -->|Admin Panels| O[Update Admin Data]
    
    M --> P[Refresh Availability Status]
    N --> Q[Update Booking Counters]
    O --> R[Update System Metrics]
    
    P --> S[Notify UI Components]
    Q --> S
    R --> S
    
    S --> T[User Sees Updated Info]
```

## Error Handling and Recovery Flow

```mermaid
flowchart TD
    A[User Action] --> B[System Processing]
    B --> C{Processing Result}
    
    C -->|Success| D[Normal Flow Continues]
    C -->|Error| E[Error Classification]
    
    E --> F{Error Type}
    F -->|Network Error| G[Connection Lost]
    F -->|Validation Error| H[User Input Error]
    F -->|System Error| I[Server Error]
    F -->|Business Logic Error| J[Booking Conflict]
    
    G --> K[Show Offline Message]
    K --> L[Queue Action for Retry]
    L --> M[Monitor Connection]
    M --> N{Connection Restored?}
    N -->|Yes| O[Retry Queued Actions]
    N -->|No| M
    
    H --> P[Show Validation Message]
    P --> Q[Highlight Error Fields]
    Q --> R[User Corrects Input]
    R --> A
    
    I --> S[Show Generic Error]
    S --> T[Log Error Details]
    T --> U[Offer Retry Option]
    U --> V{User Retries?}
    V -->|Yes| A
    V -->|No| W[Return to Previous State]
    
    J --> X[Show Specific Conflict Message]
    X --> Y[Suggest Alternatives]
    Y --> Z{User Selects Alternative?}
    Z -->|Yes| AA[Process Alternative]
    Z -->|No| BB[Cancel Current Action]
    
    O --> D
    AA --> D
    W --> CC[User Can Try Different Action]
    BB --> CC
``` 
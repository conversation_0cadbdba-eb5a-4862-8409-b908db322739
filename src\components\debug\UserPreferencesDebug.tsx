import { useAuth } from '@/hooks/auth/useAuth';
import { useUserPreferencesManager } from '@/hooks/useUserPreferencesManager';
import { useFilterStore } from '@/stores/useFilterStore';
import { useUserPreferencesSetup } from '@/hooks/useUserPreferencesSetup';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

/**
 * Debug component to verify user preferences setup integration
 * Shows current user preferences and filter state
 */
export const UserPreferencesDebug = () => {
  const { user } = useAuth();
  const { preferences, isLoading } = useUserPreferencesManager();
  const { filters } = useFilterStore();
  const { applicationAccess } = useUserPreferencesSetup();

  if (!user) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-sm">User Preferences Debug</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-500">User not authenticated</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="text-sm">User Preferences Debug</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="text-sm font-medium mb-2">User ID:</h4>
          <p className="text-xs text-gray-600">{user.id}</p>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-2">Preferences Status:</h4>
          <Badge variant={isLoading ? "secondary" : "default"}>
            {isLoading ? "Loading..." : preferences ? "Found" : "Not Found"}
          </Badge>
        </div>

        {preferences && (
          <div>
            <h4 className="text-sm font-medium mb-2">Stored Categories:</h4>
            <div className="flex flex-wrap gap-1">
              {preferences.categories.length > 0 ? (
                preferences.categories.map((categoryId, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {categoryId}
                  </Badge>
                ))
              ) : (
                <span className="text-xs text-gray-500">No categories stored</span>
              )}
            </div>
          </div>
        )}

        <div>
          <h4 className="text-sm font-medium mb-2">Current Filter Categories:</h4>
          <div className="flex flex-wrap gap-1">
            {filters.selectedCategoryIds.length > 0 ? (
              filters.selectedCategoryIds.map((categoryId, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {categoryId}
                </Badge>
              ))
            ) : (
              <span className="text-xs text-gray-500">No categories selected</span>
            )}
          </div>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-2">Application Access:</h4>
          <Badge variant={applicationAccess ? "default" : "secondary"}>
            {applicationAccess ? "✅ Accessed" : "⏳ First Time"}
          </Badge>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-2">Sync Status:</h4>
          <Badge 
            variant={
              JSON.stringify(preferences?.categories?.sort() || []) === 
              JSON.stringify(filters.selectedCategoryIds.sort())
                ? "default" 
                : "destructive"
            }
          >
            {JSON.stringify(preferences?.categories?.sort() || []) === 
             JSON.stringify(filters.selectedCategoryIds.sort())
              ? "✅ In Sync" 
              : "⚠️ Out of Sync"}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}; 
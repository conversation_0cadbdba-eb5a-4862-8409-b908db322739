
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { GroupWithMemberCount } from '@/types/groups';

interface GroupMember {
  id: string;
  user_id: string;
  role: 'admin' | 'member';
  joined_at: string;
  first_name: string;
  last_name: string;
  avatar_url?: string;
}

interface GroupDetailsData extends GroupWithMemberCount {
  members: GroupMember[];
}

export const useGroupDetails = (groupId: string) => {

// TODO: check if this is correct users_with_details or we need user_details  
  return useQuery({
    queryKey: ['group-details', groupId],
    queryFn: async (): Promise<GroupDetailsData> => {
      // Get group details
      const { data: group, error: groupError } = await supabase
        .from('groups')
        .select(`
          *,
          group_members!inner(
            id,
            user_id,
            role,
            joined_at,
            profiles:users_with_details(   
              first_name,
              last_name,
              avatar_url
            )
          )
        `)
        .eq('id', groupId)
        .maybeSingle();

      if (groupError) {
        console.error('Error fetching group details:', groupError);
        throw groupError;
      }

      // Get current user's role in the group
      const { data: { user } } = await supabase.auth.getUser();
      const userRole = group.group_members?.find(
        (member: any) => member.user_id === user?.id
      )?.role || 'member';

      // Format the response
      return {
        ...group,
        member_count: group.group_members?.length || 0,
        user_role: userRole as 'admin' | 'member',
        members: group.group_members?.map((member: any) => ({
          id: member.id,
          user_id: member.user_id,
          role: member.role,
          joined_at: member.joined_at,
          first_name: member.profiles?.first_name || '',
          last_name: member.profiles?.last_name || '',
          avatar_url: member.profiles?.avatar_url
        })) || []
      };
    },
    enabled: !!groupId
  });
};

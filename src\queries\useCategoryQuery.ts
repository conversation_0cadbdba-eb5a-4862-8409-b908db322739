
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useCategoryQuery = () => {
  return useQuery({
    queryKey: ['categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (error) {
        throw new Error(`Errore nel recupero delle categorie: ${error.message}`);
      }

      return data || [];
    },
    enabled: true,
    staleTime: 1000 * 60 * 30, // 30 minutes
  });
};

import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";
import { corsHeaders } from "../_shared/cors.ts";


// Types for request parameters
interface LocationParams {
  lat: number;
  lng: number;
  radius?: number;
}

interface RequestParams {
  date: string;
  time?: string;
  location?: LocationParams;
  cityName?: string;
  category_id?: string;
  cursor?: string;
  limit?: number;
}

// Response types
interface DealAvailability {
  id: string;
  name: string;
  description: string;
  original_price: number;
  discounted_price:number;
  discount_percentage:number;
  images: string[] | null;
  time_slots: any;
  status: "published" | "draft" | "expired";
  created_at: string;
  updated_at: string;
  auto_confirm: boolean;
  category_id: string;
  start_date: string;
  end_date: string;
  business: Business
}

interface Business{
  id: string;
  name: string;
  address:string;
  city:string;
  state:string;
  zip_code:string;
  country:string;

  latitude: number;
  longitude:number;
  distance:number;
  category_id: string
}

interface AvailabilityResponse {
  date: string;
  time?: string;
  deal_count: number,
  user_location?: LocationParams;
  deals: DealAvailability[];
  next_cursor: string | null;
  cached?: boolean;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Extract and validate request parameters
    let params: RequestParams;
    try {
      params = await req.json();
    } catch (e) {
      return new Response(
        JSON.stringify({
          error: "Invalid parameters",
          details: "Request body must be valid JSON"
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    const { 
      date, 
      time, 
      location, 
      category_id, 
      cursor,
      limit = 20 
    } = params;

    // Validate required parameters
    if (!date) {
      return new Response(
        JSON.stringify({
          error: "Missing required parameter",
          details: "Date is required"
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    console.log(`Processing availability request for date: ${date}, time: ${time || 'any'}, cursor: ${cursor || 'initial'}`);

    const payLoad = {
        p_date: date,
        p_time: time || null,
        p_lat: location?.lat || null,
        p_lng: location?.lng || null,
        p_radius: location?.radius || 5000,
        p_category_id: category_id || null,
        p_cursor: cursor || null,
        p_limit: limit
      };
    console.log("Processing with payload:", payLoad);
    // Call the optimized stored procedure
    const { data: dealNearbyData, error } = await supabase
      .rpc('get_nearby_deals', {
        p_date: date,
        p_time: time || null,
        p_lat: location?.lat || null,
        p_lng: location?.lng || null,
        p_radius: location?.radius || 5000,
        p_category_id: category_id || null,
        p_cursor: cursor || null,
        p_limit: limit
      });
      
   
    if (error) {
      console.error("Database query error:", error);
      return new Response(
        JSON.stringify({
          error: "Database query failed",
          details: error.message
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    // Filtriamo le offerte sulla base della data e ora specificate
   // const filteredDeals = filterDealsByTimeSlot(data, date, time);
   // console.log(`Found ${data.length} deals from DB, filtered to ${filteredDeals.length} deals with available slots for the requested time`);

    // Prepare the response
    const response: AvailabilityResponse = {
      date,
      time,
      deal_count: dealNearbyData.length,
      user_location: location,
      deals: dealNearbyData.map(deal => ({
        id: deal.deal_id,
        name: deal.title,
        description: deal.description,
        original_price: deal.original_price,
        discounted_price: deal.discounted_price,
        discount_percentage: deal.discount_percentage,
        images: deal.images,
        time_slots: deal.available_slots,
        // Assicuriamoci che status sia uno dei valori ammessi
        status: validateDealStatus(deal.status),
        created_at: deal.created_at || new Date().toISOString(),
        updated_at: deal.updated_at || new Date().toISOString(),
        auto_confirm: deal.auto_confirm !== false,
        category_id: deal.category_id,
        start_date: deal.start_date,
        end_date: deal.end_date,
        terms_conditions: deal.terms_conditions || "Informazioni importanti -La prenotazione non è rimborsabile -Presentati 5 minuti prima dell'orario prenotato -Porta con te un documento d'identità",
        business: {
          id: deal.business_id,
          name: deal.business_name,
          address: deal.address,
          city: deal.city,
          state: deal.state,
          zip_code: deal.zip_code,
          country: deal.country,
          latitude: deal.latitude,
          longitude: deal.longitude,
          distance: deal.distance,
          category_id: deal.category_id
        }
      })),
      next_cursor: dealNearbyData.length > 0 ? dealNearbyData[dealNearbyData.length - 1]?.next_cursor || null : null,
      cached: false
    };

    // Log success metrics
    console.log(`Request completed successfully. Found ${response.deals.length} deals after time slot filtering`);
    if (response.next_cursor) {
      console.log(`Next cursor available: ${response.next_cursor}`);
    }

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );

  } catch (error) {
    console.error("Unexpected error:", error);
    
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        details: error.message
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500
      }
    );
  }
});

/**
 * Funzione di supporto per validare lo stato dell'offerta
 */
function validateDealStatus(status: string | undefined): "published" | "draft" | "expired" {
  if (status === 'published' || status === 'draft' || status === 'expired') {
    return status;
  }
  // Valore predefinito se lo stato non è valido
  return 'published';
}

/**
 * Filtra le offerte per assicurarsi che abbiano slot disponibili nella data e ora specificate
 */
// function filterDealsByTimeSlot(deals: any[], date: string, time: string | undefined): any[] {
//   if (!time) {
//     return deals; // Se non viene specificata un'ora, restituisci tutte le offerte
//   }

//   // Converti la data in numero di giorno della settimana (0-6, dove 0 è domenica)
//   const dayOfWeek = new Date(date).getDay();
//   // Converti in formato 1-7 dove 1 è lunedì (formato utilizzato nel nostro database)
//   const dayOfWeekAdjusted = dayOfWeek === 0 ? 7 : dayOfWeek;

//   return deals.filter(deal => {
//     if (!deal.available_slots || !deal.available_slots.schedule) {
//       return false;
//     }

//     // Trova lo schedule per il giorno specificato
//     const daySchedule = deal.available_slots.schedule.find((s: any) => s.day === dayOfWeekAdjusted);
//     if (!daySchedule || !daySchedule.time_slots) {
//       return false;
//     }

//     // Controlla se esiste uno slot che include l'ora specificata
//     return daySchedule.time_slots.some((slot: any) => {
//       const [requestedHour, requestedMinute] = time.split(':').map(Number);
//       const [startHour, startMinute] = slot.start_time.split(':').map(Number);
//       const [endHour, endMinute] = slot.end_time.split(':').map(Number);

//       const requestedTimeMinutes = requestedHour * 60 + requestedMinute;
//       const startTimeMinutes = startHour * 60 + startMinute;
//       const endTimeMinutes = endHour * 60 + endMinute;

//       return requestedTimeMinutes >= startTimeMinutes && requestedTimeMinutes <= endTimeMinutes;
//     });
//   });
//}

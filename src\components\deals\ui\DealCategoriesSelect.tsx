import { useState, useEffect } from "react";
import { Check, ChevronDown, X } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { cn } from "@/lib/utils";

interface DealCategory {
  id: string;
  name: string;
  category_id: string;
}

interface DealCategoriesSelectProps {
  selectedCategories: string[];
  onCategoriesChange: (categories: string[]) => void;
  businessCategoryId?: string;
}

export const DealCategoriesSelect = ({
  selectedCategories,
  onCategoriesChange,
  businessCategoryId
}: DealCategoriesSelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dealCategories, setDealCategories] = useState<DealCategory[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDealCategories = async () => {
      try {
        setLoading(true);
        
        // Fetch deal categories from the database
        const { data, error } = await supabase
          .from("deal_categories" as any)
          .select("id, name, category_id")
          .order("name");

        if (error) {
          console.error("Error fetching deal categories:", error);
          return;
        }

        // Type assertion to match our interface
        const allCategories = (data || []).map((item: any) => ({
          id: item.id,
          name: item.name,
          category_id: item.category_id
        })) as DealCategory[];

        // Filter by business category if provided
        if (businessCategoryId) {
          const filteredCategories = allCategories.filter(cat => cat.category_id === businessCategoryId);
          setDealCategories(filteredCategories);
        } else {
          setDealCategories(allCategories);
        }
      } catch (error) {
        console.error("Error fetching deal categories:", error);
        // Fallback to empty array if there's an error
        setDealCategories([]);
      } finally {
        setLoading(false);
      }
    };

    fetchDealCategories();
  }, [businessCategoryId]);

  const toggleCategory = (categoryId: string) => {
    const isSelected = selectedCategories.includes(categoryId);
    if (isSelected) {
      onCategoriesChange(selectedCategories.filter(id => id !== categoryId));
    } else {
      onCategoriesChange([...selectedCategories, categoryId]);
    }
  };

  const removeCategory = (categoryId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    onCategoriesChange(selectedCategories.filter(id => id !== categoryId));
  };

  const selectedCategoryNames = dealCategories
    .filter(cat => selectedCategories.includes(cat.id))
    .map(cat => cat.name);

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-foreground mb-2">
        Categorie Offerta
      </label>
      
      <div
        className={cn(
          "w-full min-h-[42px] px-3 py-2 rounded-lg border border-border bg-background cursor-pointer transition-colors",
          "focus-within:ring-2 focus-within:ring-primary focus-within:border-primary",
          isOpen && "ring-2 ring-primary border-primary"
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center justify-between">
          <div className="flex flex-wrap gap-1 flex-1 min-h-[26px]">
            {selectedCategoryNames.length === 0 ? (
              <span className="text-muted-foreground text-sm">
                Seleziona categorie...
              </span>
            ) : (
              selectedCategoryNames.map((name, index) => {
                const categoryId = dealCategories.find(cat => cat.name === name)?.id;
                return (
                  <span
                    key={categoryId}
                    className="inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary text-xs rounded-md"
                  >
                    {name}
                    <button
                      type="button"
                      onClick={(e) => categoryId && removeCategory(categoryId, e)}
                      className="hover:bg-primary/20 rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                );
              })
            )}
          </div>
          <ChevronDown 
            className={cn(
              "h-4 w-4 text-muted-foreground transition-transform",
              isOpen && "rotate-180"
            )} 
          />
        </div>
      </div>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-background border border-border rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {loading ? (
            <div className="p-3 text-sm text-muted-foreground">
              Caricamento categorie...
            </div>
          ) : dealCategories.length === 0 ? (
            <div className="p-3 text-sm text-muted-foreground">
              Nessuna categoria disponibile
            </div>
          ) : (
            dealCategories.map((category) => {
              const isSelected = selectedCategories.includes(category.id);
              return (
                <div
                  key={category.id}
                  className={cn(
                    "flex items-center gap-2 px-3 py-2 cursor-pointer hover:bg-muted/50 transition-colors",
                    isSelected && "bg-primary/5"
                  )}
                  onClick={() => toggleCategory(category.id)}
                >
                  <div className={cn(
                    "w-4 h-4 border border-border rounded flex items-center justify-center",
                    isSelected && "bg-primary border-primary"
                  )}>
                    {isSelected && <Check className="h-3 w-3 text-primary-foreground" />}
                  </div>
                  <span className="text-sm">{category.name}</span>
                </div>
              );
            })
          )}
        </div>
      )}

      {/* Overlay to close dropdown */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-5" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};
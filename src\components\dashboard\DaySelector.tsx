import { format, isToday, isBefore } from "date-fns";
import { it } from "date-fns/locale";
import { useState, useEffect, useCallback, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Clock } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  CarouselApi,
} from "@/components/ui/carousel";
import { usePagedDates } from "@/hooks/calendar/usePageDates";
import ButtonDate from "@/components/ButtonDate";
interface DaySelectorProps {
  selectedDate: string;
  onDateSelect: (date: string) => void;
  onResetToNow?: () => void;
}
const DaySelector = ({
  selectedDate,
  onDateSelect,
  onResetToNow,
}: DaySelectorProps) => {
  const today = new Date();
  const carouselApiRef = useRef<any>(null);
  const { visibleDates, handleSlideChange, currentPage } = usePagedDates({
    pageSize: 7,
    maxPages: 15,
  });
  const onCarouselInit = useCallback(
    (api: CarouselApi) => {
      carouselApiRef.current = api;
      //api.scrollTo(1); // sempre centrato sulla pagina visibile

      // Assicuriamoci che la pagina attuale sia correttamente inizializzata
      if (api) {
        setTimeout(() => {
          const currentSnap = api.selectedScrollSnap();
          handleSlideChange(currentSnap, api);
        }, 0);
      }
      api.on("select", () => {
        const currentSnap = api.selectedScrollSnap();
        handleSlideChange(currentSnap, api);
      });
    },
    [handleSlideChange]
  );
  const formatDateDisplay = (date: Date): string => {
    return isToday(date)
      ? "Oggi"
      : format(date, "EEE d MMM", {
          locale: it,
        });
  };
  const formatDateValue = (date: Date): string => {
    return format(date, "yyyy-MM-dd");
  };
  const handleDateSelect = (date: Date) => {
    const formatted = formatDateValue(date);
    onDateSelect(formatted);
  };
  const handleResetToNow = () => {
    const api = carouselApiRef.current;
    if (api) api.scrollTo(0); // riporta alla pagina iniziale
    onDateSelect(formatDateValue(today));
    if (onResetToNow) onResetToNow();
  
  };

  // auto-seleziona il primo giorno visibile dopo scroll
  useEffect(() => {
    if (visibleDates.length > 0 && currentPage >= 0) {
      // Calcola l'indice del primo elemento della pagina corrente
      const pageStartIndex = 7 * currentPage;

      // Assicuriamoci che l'indice sia valido
      if (pageStartIndex < visibleDates.length) {
        const firstVisible = visibleDates[pageStartIndex];
        onDateSelect(formatDateValue(firstVisible));
      }
    }
  }, [currentPage, onDateSelect, visibleDates]);
  const isDateSelected = (date: Date): boolean => {
    return formatDateValue(date) === selectedDate;
  };
  return (
    <div className="mb-4 relative">
      <div className="flex justify-end items-center text-xs font-medium px-1 mb-2">
        {onResetToNow && (
          <Button
            variant="ghost"
            size="sm"
            className="text-blue-600 p-0 h-auto"
            onClick={handleResetToNow}
          >
            <Clock className="h-3.5 w-3.5 mr-1" />
            Oggi
          </Button>
        )}
      </div>
      <div className="relative flex justify-center">
        <Carousel
          className="w-full mx-auto"
          setApi={onCarouselInit}
          opts={{
            align: "start",
            slidesToScroll: 7,
            containScroll: "trimSnaps",
            dragFree: false,
          }}
        >
          <CarouselContent className="ml-0">
            {visibleDates.map((date, index) => (
              <CarouselItem
                className="basis-1/7 flex-shrink-0 w-[calc(100%/7)] pl-0 pr-1"
                key={`day-${index}-${format(date, "yyyy-MM-dd")}`}
              >
                <ButtonDate
                  date={date}
                  isSelected={isDateSelected(date)}
                  today={today}
                  onSelect={handleDateSelect}
                />
              </CarouselItem>
            ))}
          </CarouselContent>
          {/* Frecce nascoste dal CSS, ma mantenute per accessibilità */}
          <CarouselPrevious className="hidden" />
          <CarouselNext className="hidden" />
        </Carousel>
      </div>
    </div>
  );
};
export default DaySelector;

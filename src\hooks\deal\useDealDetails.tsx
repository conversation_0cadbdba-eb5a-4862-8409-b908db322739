
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/auth/useAuth";
import { toast } from "sonner";
import { settings } from "@/config/settings";
import { format, isWithinInterval, startOfToday, parseISO, addDays } from "date-fns";
import type { Database } from "@/integrations/supabase/types";
import type { WeeklySchedule } from "@/types/deals";

type Deal = Database['public']['Tables']['deals']['Row'];

export const useDealDetails = (id: string | undefined) => {
  
  const { user, userDetails } = useAuth();
  
  
  const [deal, setDeal] = useState<Deal | null>(null);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [isFavorite, setIsFavorite] = useState(false);
  const [availableDates, setAvailableDates] = useState<Date[]>([]);

  useEffect(() => {
    if (!user || !userDetails || !id) return;
    if (userDetails?.favorite_deals) {
      setIsFavorite((userDetails.favorite_deals as string[]).includes(id || ''));
    }
  }, [userDetails, id]);

  useEffect(() => {
    const fetchDeal = async () => {
      if (!id) return;

      try {
        const { data, error } = await supabase
          .from('deals')
          .select('*')
          .eq('id', id)
          .maybeSingle();

        if (error) {
          toast.error("Errore nel caricamento dell'offerta");
          return;
        }

        setDeal(data);

        if (data) {
          const start = parseISO(data.start_date);
          const end = parseISO(data.end_date);
          const today = startOfToday();
          const dates: Date[] = [];
          
          let currentDate = today;
          while (currentDate <= end) {
            if (isWithinInterval(currentDate, { start, end })) {
              // Verifica se ci sono time slots per questo giorno della settimana
              const dayOfWeek = currentDate.getDay();
              const timeSlots = data.time_slots as WeeklySchedule;
              const hasTimeSlots = timeSlots?.schedule?.some(
                slot => slot.day === dayOfWeek && slot.time_slots.length > 0
              );
              
              if (hasTimeSlots) {
                dates.push(currentDate);
              }
            }
            currentDate = addDays(currentDate, 1);
          }
          
          setAvailableDates(dates);
        }

        if (user && userDetails) {
          const currentRecentlyViewed = Array.isArray(userDetails.recently_viewed_deals) 
            ? userDetails.recently_viewed_deals 
            : [];
          
          const newRecentlyViewed = [
            id,
            ...currentRecentlyViewed.filter(dealId => dealId !== id)
          ].slice(0, settings.recentlyViewedDeals.maxItems);

          const { error: updateError } = await supabase
            .from('user_details')
            .update({ recently_viewed_deals: newRecentlyViewed })
            .eq('id', user.id);

          if (updateError) {
            console.error('Error updating recently viewed:', updateError);
            toast.error("Errore nell'aggiornamento delle offerte recenti");
          }
        }
      } catch (error) {
        console.error('Error in fetchDeal:', error);
        toast.error("Si è verificato un errore");
      }
    };

    fetchDeal();
  }, [id, user, userDetails]);

  const toggleFavorite = async () => {
    if (!user || !userDetails || !id) return;

    try {
      const currentFavorites = Array.isArray(userDetails.favorite_deals) 
        ? userDetails.favorite_deals 
        : [];

      const newFavorites = isFavorite
        ? currentFavorites.filter(dealId => dealId !== id)
        : [...currentFavorites, id];

      const { error } = await supabase
        .from('user_details')
        .update({ favorite_deals: newFavorites })
        .eq('id', user.id);

      if (error) {
        toast.error("Errore nell'aggiornamento dei preferiti");
        return;
      }

      setIsFavorite(!isFavorite);
      toast.success(isFavorite ? "Rimosso dai preferiti" : "Aggiunto ai preferiti");
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast.error("Si è verificato un errore");
    }
  };

  return {
    deal,
    selectedDate,
    setSelectedDate,
    selectedTime,
    setSelectedTime,
    isFavorite,
    availableDates,
    toggleFavorite
  };
};

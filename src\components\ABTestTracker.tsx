import { useEffect } from 'react';
import { useABTest } from '../contexts/ABTestContext';
import { useLocation } from 'react-router-dom';

// Define key metrics to track
const KEY_PAGES = [
  '/', // Home page
  '/map', // Map page
  '/deals', // Deals page
  '/profile', // Profile page
];

// Define key conversion actions
const CONVERSION_ACTIONS = [
  'booking_completed',
  'deal_viewed',
  'deal_saved',
  'business_contacted',
  'app_shared',
];

const ABTestTracker = () => {
  const { trackEvent, trackConversion } = useABTest();
  const location = useLocation();

  // Track page views
  useEffect(() => {
    const path = location.pathname;

    // Track all page views
    trackEvent('page_view', { path });

    // Track key page views separately
    if (KEY_PAGES.includes(path)) {
      trackEvent('key_page_view', { page: path });
    }
  }, [location.pathname, trackEvent]);

  // Track user engagement metrics
  useEffect(() => {
    // Variables to track engagement
    const sessionStartTime = Date.now();
    let lastActivityTime = Date.now();
    let totalScrollDepth = 0;
    let maxScrollDepth = 0;
    let interactionCount = 0;

    // Track scroll depth
    const handleScroll = () => {
      lastActivityTime = Date.now();

      // Calculate scroll depth as percentage
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = scrollTop / docHeight * 100;

      totalScrollDepth += scrollPercent - maxScrollDepth;
      maxScrollDepth = Math.max(maxScrollDepth, scrollPercent);
    };

    // Track user interactions
    const handleInteraction = () => {
      lastActivityTime = Date.now();
      interactionCount++;
    };

    // Track session data when user leaves the page
    const handleBeforeUnload = () => {
      const sessionDuration = (Date.now() - sessionStartTime) / 1000; // in seconds

      trackEvent('user_engagement', {
        sessionDuration,
        interactionCount,
        maxScrollDepth,
        averageScrollDepth: totalScrollDepth / interactionCount || 0,
      });
    };

    // Set up event listeners
    window.addEventListener('scroll', handleScroll);
    window.addEventListener('click', handleInteraction);
    window.addEventListener('keydown', handleInteraction);
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Clean up event listeners
    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('click', handleInteraction);
      window.removeEventListener('keydown', handleInteraction);
      window.removeEventListener('beforeunload', handleBeforeUnload);

      // Track engagement data on component unmount as well
      const sessionDuration = (Date.now() - sessionStartTime) / 1000; // in seconds

      trackEvent('user_engagement', {
        sessionDuration,
        interactionCount,
        maxScrollDepth,
        averageScrollDepth: totalScrollDepth / interactionCount || 0,
      });
    };
  }, [trackEvent]);

  // Track conversion actions on specific paths
  useEffect(() => {
    // Function to check the current path for conversion actions
    const checkForConversions = () => {
      const path = location.pathname;

      // Check if the current path matches any conversion actions
      if (path.includes('/booking/confirmation')) {
        trackConversion('booking_completed');
      } else if (path.includes('/deal/')) {
        trackEvent('deal_viewed', { path });
      } else if (path.includes('/business/')) {
        trackEvent('business_viewed', { path });
      }
    };

    // Check for conversions when the component mounts and when the path changes
    checkForConversions();

    // No cleanup needed for this effect
  }, [location.pathname, trackConversion, trackEvent]);

  // This component doesn't render anything
  return null;
};

export default ABTestTracker;

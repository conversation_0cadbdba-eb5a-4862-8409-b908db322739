
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export const useLogout = () => {
  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    //  toast.success("Logout effettuato con successo");
    } catch (error) {
      console.error('Error during logout:', error);
      toast.error("Errore durante il logout");
    }
  };

  return { logout };
};

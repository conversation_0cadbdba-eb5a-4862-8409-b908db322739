import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { toast } from '@/hooks/use-toast';

interface GmailIntegration {
  id: string;
  provider: string;
  integration_id: string;
  is_active: boolean;
  metadata?: {
    email?: string;
    scopes?: string[];
  };
  created_at: string;
  updated_at: string;
}

interface EmailData {
  id: string;
  sender: string;
  subject: string;
  preview: string;
  time: string;
  isRead: boolean;
  isStarred: boolean;
  body?: string;
}

export const useGmailIntegration = () => {
  const { user, isAuthenticated } = useAuth();
  const [integration, setIntegration] = useState<GmailIntegration | null>(null);
  const [emails, setEmails] = useState<EmailData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  // Check if user has Gmail integration
  const checkIntegration = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('integration_authorizations')
        .select('*')
        .eq('user_id', user.id)
        .eq('provider', 'gmail')
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error checking integration:', error);
        return;
      }

      setIntegration(data);
    } catch (error) {
      console.error('Error checking integration:', error);
    }
  };

  // Connect Gmail account
  const connectGmail = async () => {
    if (!isAuthenticated) {
      toast({
        title: "Errore",
        description: "Devi essere autenticato per connettere Gmail",
        variant: "destructive",
      });
      return;
    }

    setIsConnecting(true);

    try {
      const { data, error } = await supabase.functions.invoke('composio-gmail', {
        body: { action: 'start_auth' }
      });

      if (error) {
        throw error;
      }

      if (data.success) {
        // Store integration ID in session storage for callback
        sessionStorage.setItem('composio_integration_id', data.integrationId);
        
        // Redirect to Composio OAuth
        window.location.href = data.authUrl;
      } else {
        throw new Error(data.error || 'Failed to start authentication');
      }
    } catch (error) {
      console.error('Error connecting Gmail:', error);
      toast({
        title: "Errore",
        description: "Impossibile connettere Gmail. Riprova più tardi.",
        variant: "destructive",
      });
    } finally {
      setIsConnecting(false);
    }
  };

  // Handle OAuth callback
  const handleCallback = async (code: string, state: string) => {
    if (!isAuthenticated) return;

    setIsLoading(true);

    try {
      const { data, error } = await supabase.functions.invoke('composio-gmail', {
        body: { 
          action: 'handle_callback',
          code,
          state
        }
      });

      if (error) {
        throw error;
      }

      if (data.success) {
        toast({
          title: "Successo",
          description: "Gmail connesso con successo!",
        });
        
        // Remove integration ID from session storage
        sessionStorage.removeItem('composio_integration_id');
        
        // Refresh integration status
        await checkIntegration();
        
        // Fetch emails
        await fetchEmails();
        
        return true;
      } else {
        throw new Error(data.error || 'Failed to complete authentication');
      }
    } catch (error) {
      console.error('Error handling callback:', error);
      toast({
        title: "Errore",
        description: "Impossibile completare l'autenticazione Gmail",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch emails from Gmail
  const fetchEmails = async () => {
    if (!integration) return;

    setIsLoading(true);

    try {
      const { data, error } = await supabase.functions.invoke('composio-gmail', {
        body: { action: 'get_emails' }
      });

      if (error) {
        throw error;
      }

      if (data.success) {
        // Transform Gmail data to our format
        const transformedEmails: EmailData[] = data.emails.map((email: any) => ({
          id: email.id,
          sender: email.payload?.headers?.find((h: any) => h.name === 'From')?.value || 'Unknown',
          subject: email.payload?.headers?.find((h: any) => h.name === 'Subject')?.value || 'No Subject',
          preview: email.snippet || '',
          time: new Date(parseInt(email.internalDate)).toLocaleDateString(),
          isRead: !email.labelIds?.includes('UNREAD'),
          isStarred: email.labelIds?.includes('STARRED') || false,
          body: email.payload?.body?.data || email.snippet,
        }));

        setEmails(transformedEmails);
      } else {
        throw new Error(data.error || 'Failed to fetch emails');
      }
    } catch (error) {
      console.error('Error fetching emails:', error);
      toast({
        title: "Errore",
        description: "Impossibile recuperare le email",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Send email
  const sendEmail = async (to: string, subject: string, body: string) => {
    if (!integration) {
      toast({
        title: "Errore",
        description: "Gmail non connesso",
        variant: "destructive",
      });
      return false;
    }

    try {
      const { data, error } = await supabase.functions.invoke('composio-gmail', {
        body: { 
          action: 'send_email',
          email_data: { to, subject, body }
        }
      });

      if (error) {
        throw error;
      }

      if (data.success) {
        toast({
          title: "Successo",
          description: "Email inviata con successo!",
        });
        return true;
      } else {
        throw new Error(data.error || 'Failed to send email');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      toast({
        title: "Errore",
        description: "Impossibile inviare l'email",
        variant: "destructive",
      });
      return false;
    }
  };

  // Disconnect Gmail
  const disconnectGmail = async () => {
    if (!integration) return;

    try {
      const { error } = await supabase
        .from('integration_authorizations')
        .update({ is_active: false })
        .eq('user_id', user?.id)
        .eq('provider', 'gmail');

      if (error) {
        throw error;
      }

      setIntegration(null);
      setEmails([]);

      toast({
        title: "Successo",
        description: "Gmail disconnesso con successo",
      });
    } catch (error) {
      console.error('Error disconnecting Gmail:', error);
      toast({
        title: "Errore",
        description: "Impossibile disconnettere Gmail",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      checkIntegration();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (integration) {
      fetchEmails();
    }
  }, [integration]);

  return {
    integration,
    emails,
    isLoading,
    isConnecting,
    isConnected: !!integration,
    connectGmail,
    disconnectGmail,
    handleCallback,
    fetchEmails,
    sendEmail,
  };
};
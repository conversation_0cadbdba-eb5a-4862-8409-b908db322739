# Deal Components Options

This document describes the available props (options) for the `DealCard` and `DealCarousel` components found in `src/components/deals/core`.


## DealCarousel

`DealCarousel` renders a horizontal list of deals using the `Carousel` component. It provides configurable behavior through these props:

| Prop | Type | Default | Description |
| --- | --- | --- | --- |
| `deals` | `Deal[]` | – | Array of deals to render in the carousel. |
| `title` | `string` | – | Section heading shown above the carousel. |
| `isLoading` | `boolean` | `false` | Shows skeleton cards instead of content when `true`. |
| `variant` | `'full' \| 'compact'` | `'compact'` | Passed to each `DealCard` to choose the card layout. |
| `showFavoriteButton` | `boolean` | `false` | Displays the favorite button on each card. |
| `emptyMessage` | `string` | `'Nessuna offerta disponibile'` | Message shown when the `deals` array is empty. |
| `onDealClick` | `(dealId: string) => void` | – | Called when a card is clicked. The deal id is passed to the handler. |
| `className` | `string` | – | Additional CSS classes for the outer container. |
| `renderDealCard` | `(deal: Deal) => React.ReactNode` | – | Optional custom rendering function. If provided, it overrides the default `DealCard` rendering. |
| `showDetailedTimeSlots` | `boolean` | `false` | Forces the detailed time slot view inside each card. |
| `showNavigationArrows` | `boolean` | `false` | Displays previous/next arrows when there is more than one card. |
| `hideTimeSlots` | `boolean` | `false` | Hides time slot information in the underlying `DealCard`. |
| `selectedTime` | `string` | – | Time value forwarded to each `DealCard` to highlight a specific slot. |
| `selectedDate` | `string` | – | Date value forwarded to each `DealCard` to highlight a specific slot. |

Default values for these props are set inside the component at lines 33–48 of `DealCarousel.tsx`.


## DealCard

`DealCard` displays information about a specific deal. It supports two layouts and multiple interactive features. The component accepts the following props:

| Prop | Type | Default | Description |
| --- | --- | --- | --- |
| `deal` | `Deal` | – | The deal object containing all information to display. |
| `variant` | `'full' \| 'compact'` | `'full'` | Selects between the full layout (with description and time slots) and a compact version. |
| `onFavoriteClick` | `() => void` | – | Callback invoked when the favorite button is pressed. Event propagation is stopped inside the component. |
| `isFavorite` | `boolean` | `false` | Controls the state of the favorite button. When `true`, the button is filled with the brand color. |
| `onClick` | `() => void` | – | Called when the card is clicked. Can be used to navigate to the deal detail page. |
| `showFavoriteButton` | `boolean` | `false` | Toggles display of the favorite heart icon in the top-left corner of the image. |
| `showDetailedTimeSlots` | `boolean` | `false` | When `true`, always displays the `DealTimeSlots` section even in compact mode. |
| `isBusinessOwner` | `boolean` | `false` | If the current user owns the deal, edit/duplicate/delete actions are shown over the image. |
| `onEditClick` | `() => void` | – | Called when the edit button is clicked by the owner. |
| `onDeleteClick` | `() => void` | – | Called when the delete button is clicked by the owner. |
| `onDuplicateClick` | `() => void` | – | Called when the duplicate button is clicked by the owner. |
| `hideTimeSlots` | `boolean` | `false` | Hides the list of available time slots. Useful for near-by deal listings. |
| `selectedTime` | `string` | – | Selected start time for nearby deal filtering. Used to highlight a specific slot. |
| `selectedDate` | `string` | – | Selected date for nearby deal filtering. Combined with `selectedTime` to pick a slot. |

The default values are defined when destructuring props in the component implementation at lines 79–93 of `DealCard.tsx`.


---

Both components support customization via these props, enabling flexible display of deals and integration with navigation or booking flows.

import React, { useRef } from 'react';
import { X, Store } from "lucide-react";
import { cn } from "@/lib/utils";
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel";
import DealCard from '@/components/deals/core/DealCard';
import { Business } from './types';

interface BusinessInfoSlideProps {
  selectedBusiness: Business | null;
  isSlideVisible: boolean;
  setIsSlideVisible: (visible: boolean) => void;
  clearNavigation: () => void;
  handleDealClick: (dealId: string) => void;
}

export const BusinessInfoSlide = ({
  selectedBusiness,
  isSlideVisible,
  setIsSlideVisible,
  clearNavigation,
  handleDealClick,
}: BusinessInfoSlideProps) => {
  const slideRef = useRef<HTMLDivElement>(null);
  const touchStartY = useRef<number | null>(null);
  
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartY.current = e.touches[0].clientY;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (touchStartY.current === null || !slideRef.current) return;

    const touchY = e.touches[0].clientY;
    const diff = touchY - touchStartY.current;

    if (diff > 0) {
      slideRef.current.style.transform = `translateY(${diff}px)`;
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (touchStartY.current === null || !slideRef.current) return;

    const touchY = e.changedTouches[0].clientY;
    const diff = touchY - touchStartY.current;

    slideRef.current.style.transform = "";

    if (diff > 100) {
      setIsSlideVisible(false);
      setTimeout(() => {
        clearNavigation();
      }, 300);
    }

    touchStartY.current = null;
  };

  if (!selectedBusiness) return null;

  return (
    <div
      className={cn(
        "fixed bottom-0 left-0 right-0 bg-white rounded-t-2xl z-40 transition-transform duration-300 ease-in-out transform",
        isSlideVisible ? "translate-y-0" : "translate-y-full"
      )}
      ref={slideRef}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <div className="relative bg-white rounded-t-xl shadow-lg p-4 mx-4 max-h-[70vh] overflow-y-auto">
        <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gray-300 rounded-full mb-4"></div>

        <button
          onClick={() => {
            setIsSlideVisible(false);
            clearNavigation();
          }}
          className="absolute right-2 top-2 p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
        >
          <X className="h-4 w-4 text-gray-600" />
        </button>

        <div className="flex items-center gap-2 mb-4 mt-4">
          <Store className="h-5 w-5 text-brand-primary" />
          <h3 className="font-semibold text-lg">{selectedBusiness.name}</h3>
        </div>

        {selectedBusiness.deals && selectedBusiness.deals.length > 0 ? (
          <Carousel className="w-full">
            <CarouselContent>
              {selectedBusiness.deals.map((deal) => (
                <CarouselItem key={deal.id}>
                  <DealCard
                    deal={deal}
                    variant="compact"
                    onClick={() => handleDealClick(deal.id)}
                    showVisitBusiness={true}
                  />
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        ) : (
          <p className="text-gray-500 text-center py-4">
            Nessuna offerta disponibile al momento
          </p>
        )}
      </div>
    </div>
  );
};

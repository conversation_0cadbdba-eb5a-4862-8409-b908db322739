
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { MessageCircle } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAuth } from "@/hooks/auth/useAuth";

interface DealContactButtonProps {
  dealId: string;
  businessId: string;
  businessOwnerId: string;
}

const DealContactButton = ({ dealId, businessId, businessOwnerId }: DealContactButtonProps) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isCreating, setIsCreating] = useState(false);
 
  const handleContact = async () => {
    if (!user) {
      navigate('/login');
      return;
    }
 
    setIsCreating(true);
    try {
      // 1. Crea la conversazione
      const { data: conversation, error: convError } = await supabase
        .from('conversations')
        .insert({
          deal_id: dealId,
          business_id: businessId,
          type: 'inquiry',
          created_by: user.id, // Aggiunta la proprietà created_by richiesta
          is_active: true // Aggiunta la proprietà is_active con valore di default
        })
        .select()
        .single();

      if (convError) throw convError;

      // 2. Aggiungi i partecipanti
      const { error: partError } = await supabase
        .from('conversation_participants')
        .insert([
          {
            conversation_id: conversation.id,
            user_id: user.id,
            role: 'customer'
          },
          {
            conversation_id: conversation.id,
            user_id: businessOwnerId,
            role: 'owner'
          }
        ]);

      if (partError) throw partError;

      // 3. Naviga alla conversazione
      navigate(`/conversations/${conversation.id}`);
    } catch (error) {
      console.error('Error creating conversation:', error);
      toast.error("Errore nella creazione della conversazione");
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <>
    <button
      onClick={handleContact}
      disabled={isCreating}
      className="flex items-center gap-2 bg-brand-primary text-white px-4 py-2 rounded-lg hover:bg-brand-primary/90 disabled:opacity-50"
    >
      <MessageCircle className="h-5 w-5" />
      <span>{isCreating ? "Creazione chat..." : "Contatta"}</span>
    </button>

    </>
  );
};

export default DealContactButton;

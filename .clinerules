# CatchUp - Cline AI Rules
# Progressive Web App (PWA) for connecting customers with businesses offering discounted services
# Built with React, TypeScript, Vite, Tailwind CSS, Supabase and Google Maps API

# TypeScript Best Practices
- "Define precise interfaces for all data structures and component props"
- "Use union types instead of enums for better type safety"
- "Implement strict null checks and avoid using 'any' type"
- "Create reusable type utilities for common patterns"
- "Define proper return types for all functions and methods"
- "Use generics for reusable components and hooks"
- "Implement proper type guards for runtime type checking"
- "Create dedicated type files (.d.ts) for third-party libraries missing types"
- "Use type inference where it improves readability"
- "Maintain consistent naming conventions for types and interfaces"

# Tailwind CSS Implementation
- "Use Tailwind's utility classes directly in TSX for consistent styling"
- "Create component-specific Tailwind classes using @apply for reusable patterns"
- "Implement a consistent responsive design system using Tailwind breakpoints"
- "Extend Tailwind theme for CatchUp's brand colors and typography"
- "Use Tailwind's dark mode utilities for alternative color schemes"
- "Create a design system using Tailwind that matches CatchUp's brand identity"
- "Organize Tailwind classes in a consistent order (layout, dimensions, spacing, etc.)"
- "Leverage Tailwind plugins for specific UI requirements"
- "Use Tailwind's JIT mode for development to improve build times"
- "Purge unused Tailwind classes in production builds"

# User Experience Focus
- "Implement intuitive navigation with clear user flows based on the EPIC requirements"
- "Design mobile-first, considering thumb zones for important actions"
- "Ensure all interactive elements have appropriate touch targets (minimum 44x44px)"
- "Use skeleton screens instead of spinners for better perceived performance"
- "Follow a consistent visual hierarchy to guide user attention"
- "Implement responsive design patterns for all screen sizes"

# Performance Optimization
- "Use React.lazy and Suspense for code-splitting critical components"
- "Implement proper caching strategies for Supabase data"
- "Optimize images with modern formats (WebP) and responsive loading"
- "Minimize JavaScript bundle size using dynamic imports"
- "Ensure Core Web Vitals metrics are met (LCP, FID, CLS)"
- "Implement service workers for offline functionality"

# State Management
- "Use React Context for global app state that doesn't change frequently"
- "Implement React Query for server state management with Supabase"
- "Maintain separation between UI state and application data"
- "Use local component state for UI-specific interactions"
- "Implement proper loading, error, and success states for all async operations"

# API Integration
- "Create dedicated service layers for Supabase operations"
- "Implement proper error handling for all API requests"
- "Use environment variables for API keys and configuration"
- "Cache Google Maps API responses appropriately to minimize API calls"
- "Design resilient API integration that gracefully handles service outages"

# Authentication & Authorization
- "Implement Supabase Auth for user registration and login"
- "Create separate user profiles for customers and businesses"
- "Secure routes based on user roles and permissions"
- "Handle auth state persistently across sessions"
- "Implement proper password recovery and account management flows"

# Form Implementation
- "Use form validation libraries (Formik or React Hook Form) consistently"
- "Implement inline validation with clear error messages"
- "Use appropriate input types for mobile-friendly forms"
- "Ensure all forms are accessible with proper labels and ARIA attributes"
- "Implement multi-step forms for complex processes like business registration"

# Maps Integration
- "Lazy load Google Maps components to improve initial load time"
- "Implement location-based searches with appropriate indexing"
- "Use clustering for handling multiple map markers in dense areas"
- "Provide fallback UI when geolocation permissions are denied"
- "Optimize map rendering for performance on mobile devices"

# Booking System
- "Design calendar views with proper time slot visualization"
- "Implement real-time availability using Supabase's real-time capabilities"
- "Create clear booking confirmation flows with appropriate status updates"
- "Allow scheduling modifications with proper validation"
- "Implement notification systems for booking reminders"

# AI Integration
- "Develop modular AI recommendation services based on user preferences"
- "Implement progressive data collection for improving AI suggestions"
- "Create middleware for AI service integration with appropriate fallbacks"
- "Design AI assistants that respect user privacy and data minimization"
- "Use AI for dynamic pricing suggestions to businesses"

# Accessibility
- "Ensure WCAG 2.1 AA compliance for all components"
- "Implement proper keyboard navigation throughout the application"
- "Use semantic HTML elements appropriately"
- "Provide text alternatives for all non-text content"
- "Test with screen readers and other assistive technologies"
- "Implement high contrast mode and text scaling support"

# Internationalization
- "Design with multilingual support from the start"
- "Use i18next or similar library for translation management"
- "Handle right-to-left (RTL) layouts appropriately"
- "Format dates, times, and currencies according to locale"
- "Support regional differences in address formats and phone numbers"

# Testing
- "Write unit tests for all critical business logic"
- "Implement integration tests for key user flows"
- "Create E2E tests for critical paths (registration, booking, payment)"
- "Use testing-library/react for component testing"
- "Implement visual regression testing for UI components"

# Error Handling
- "Create user-friendly error pages and messages"
- "Implement global error boundaries to prevent application crashes"
- "Log errors appropriately for debugging purposes"
- "Handle network errors gracefully with retry mechanisms"
- "Provide clear recovery paths for users when errors occur"

# Progressive Web App Features
- "Implement a comprehensive manifest.json file"
- "Create optimized icons for various platforms and sizes"
- "Design offline-first experiences where appropriate"
- "Implement push notifications for important updates"
- "Ensure proper install prompts and home screen experiences"

# Payment Integration
- "Use secure payment processing methods"
- "Implement clear transactional interfaces with proper confirmation"
- "Ensure PCI compliance for all payment processing"
- "Create comprehensive receipt and invoice generation"
- "Support multiple payment methods appropriate for target markets"

# Analytics
- "Implement proper event tracking for key user actions"
- "Create dashboards for business users to track performance"
- "Respect user privacy and implement appropriate consent mechanisms"
- "Track conversion funnels for key processes"
- "Implement heat mapping for UX improvements"

# Security
- "Follow OWASP security best practices"
- "Implement proper input sanitization throughout"
- "Set appropriate Content Security Policy headers"
- "Use HTTPS for all connections"
- "Regularly update dependencies to address security vulnerabilities"
- "Implement rate limiting for critical API endpoints"

# Documentation
- "Document component usage and props with JSDoc comments"
- "Create storybook stories for key components"
- "Maintain up-to-date API documentation"
- "Document architectural decisions and patterns"
- "Provide clear comments for complex business logic"

# Code Structure & Organization
- "Use feature-based folder structure to organize components and logic"
- "Keep component files focused on a single responsibility"
- "Maintain consistent file naming conventions across the project"
- "Implement proper code splitting for better loading performance"
- "Extract reusable logic into custom hooks"

import { motion, AnimatePresence } from "framer-motion";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAuth } from "@/hooks/auth/useAuth";
import useUserPreferencesManager from "@/hooks/useUserPreferencesManager";
import { StepIndicator } from "@/components/preferences/StepIndicator";
import { CategoriesStep } from "@/components/preferences/steps/CategoriesStep";
import { PriceRangeStep } from "@/components/preferences/steps/PriceRangeStep";
import { NotificationsStep } from "@/components/preferences/steps/NotificationsStep";

interface ServiceCategory {
  id: string;
  name: string;
  icon: string;
  selected: boolean;
}

interface PriceRange {
  id: string;
  name: string;
  description: string;
  selected: boolean;
}

interface NotificationPreference {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
}

const PersonalizePreferences = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    preferences,
    updatePreferences,
    isLoading: preferencesLoading,
  } = useUserPreferencesManager();
  const [isLoading, setIsLoading] = useState(false);
  const [serviceCategories, setServiceCategories] = useState<ServiceCategory[]>([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const totalSteps = 3;
  const stepLabels = ["Categorie", "Prezzo", "Notifiche"];

  // Determine if we're in edit mode based on whether preferences exist
  useEffect(() => {
    console.log("Preferences:", preferences);
    if (preferences) {
      setIsEditMode(true);

      // Pre-populate selected categories if in edit mode
      if (preferences.categories && preferences.categories.length > 0) {
        setServiceCategories((prev) =>
          prev.map((category) => ({
            ...category,
            selected: preferences.categories.includes(category.id),
          }))
        );
      }

      // Pre-populate price range selection
      if (preferences.price_range) {
        setPriceRanges((prev) =>
          prev.map((range) => ({
            ...range,
            selected: range.id === preferences.price_range,
          }))
        );
      }

      // Pre-populate notification preferences
      if (preferences.notification_preferences) {
        setNotificationPreferences((prev) =>
          prev.map((pref) => ({
            ...pref,
            enabled: preferences.notification_preferences[pref.id] || false,
          }))
        );
      }
    }
  }, [preferences]);

  const [priceRanges, setPriceRanges] = useState<PriceRange[]>([
    {
      id: "budget",
      name: "Economico",
      description: "Risparmia con le migliori offerte",
      selected: true,
    },
    {
      id: "mid-range",
      name: "Medio",
      description: "Equilibrio tra qualità e prezzo",
      selected: false,
    },
    {
      id: "premium",
      name: "Premium",
      description: "Esperienze e servizi di lusso",
      selected: false,
    },
  ]);

  const [notificationPreferences, setNotificationPreferences] = useState<
    NotificationPreference[]
  >([
    {
      id: "special_offers",
      name: "Offerte speciali",
      description: "Ricevi notifiche sugli sconti",
      enabled: true,
    },
    {
      id: "appointment_reminders",
      name: "Promemoria appuntamenti",
      description: "Promemoria sulle tue prenotazioni",
      enabled: true,
    },
    {
      id: "new_businesses",
      name: "Nuove attività",
      description: "Aggiornamenti su nuovi posti",
      enabled: false,
    },
    {
      id: "voice_assistant",
      name: "Assistente Vocale",
      description: "Usa comandi vocali per cercare e prenotare",
      enabled: true,
    },
  ]);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const { data, error } = await supabase
          .from("categories")
          .select("*")
          .order("name");

        if (error) {
          toast.error("Errore nel caricamento delle categorie");
          return;
        }

        if (data) {
          const formattedCategories = data.map((category) => ({
            id: category.id,
            name: category.name,
            icon: category.icon || "Scissors",
            selected: preferences?.categories?.includes(category.id) || false,
          }));
          setServiceCategories(formattedCategories);
        }
      } catch (error) {
        toast.error("Si è verificato un errore");
      }
    };

    fetchCategories();
  }, [preferences?.categories]);

  const toggleServiceCategory = (id: string) => {
    setServiceCategories((prev) =>
      prev.map((category) =>
        category.id === id
          ? { ...category, selected: !category.selected }
          : category
      )
    );
  };

  const selectPriceRange = (id: string) => {
    setPriceRanges((prev) =>
      prev.map((range) => ({
        ...range,
        selected: range.id === id,
      }))
    );
  };

  const toggleNotificationPreference = (id: string) => {
    setNotificationPreferences((prev) =>
      prev.map((pref) =>
        pref.id === id ? { ...pref, enabled: !pref.enabled } : pref
      )
    );
  };

  const handleContinue = async () => {
    if (!user) {
      toast.error("Utente non autenticato");
      navigate("/login");
      return;
    }

    setIsLoading(true);

    try {
      const selectedCategories = serviceCategories
        .filter((category) => category.selected)
        .map((category) => category.id);

      const selectedPriceRange =
        priceRanges.find((range) => range.selected)?.id || "budget";

      const notificationSettings = {
        special_offers:
          notificationPreferences.find((p) => p.id === "special_offers")
            ?.enabled || false,
        appointment_reminders:
          notificationPreferences.find((p) => p.id === "appointment_reminders")
            ?.enabled || false,
        new_businesses:
          notificationPreferences.find((p) => p.id === "new_businesses")
            ?.enabled || false,
        voice_assistant:
          notificationPreferences.find((p) => p.id === "voice_assistant")
            ?.enabled || false,
      };

      // Save preferences using our hook
      const success = await updatePreferences({
        categories: selectedCategories,
        price_range: selectedPriceRange,
        notification_preferences: notificationSettings,
        onboarding_completed: true,
      });

      if (success) {
        toast.success("Preferenze salvate con successo!");
        navigate("/");
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error("Si è verificato un errore");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = async () => {
    if (user) {
      // Mark onboarding as completed even if skipped
      await updatePreferences({
        onboarding_completed: true,
      });
    }
    navigate("/");
  };

  const handleNext = () => {
    if (currentStep < totalSteps - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleContinue();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: // Categories step
        return serviceCategories.some(cat => cat.selected);
      case 1: // Price range step
        return priceRanges.some(range => range.selected);
      case 2: // Notifications step
        return true; // Always can proceed from notifications
      default:
        return false;
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <CategoriesStep
            categories={serviceCategories}
            onToggleCategory={toggleServiceCategory}
          />
        );
      case 1:
        return (
          <PriceRangeStep
            priceRanges={priceRanges}
            onSelectPriceRange={selectPriceRange}
          />
        );
      case 2:
        return (
          <NotificationsStep
            notificationPreferences={notificationPreferences}
            onToggleNotification={toggleNotificationPreference}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-accent/5 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      <div className="absolute top-0 right-0 w-72 h-72 bg-gradient-to-br from-primary/20 to-transparent rounded-full blur-3xl" />
      <div className="absolute bottom-0 left-0 w-72 h-72 bg-gradient-to-tr from-secondary/20 to-transparent rounded-full blur-3xl" />
      
      <header className="fixed top-0 left-0 right-0 bg-white/80 backdrop-blur-xl z-50 p-4 max-w-md mx-auto flex items-center justify-between border-b border-border/50 shadow-sm">
        <motion.button
          whileHover={{ scale: 1.05, backgroundColor: "hsl(var(--accent))" }}
          whileTap={{ scale: 0.95 }}
          onClick={currentStep === 0 ? () => navigate(-1) : handlePrevious}
          className="text-foreground p-2.5 rounded-xl hover:bg-accent transition-all duration-200 shadow-sm"
        >
          <ArrowLeft className="h-5 w-5" />
        </motion.button>
        
        <motion.h1 
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-lg font-semibold text-foreground"
        >
          {isEditMode ? "Modifica Preferenze" : "Personalizza la tua esperienza"}
        </motion.h1>
        
        {!isEditMode && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleSkip}
            className="text-muted-foreground text-sm font-medium hover:text-foreground transition-colors duration-200 px-3 py-2 rounded-lg hover:bg-accent"
          >
            Salta
          </motion.button>
        )}
        
        {isEditMode && <div className="w-10" />}
      </header>

      <div className="pt-20 p-4 max-w-md mx-auto">

        {!isEditMode && (
          <StepIndicator
            currentStep={currentStep}
            totalSteps={totalSteps}
            stepLabels={stepLabels}
          />
        )}

        <main className="min-h-[60vh] flex flex-col pb-32 relative">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20, filter: "blur(4px)" }}
              animate={{ opacity: 1, x: 0, filter: "blur(0px)" }}
              exit={{ opacity: 0, x: -20, filter: "blur(4px)" }}
              transition={{ 
                duration: 0.4, 
                ease: [0.23, 1, 0.32, 1],
                staggerChildren: 0.1
              }}
              className="w-full"
            >
              {renderCurrentStep()}
            </motion.div>
          </AnimatePresence>
          
          {/* Progress dots */}
          {!isEditMode && (
            <motion.div 
              className="flex justify-center gap-2 mt-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              {Array.from({ length: totalSteps }).map((_, index) => (
                <motion.div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    index === currentStep 
                      ? 'bg-primary w-8' 
                      : index < currentStep 
                        ? 'bg-primary/60' 
                        : 'bg-muted-foreground/20'
                  }`}
                  whileHover={{ scale: 1.2 }}
                  layoutId={`progress-dot-${index}`}
                />
              ))}
            </motion.div>
          )}
        </main>
      </div>

      <footer className="fixed bottom-0 left-0 right-0 bg-white/90 backdrop-blur-xl z-40 p-6 max-w-md mx-auto border-t border-border/30 shadow-lg">
        <motion.button
          whileHover={{ 
            scale: 1.02,
            boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
          }}
          whileTap={{ scale: 0.98 }}
          onClick={handleNext}
          disabled={isLoading || preferencesLoading || !canProceed()}
          className="w-full bg-gradient-to-r from-primary to-secondary text-white py-4 px-6 rounded-2xl font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3 shadow-lg transition-all duration-200 relative overflow-hidden group"
        >
          {/* Button shine effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
          
          {isLoading || preferencesLoading ? (
            <motion.div 
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
            />
          ) : currentStep === totalSteps - 1 ? (
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex items-center gap-2"
            >
              {isEditMode ? "Salva modifiche" : "Completa configurazione"}
            </motion.span>
          ) : (
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex items-center gap-2"
            >
              Continua
              <motion.div
                animate={{ x: [0, 3, 0] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                <ArrowRight className="h-5 w-5" />
              </motion.div>
            </motion.span>
          )}
        </motion.button>
      </footer>
    </div>
  );
};

export default PersonalizePreferences;

import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SwipeHintOverlayProps {
  show: boolean;
  onDismiss: () => void;
}

const SwipeHintOverlay = ({ show, onDismiss }: SwipeHintOverlayProps) => {
  const [currentStep, setCurrentStep] = useState(0);
  
  const steps = [
    {
      title: "Gestisci i tuoi inviti",
      description: "Scorri le carte degli inviti ai gruppi per accettare o rifiutare rapidamente",
      animation: "swipe-demo"
    },
    {
      title: "Scorri a destra per accettare",
      description: "Un gesto veloce verso destra accetterà l'invito al gruppo",
      animation: "swipe-right"
    },
    {
      title: "Scorri a sinistra per rifiutare", 
      description: "Un gesto verso sinistra rifiuterà l'invito",
      animation: "swipe-left"
    }
  ];

  useEffect(() => {
    if (!show) return;
    
    const timer = setInterval(() => {
      setCurrentStep((prev) => (prev + 1) % steps.length);
    }, 3000);
    
    return () => clearInterval(timer);
  }, [show]);

  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl p-6 max-w-sm w-full shadow-2xl">
        <div className="flex justify-between items-start mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            Nuova Funzionalità
          </h2>
          <button 
            onClick={onDismiss}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="text-center mb-6">
          {/* Animated Demo */}
          <div className="relative h-20 mb-4 flex items-center justify-center">
            {steps[currentStep].animation === 'swipe-demo' && (
              <div className="flex items-center gap-2 animate-pulse">
                <ChevronLeft className="h-6 w-6 text-red-500 animate-bounce" />
                <div className="w-16 h-10 bg-blue-100 rounded-lg border-2 border-blue-300" />
                <ChevronRight className="h-6 w-6 text-green-500 animate-bounce" />
              </div>
            )}
            
            {steps[currentStep].animation === 'swipe-right' && (
              <div className="flex items-center gap-2">
                <div className="w-16 h-10 bg-blue-100 rounded-lg border-2 border-blue-300 animate-slide-in-right" />
                <ChevronRight className="h-8 w-8 text-green-500 animate-pulse" />
              </div>
            )}
            
            {steps[currentStep].animation === 'swipe-left' && (
              <div className="flex items-center gap-2">
                <ChevronLeft className="h-8 w-8 text-red-500 animate-pulse" />
                <div className="w-16 h-10 bg-blue-100 rounded-lg border-2 border-blue-300 animate-slide-in-left" />
              </div>
            )}
          </div>
          
          <h3 className="font-medium text-gray-900 mb-2">
            {steps[currentStep].title}
          </h3>
          <p className="text-sm text-gray-600 leading-relaxed">
            {steps[currentStep].description}
          </p>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex gap-1">
            {steps.map((_, index) => (
              <div 
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentStep ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
          
          <Button 
            onClick={onDismiss}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6"
          >
            Ho Capito
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SwipeHintOverlay;

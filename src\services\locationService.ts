    // src/services/locationService.ts

import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

/**
 * Location service for managing and tracking user location in the CatchUp application.
 * Handles geolocation, demo mode, caching, and fallback scenarios.
 * @module LocationService
 */

/**
 * Represents geographical coordinates with latitude and longitude.
 */
export interface LocationCoordinates {
  lat: number;
  lng: number;
}

/**
 * Possible sources of location data in the application.
 */
export type LocationSource = 'geolocation' | 'demo' | 'fallback' | 'cache' | 'none';

/**
 * Represents the complete state of the location service.
 */
export interface LocationState {
  /** Current coordinates of the user or demo location */
  coordinates: LocationCoordinates | null;
  /** Whether demo mode is enabled */
  demoEnabled: boolean;
  /** Default coordinates used in demo mode */
  demoLocation: LocationCoordinates;
  /** Fallback coordinates used when geolocation fails */
  fallbackLocation: LocationCoordinates;
  /** Whether fallback mode is active */
  fallbackMode: boolean;
  /** Whether the service is currently loading location data */
  isLoading: boolean;
  /** Whether geolocation permission has been denied */
  isPermissionDenied: boolean;
  /** Current source of location data */
  source: LocationSource;
  /** Timestamp of last location update */
  lastUpdated: number | null;
}

/**
 * Callback function type for location state updates.
 */
export type LocationSubscriber = (state: LocationState) => void;

/**
 * Service class for managing location functionality in the CatchUp application.
 * Provides features for:
 * - Real-time geolocation tracking
 * - Demo mode for testing
 * - Location caching
 * - Fallback handling
 * - State management and subscriptions
 */
class LocationService {
  /** Current state of the location service */
  private state: LocationState = {
    coordinates: null,
    demoEnabled: false,
    demoLocation: { lat: 45.4666, lng: 9.1832 }, // Milano, default
    fallbackLocation: { lat: 45.4671, lng: 9.1526 }, // Sempre Milano ma in un punto diverso
    fallbackMode: false,
    isLoading: true,
    isPermissionDenied: false,
    source: 'none',
    lastUpdated: null
  };

  /** Array of subscriber callbacks for state updates */
  private subscribers: LocationSubscriber[] = [];
  /** Flag to prevent multiple initializations */
  private isInitialized: boolean = false;
  /** ID of the current geolocation watch */
  private watchId: number | null = null;
  /** Counter for geolocation permission request attempts */
  private permissionRetries: number = 0;
  /** Maximum number of permission request retries */
  private MAX_RETRIES = 3;

  /**
   * Initializes the location service.
   * - Loads cached location if available
   * - Fetches global settings from database
   * - Starts geolocation tracking if demo mode is disabled
   * @returns Promise that resolves when initialization is complete
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    try {
      // Try to load from cache first
      this.loadFromCache();
      
      // Fetch settings from database
      await this.fetchGlobalSettings();
      
      // If demo mode is not enabled, try to get real location
      if (!this.state.demoEnabled) {
        this.requestGeolocation();
      }
      
      this.isInitialized = true;
    } catch (error) {
      console.error("Failed to initialize location service:", error);
      this.updateState({
        coordinates: this.state.fallbackLocation,
        isLoading: false,
        source: 'fallback'
      });
    }
  }

  /**
   * Subscribes to location state updates.
   * @param callback - Function to be called when state changes
   * @returns Unsubscribe function to remove the subscription
   */
  public subscribe(callback: LocationSubscriber): () => void {
    this.subscribers.push(callback);
    
    // Immediately notify with current state
    callback(this.state);
    
    // Return unsubscribe function
    return () => {
      this.subscribers = this.subscribers.filter(cb => cb !== callback);
    };
  }


  /**
   * Updates the service state and notifies subscribers.
   * Also handles cache updates when coordinates change.
   * @param partialState - Partial state object to merge with current state
   */
  private updateState(partialState: Partial<LocationState>): void {
    this.state = {
      ...this.state,
      ...partialState,
      lastUpdated: partialState.lastUpdated || Date.now()
    };

    // Save to cache if coordinates are updated
    if (partialState.coordinates) {
      this.saveToCache();
    }
    
    // Notify all subscribers
    this.subscribers.forEach(callback => callback(this.state));
  }
  /**
   * Updates the location service based on mobility status (demo mode)
   * @param isDemoMode - Whether demo mode is enabled
   */
  public updateDemoModeStatus(isDemoMode: boolean): void {
    console.log("Update demo mode");
    this.setDemoMode(isDemoMode);
  }

  /**
   * Loads the last known location from localStorage cache.
   * Only uses cached data if it's less than 30 minutes old.
   */
  private loadFromCache(): void {
    try {
      const cachedData = localStorage.getItem('location_cache');
      if (cachedData) {
        const { coordinates, timestamp } = JSON.parse(cachedData);
        
        // Only use cache if it's less than 30 minutes old
        const thirtyMinutesInMs = 30 * 60 * 1000;
        if (coordinates && Date.now() - timestamp < thirtyMinutesInMs) {
          this.updateState({
            coordinates,
            isLoading: false,
            source: 'cache'
          });
        }
      }
    } catch (error) {
      console.error("Error loading location from cache:", error);
    }
  }

  /**
   * Saves the current location to localStorage cache.
   * Only caches non-cache-sourced locations.
   */
  private saveToCache(): void {
    try {
      if (this.state.coordinates && this.state.source !== 'cache') {
        localStorage.setItem('location_cache', JSON.stringify({
          coordinates: this.state.coordinates,
          timestamp: Date.now()
        }));
      }
    } catch (error) {
      console.error("Error saving location to cache:", error);
    }
  }

  /**
   * Fetches global settings from Supabase database.
   * Updates demo mode, demo location, and fallback location settings.
   * If user is authenticated, fetches from user_details, otherwise from globalsetting.
   */
  private async fetchGlobalSettings(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      let demoModeEnabled = false;
      let demoLocationData = null;
      let fallbackLocationData = null;

      if (user) {
        // User is authenticated - fetch from user_details
        const { data: userDetails, error: userError } = await supabase
          .from("user_details")
          .select("map_demo, demo_location")
          .eq("id", user.id)
          .maybeSingle();

        if (userError) {
          console.error("Error fetching user details:", userError);
          throw userError;
        }

        if (userDetails) {
          demoModeEnabled = userDetails.map_demo ?? false;
          demoLocationData = userDetails.demo_location;
        }

        // Always fetch fallback_location from global settings
        const { data: globalData, error: globalError } = await supabase
          .from("globalsetting")
          .select("fallback_location")
          .eq("id", 1)
          .maybeSingle();

        if (globalError) {
          console.error("Error fetching global fallback location:", globalError);
        } else if (globalData) {
          fallbackLocationData = globalData.fallback_location;
        }
      } else {
        // User is not authenticated - fetch from globalsetting
        const { data: globalData, error: globalError } = await supabase
          .from("globalsetting")
          .select("map_demo, demo_location, fallback_location")
          .eq("id", 1)
          .maybeSingle();

        if (globalError) {
          console.error("Error fetching global settings:", globalError);
          throw globalError;
        }

        if (globalData) {
          demoModeEnabled = globalData.map_demo ?? false;
          demoLocationData = globalData.demo_location;
          fallbackLocationData = globalData.fallback_location;
        }
      }

      // Process the fetched data
      const updates: Partial<LocationState> = {};
      
      // Update demo mode
      updates.demoEnabled = demoModeEnabled;

      // Parse demo location if available
      if (demoLocationData) {
        try {
          const demoLocationObj =
            typeof demoLocationData === "string"
              ? JSON.parse(demoLocationData)
              : demoLocationData;
          
          updates.demoLocation = demoLocationObj;
        } catch (e) {
          console.error("Error parsing demo location:", e);
        }
      }

      // Parse fallback location if available
      if (fallbackLocationData) {
        try {
          const fallbackLocationObj =
            typeof fallbackLocationData === "string"
              ? JSON.parse(fallbackLocationData)
              : fallbackLocationData;
          
          updates.fallbackLocation = fallbackLocationObj;
        } catch (e) {
          console.error("Error parsing fallback location:", e);
        }
      }

      this.updateState(updates);
      
      // Update coordinates based on demo mode
      if (demoModeEnabled) {
        // In demo mode, always use demo location (either updated or existing)
        const demoCoords = updates.demoLocation || this.state.demoLocation;
        this.updateState({
          coordinates: demoCoords,
          isLoading: false,
          source: 'demo'
        });
      } else if (!this.state.coordinates) {
        // Not in demo mode and no coordinates yet - this will trigger geolocation
        // Don't set coordinates here, let geolocation handle it
        this.updateState({
          isLoading: false
        });
      }
    } catch (error) {
      console.error("Error in global settings process:", error);
      
      // In case of error, use fallback location
      if (!this.state.coordinates) {
        this.updateState({
          coordinates: this.state.fallbackLocation,
          isLoading: false,
          source: 'fallback'
        });
      }
    }
  }

  /**
   * Initiates geolocation tracking using the browser's geolocation API.
   * Sets up continuous watching of user's position.
   */
  private requestGeolocation(): void {
    if (!navigator.geolocation) {
      this.handleGeolocationError(new Error("Geolocation not supported"));
      return;
    }

    // Clear any existing watch
    this.clearGeolocationWatch();

    // Set up watch for position updates
    this.watchId = navigator.geolocation.watchPosition(
      this.handleGeolocationSuccess.bind(this),
      this.handleGeolocationError.bind(this),
      {
        enableHighAccuracy: false,    // Changed to false for faster initial response
        timeout: 30000,               // Increased to 30 seconds
        maximumAge: 300000           // Increased to 5 minutes
      }
    );
  }

  /**
   * Handles successful geolocation updates.
   * Updates state with new coordinates and resets retry counter.
   * @param position - GeolocationPosition object from browser API
   */
  private handleGeolocationSuccess(position: GeolocationPosition): void {
    const coordinates = {
      lat: position.coords.latitude,
      lng: position.coords.longitude
    };
    
    this.permissionRetries = 0; // Reset retries on success

    this.updateState({
      coordinates,
      isLoading: false,
      isPermissionDenied: false,
      source: 'geolocation'
    });
  }

  /**
   * Handles geolocation errors including permission denied.
   * Implements retry logic for permission requests and fallback handling.
   * @param error - Error object from geolocation API
   */
  private handleGeolocationError(error: GeolocationPositionError | Error): void {
    console.error("Geolocation error:", error);
    
    // Handle permission denied specifically
    if (
      'code' in error && 
      error.code === 1 // Permission denied
    ) {
      this.permissionRetries++;
      
      if (this.permissionRetries >= this.MAX_RETRIES) {
        toast.error("Impossibile ottenere la posizione. Verifica le impostazioni di posizione.");
        
        this.updateState({
          isPermissionDenied: true,
          isLoading: false
        });
        
        // If no coordinates yet, use fallback
        if (!this.state.coordinates) {
          this.updateState({
            coordinates: this.state.fallbackLocation,
            source: 'fallback'
          });
        }
      } else {
        // Try again after a delay
        setTimeout(() => this.requestGeolocation(), 2000);
      }
      return;
    }
    
    // For other errors, use fallback if needed
    if (!this.state.coordinates) {
      toast.error("Impossibile ottenere la posizione attuale");
      
      this.updateState({
        coordinates: this.state.fallbackLocation,
        isLoading: false,
        source: 'fallback'
      });
    }
  }

  /**
   * Clears the current geolocation watch if active.
   */
  private clearGeolocationWatch(): void {
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId);
      this.watchId = null;
    }
  }

  /**
   * Sets whether fallback mode is active.
   * @param isActive - Whether to enable fallback mode
   */
  public setFallbackMode(isActive: boolean): void {
    this.updateState({ fallbackMode: isActive });
  }

  /**
   * Requests a new location update.
   * No effect in demo mode.
   */
  public refreshLocation(): void {
    if (this.state.demoEnabled) {
      // In demo mode, no need to refresh
      return;
    }
    
    this.updateState({ isLoading: true });
    this.requestGeolocation();
  }

  /**
   * Toggles demo mode on/off.
   * In demo mode, uses demo location instead of real geolocation.
   * @param enabled - Whether to enable demo mode
   */
  public setDemoMode(enabled: boolean): void {
    this.updateState({ 
      demoEnabled: enabled,
      isLoading: true 
    });
    
    if (enabled) {
      // In demo mode, use demo location
      this.updateState({
        coordinates: this.state.demoLocation,
        isLoading: false,
        source: 'demo'
      });
      
      // Clear any geolocation watch
      this.clearGeolocationWatch();
    } else {
      // In real mode, request geolocation
      this.requestGeolocation();
    }
  }

  /**
   * Cleans up service resources.
   * Clears geolocation watch and removes all subscribers.
   */
  public cleanup(): void {
    this.clearGeolocationWatch();
    this.subscribers = [];
  }

  /**
   * Updates the demo coordinates used by the service.
   * If demo mode is currently active, this immediately updates the current location.
   * @param coordinates - New demo coordinates to use
   */
  public updateDemoCoordinates(coordinates: LocationCoordinates): void {
    console.log("LocationService: Updating demo coordinates", coordinates);
    console.log("LocationService: Current state before update", {
      demoEnabled: this.state.demoEnabled,
      currentCoords: this.state.coordinates,
      currentDemoLocation: this.state.demoLocation
    });
    
    const updates: Partial<LocationState> = {
      demoLocation: coordinates
    };

    // If demo mode is currently active, also update current coordinates
    if (this.state.demoEnabled) {
      console.log("LocationService: Demo mode is active, updating current coordinates too");
      updates.coordinates = coordinates;
      updates.source = 'demo';
    } else {
      console.log("LocationService: Demo mode is not active, only updating demo location");
    }

    this.updateState(updates);
    
    console.log("LocationService: State after update", {
      demoEnabled: this.state.demoEnabled,
      currentCoords: this.state.coordinates,
      currentDemoLocation: this.state.demoLocation,
      source: this.state.source
    });
  }

  /**
   * Refreshes settings from the database.
   * Useful when user settings have been updated externally.
   */
  public async refreshSettings(): Promise<void> {
    console.log("LocationService: Refreshing settings from database");
    try {
      await this.fetchGlobalSettings();
    } catch (error) {
      console.error("Error refreshing settings:", error);
    }
  }

  /**
   * Returns a copy of the current location state.
   * @returns Current LocationState
   */
  public getState(): LocationState {
    return { ...this.state };
  }
}

// Create singleton instance
export const locationService = new LocationService();

// Initialize service on import
locationService.initialize();

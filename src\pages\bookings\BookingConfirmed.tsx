
import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ArrowLeft, Check } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import type { BookingWithDetails } from "@/types/booking";
import { parseBookingFromDB } from "@/types/booking";
import { QRCodeSVG } from "qrcode.react";

const BookingConfirmed = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [booking, setBooking] = useState<BookingWithDetails | null>(null);

  useEffect(() => {
    const fetchBooking = async () => {
      if (!id) return;

      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          deals (
            title,
            images,
            description,
            discounted_price,
            businesses (
              name,
              address
            )
          )
        `)
        .eq('id', id)
        .single();

      if (!error && data) {
        setBooking(parseBookingFromDB(data));
      }
    };

    fetchBooking();
  }, [id]);

  const handleBackToBookings = () => {
    navigate('/mybookings');
  };

  if (!booking) {
    return <div className="min-h-screen flex items-center justify-center">
      <div className="animate-pulse">Caricamento...</div>
    </div>;
  }

  const qrData = JSON.stringify({
    bookingId: booking.id,
    dealTitle: booking.deals?.title,
    businessName: booking.deals?.businesses?.name,
    date: booking.booking_date,
    time: booking.booking_time,
    status: booking.status
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <button onClick={handleBackToBookings} className="text-gray-600">
            <ArrowLeft className="h-6 w-6" />
          </button>
          <h1 className="text-xl font-semibold">Prenotazione Confermata</h1>
          <div className="w-6"></div>
        </div>
      </div>

      <main className="container mx-auto px-4 pt-20 pb-8">
        <div className="bg-white rounded-xl shadow-sm p-6 flex flex-col items-center text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <Check className="h-8 w-8 text-green-600" />
          </div>
          
          <h2 className="text-2xl font-semibold mb-2">Grazie per la tua prenotazione!</h2>
          <p className="text-gray-600 mb-8">
            La tua prenotazione è stata confermata. Mostra questo QR code al tuo arrivo.
          </p>

          <div className="bg-white p-4 rounded-xl shadow-sm border mb-8">
            <QRCodeSVG value={qrData} size={200} />
          </div>

          <button
            onClick={handleBackToBookings}
            className="w-full bg-brand-primary text-white py-3 rounded-full font-semibold"
          >
            Vedi le mie prenotazioni
          </button>
        </div>
      </main>
    </div>
  );
};

export default BookingConfirmed;

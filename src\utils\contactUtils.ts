
import { Contact } from '@/hooks/useContactPicker';

export const formatContactForDisplay = (contact: Contact): string => {
  if (contact.name) return contact.name;
  if (contact.email) return contact.email;
  if (contact.tel) return contact.tel;
  return 'Contatto sconosciuto';
};

export const getContactInitials = (contact: Contact): string => {
  if (contact.name) {
    const words = contact.name.split(' ');
    return words.map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);
  }
  if (contact.email) {
    return contact.email.charAt(0).toUpperCase();
  }
  if (contact.tel) {
    return 'T';
  }
  return 'C';
};

export const validateContact = (contact: Contact): boolean => {
  return !!(contact.name || contact.email || contact.tel);
};

export const sanitizeContact = (contact: Contact): Contact => {
  return {
    name: contact.name?.trim() || undefined,
    email: contact.email?.trim() || undefined,
    tel: contact.tel?.trim() || undefined
  };
};

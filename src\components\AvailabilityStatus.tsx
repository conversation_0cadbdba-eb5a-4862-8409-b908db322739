import React from 'react';

interface AvailabilityStatusProps {
  status: 'available' | 'limited' | 'sold-out' | 'loading';
  compact?: boolean;
}

/**
 * Component for displaying availability status badges
 * 
 * @param status - The availability status ('available', 'limited', 'sold-out', or 'loading')
 * @param compact - Whether to display in compact mode (smaller size)
 */
export function AvailabilityStatus({ status, compact = false }: AvailabilityStatusProps) {
  if (status === 'loading') {
    return (
      <div className={`inline-flex items-center ${compact ? 'px-1.5 py-0.5 text-xs' : 'px-2 py-1 text-sm'} bg-gray-100 text-gray-600 rounded-md`}>
        <div className="w-2 h-2 mr-1 bg-gray-400 rounded-full animate-pulse"></div>
        Checking...
      </div>
    );
  }
  
  switch (status) {
    case 'available':
      return (
        <div className={`inline-flex items-center ${compact ? 'px-1.5 py-0.5 text-xs' : 'px-2 py-1 text-sm'} bg-green-100 text-green-800 rounded-md`}>
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          Available
        </div>
      );
    case 'limited':
      return (
        <div className={`inline-flex items-center ${compact ? 'px-1.5 py-0.5 text-xs' : 'px-2 py-1 text-sm'} bg-yellow-100 text-yellow-800 rounded-md`}>
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          Limited
        </div>
      );
    case 'sold-out':
      return (
        <div className={`inline-flex items-center ${compact ? 'px-1.5 py-0.5 text-xs' : 'px-2 py-1 text-sm'} bg-red-100 text-red-800 rounded-md`}>
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          Sold Out
        </div>
      );
    default:
      return null;
  }
} 
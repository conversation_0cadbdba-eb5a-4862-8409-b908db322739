
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/auth/useAuth';
import { useNavigate } from 'react-router-dom';

export function useOnboarding() {
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState<boolean | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Controlla se l'utente ha già completato l'onboarding
    const checkOnboardingStatus = async () => {
      if (user) {
        const { data, error } = await supabase
          .from('user_details')
          .select('onboarding_completed')
          .eq('id', user.id)
          .single();

        if (!error && data) {
          setHasCompletedOnboarding(data.onboarding_completed);
        } else {
          // Se non c'è un record o c'è un errore, presumiamo che non sia stato completato
          setHasCompletedOnboarding(false);
        }
      } else {
        // Per gli utenti non autenticati, controlliamo localStorage
        const status = localStorage.getItem('onboardingCompleted');
        setHasCompletedOnboarding(status === 'true');
      }
    };

    checkOnboardingStatus();
  }, [user]);

  const nextStep = () => {
    setCurrentStep((prev) => prev + 1);
  };

  const previousStep = () => {
    setCurrentStep((prev) => Math.max(0, prev - 1));
  };

  const skipOnboarding = async () => {
    await completeOnboarding();
  };

  const completeOnboarding = async () => {
    // Aggiorna lo stato dell'onboarding
    if (user) {
      // Per utenti autenticati, aggiorna il profilo nel database
      await supabase
        .from('user_details')
        .update({ onboarding_completed: true })
        .eq('id', user.id);
    } else {
      // Per utenti non autenticati, usa localStorage
      localStorage.setItem('onboardingCompleted', 'true');
    }
    
    setHasCompletedOnboarding(true);
    navigate('/dashboard');
  };

  return {
    hasCompletedOnboarding,
    currentStep,
    nextStep,
    previousStep,
    skipOnboarding,
    completeOnboarding
  };
}

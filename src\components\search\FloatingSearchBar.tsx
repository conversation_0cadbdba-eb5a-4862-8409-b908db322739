import React, { useState } from "react";
import { Calendar, Clock, MapPin, Search } from "lucide-react";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { it } from "date-fns/locale";

interface FloatingSearchBarProps {
  onLocationChange: (location: { lat: number; lng: number } | null, cityName?: string) => void;
  onDateChange: (date: string) => void;
  onTimeChange: (time: string) => void;
  selectedDate?: string;
  selectedTime?: string;
  selectedLocation?: string;
  onGuestsChange?: (guests: number) => void;
  selectedGuests?: number;
}

export const FloatingSearchBar: React.FC<FloatingSearchBarProps> = ({
  onLocationChange,
  onDateChange,
  onTimeChange,
  selectedDate,
  selectedTime,
  selectedLocation,
}) => {
  const { userLocation } = useLocationManagement();
  const [cityName, setCityName] = useState(selectedLocation || "");
  const [date, setDate] = useState<Date | undefined>(
    selectedDate ? new Date(selectedDate) : undefined
  );
  const [time, setTime] = useState(selectedTime || "");
  const [isDateOpen, setIsDateOpen] = useState(false);
  const [isLocationFocused, setIsLocationFocused] = useState(false);

  const handleLocationSubmit = () => {
    if (cityName.trim()) {
      onLocationChange(null, cityName.trim());
    } else if (userLocation) {
      onLocationChange({ lat: userLocation.lat, lng: userLocation.lng });
    }
  };

  const handleDateSelect = (newDate: Date | undefined) => {
    setDate(newDate);
    if (newDate) {
      const dateString = format(newDate, "yyyy-MM-dd");
      onDateChange(dateString);
    }
    setIsDateOpen(false);
  };

  const handleTimeChange = (newTime: string) => {
    setTime(newTime);
    onTimeChange(newTime);
  };

  return (
    <div className="fixed top-4 left-4 right-4 z-20 bg-white rounded-full shadow-lg border border-gray-200">
      <div className="flex items-center">
        {/* Location Section */}
        <div className="flex items-center gap-2 px-4 py-3 flex-1 min-w-0">
          <MapPin className="h-5 w-5 text-gray-400 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            {!isLocationFocused && !cityName ? (
              <button
                onClick={() => setIsLocationFocused(true)}
                className="text-left w-full"
              >
                <div className="text-sm font-medium text-gray-900">
                  Seleziona luogo
                </div>
              </button>
            ) : (
              <Input
                placeholder="Seleziona luogo"
                value={cityName}
                onChange={(e) => setCityName(e.target.value)}
                onFocus={() => setIsLocationFocused(true)}
                onBlur={() => setIsLocationFocused(false)}
                className="border-0 bg-transparent px-0 focus-visible:ring-0 text-sm font-medium text-gray-900 h-auto p-0 placeholder:text-gray-500"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleLocationSubmit();
                  }
                }}
              />
            )}
          </div>
        </div>

        {/* Date Section */}
        <div className="flex items-center gap-2 px-4 py-3 border-l border-gray-200">
          <Popover open={isDateOpen} onOpenChange={setIsDateOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                className="flex items-center gap-2 p-0 h-auto hover:bg-transparent"
              >
                <Calendar className="h-5 w-5 text-gray-400" />
                <div className="text-sm font-medium text-gray-900">
                  {date ? format(date, "dd MMM", { locale: it }) : "Data"}
                </div>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="center">
              <CalendarComponent
                mode="single"
                selected={date}
                onSelect={handleDateSelect}
                initialFocus
                className="pointer-events-auto"
                locale={it}
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Time Section */}
        <div className="flex items-center gap-2 px-4 py-3 border-l border-gray-200">
          <Clock className="h-5 w-5 text-gray-400" />
          <div className="text-sm font-medium text-gray-900">
            {time ? time : "Ora"}
          </div>
          <Input
            type="time"
            value={time}
            onChange={(e) => handleTimeChange(e.target.value)}
            className="absolute opacity-0 w-full h-full cursor-pointer"
          />
        </div>

        {/* Search Button */}
        <div className="px-2">
          <Button
            onClick={handleLocationSubmit}
            size="sm"
            className="rounded-full bg-primary hover:bg-primary/90 text-primary-foreground w-10 h-10 p-0"
          >
            <Search className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};
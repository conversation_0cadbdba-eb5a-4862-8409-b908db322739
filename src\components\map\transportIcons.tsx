
import React from 'react';
import { Car, PersonStanding, Bus, Bike } from "lucide-react";
import { TransportMode } from './types';

export const getTransportIcon = (mode: TransportMode) => {
  switch (mode) {
    case "DRIVING":
      return <Car className="h-4 w-4" />;
    case "WALKING":
      return <PersonStanding className="h-4 w-4" />;
    case "TRANSIT":
      return <Bus className="h-4 w-4" />;
    case "BICYCLING":
      return <Bike className="h-4 w-4" />;
    default:
      return null;
  }
};

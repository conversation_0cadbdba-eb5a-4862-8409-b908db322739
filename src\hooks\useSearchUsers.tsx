
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface SearchUser {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  avatar_url?: string;
}

export const useSearchUsers = (query: string) => {
  return useQuery({
    queryKey: ['search-users', query],
    queryFn: async (): Promise<SearchUser[]> => {
      if (!query || query.length < 2) {
        return [];
      }

      const { data, error } = await supabase
        .from('users_with_details')
        .select('id, first_name, last_name, email, avatar_url')
        .or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,email.ilike.%${query}%`)
        .limit(20);

      if (error) {
        console.error('Error searching users:', error);
        throw error;
      }

      return data || [];
    },
    enabled: query.length >= 2
  });
};

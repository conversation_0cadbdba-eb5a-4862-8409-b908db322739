@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {

  body {
    @apply bg-background text-foreground;
    font-family: "Inter", sans-serif;
  }
}

@layer utilities {
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  /* Custom bounce animation for carousel hint */
  @keyframes bounce-hint {
    0%, 100% {
      transform: translateX(0px);
    }
    25% {
      transform: translateX(10px);
    }
    50% {
      transform: translateX(0px);
    }
    75% {
      transform: translateX(8px);
    }
  }
  
  .animate-bounce-hint {
    animation: bounce-hint 0.8s ease-in-out;
  }
  
  /* Swipe hint icon animation */
  @keyframes fade-in-out {
    0% {
      opacity: 0;
      transform: scale(0.8);
    }
    20% {
      opacity: 1;
      transform: scale(1);
    }
    80% {
      opacity: 1;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(0.8);
    }
  }
  
  .animate-fade-in-out {
    animation: fade-in-out 0.8s ease-in-out;
  }
  
  /* Gradient slide animation for voice assistant button */
  @keyframes gradientSlide {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
}

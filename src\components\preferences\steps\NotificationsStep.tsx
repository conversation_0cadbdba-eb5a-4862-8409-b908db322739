import { motion } from "framer-motion";

interface NotificationPreference {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
}

interface NotificationsStepProps {
  notificationPreferences: NotificationPreference[];
  onToggleNotification: (id: string) => void;
}

export const NotificationsStep = ({ notificationPreferences, onToggleNotification }: NotificationsStepProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -50 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">
          Preferenze di notifica
        </h1>
        <p className="text-gray-600">
          Scegli come e quando vuoi essere notificato delle migliori offerte
        </p>
      </div>

      <div className="space-y-4">
        {notificationPreferences.map((pref, index) => (
          <motion.div
            key={pref.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="p-5 rounded-xl border border-gray-200 bg-white"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 pr-4">
                <h3 className="font-semibold text-gray-800 mb-1">{pref.name}</h3>
                <p className="text-sm text-gray-500">{pref.description}</p>
              </div>
              <motion.label 
                whileTap={{ scale: 0.95 }}
                className="relative inline-flex items-center cursor-pointer"
              >
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={pref.enabled}
                  onChange={() => onToggleNotification(pref.id)}
                />
                <motion.div
                  animate={{
                    backgroundColor: pref.enabled ? "hsl(var(--brand-primary))" : "hsl(220, 13%, 91%)"
                  }}
                  transition={{ duration: 0.2 }}
                  className="w-11 h-6 rounded-full peer peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-primary/20 relative"
                >
                  <motion.div
                    animate={{
                      x: pref.enabled ? 20 : 2,
                      backgroundColor: "white"
                    }}
                    transition={{ duration: 0.2 }}
                    className="absolute top-[2px] left-[2px] bg-white border border-gray-300 rounded-full h-5 w-5"
                  />
                </motion.div>
              </motion.label>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};
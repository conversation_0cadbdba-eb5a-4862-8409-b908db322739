# CatchUp - System Patterns

## Architecture Overview

### High-Level System Design
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Client  │    │   Supabase      │    │  External APIs  │
│   (PWA)         │◄──►│   Backend       │◄──►│  (Google Maps)  │
│                 │    │                 │    │                 │
│ • Components    │    │ • PostgreSQL    │    │ • Maps API      │
│ • State Mgmt    │    │ • Auth          │    │ • Geocoding     │
│ • UI/UX         │    │ • Real-time     │    │ • Places API    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Component Architecture Patterns

#### Container/Presentation Pattern
- **Container Components**: Handle data fetching, state management, and business logic
- **Presentation Components**: Focus purely on UI rendering and user interactions
- **Hooks**: Extract reusable logic for data operations and state management

```typescript
// Container Example
const DealListContainer = () => {
  const { data: deals, isLoading } = useDeals();
  const [searchQuery, setSearchQuery] = useState("");
  
  return (
    <DealList 
      deals={deals}
      isLoading={isLoading}
      onSearch={setSearchQuery}
    />
  );
};

// Presentation Example
const DealList = ({ deals, isLoading, onSearch }) => {
  return (
    <div>
      <SearchBar onChange={onSearch} />
      {isLoading ? <LoadingSkeleton /> : <DealGrid deals={deals} />}
    </div>
  );
};
```

#### Compound Component Pattern
Used for complex UI components like the business marker system:

```typescript
// BusinessMarker compound component
<BusinessMarker business={business}>
  <BusinessMarker.Header />
  <BusinessMarker.DealList />
  <BusinessMarker.Actions />
</BusinessMarker>
```

## Data Management Patterns

### Server State Management
- **React Query**: All server state (deals, businesses, bookings)
- **Optimistic Updates**: Immediate UI updates with rollback on error
- **Background Refetching**: Keep data fresh without user intervention
- **Cache Invalidation**: Strategic cache updates for data consistency

```typescript
// Query pattern example
const useDeals = (filters?: DealFilters) => {
  return useQuery({
    queryKey: ['deals', filters],
    queryFn: () => dealService.getDeals(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};
```

### Client State Management
- **Zustand**: Global UI state (filters, preferences, UI mode)
- **Local State**: Component-specific state (form inputs, UI interactions)
- **URL State**: Search parameters and navigation state

```typescript
// Zustand store pattern
interface AppStore {
  mapView: boolean;
  searchFilters: SearchFilters;
  setMapView: (view: boolean) => void;
  updateFilters: (filters: Partial<SearchFilters>) => void;
}

const useAppStore = create<AppStore>((set) => ({
  mapView: false,
  searchFilters: {},
  setMapView: (view) => set({ mapView: view }),
  updateFilters: (filters) => set((state) => ({
    searchFilters: { ...state.searchFilters, ...filters }
  })),
}));
```

## Service Layer Patterns

### API Service Architecture
- **Service Classes**: Encapsulate all external API interactions
- **Error Handling**: Consistent error processing and user feedback
- **Type Safety**: Full TypeScript integration with API responses
- **Request/Response Transformation**: Clean data interfaces for components

```typescript
// Service class pattern
class DealService {
  async getDeals(filters?: DealFilters): Promise<Deal[]> {
    try {
      const { data, error } = await supabase
        .from('deals')
        .select(DEAL_SELECT_QUERY)
        .match(this.buildFilterQuery(filters));
      
      if (error) throw new APIError(error.message);
      return data.map(this.transformDeal);
    } catch (error) {
      throw new ServiceError('Failed to fetch deals', error);
    }
  }
  
  private transformDeal(rawDeal: RawDeal): Deal {
    // Transform database response to application model
  }
}
```

### Availability Service Pattern
- **Centralized Logic**: All availability calculations in dedicated service
- **Real-time Updates**: Supabase subscriptions for live availability
- **Caching Strategy**: Aggressive caching with smart invalidation

```typescript
class AvailabilityService {
  async getAvailabilityForDate(dealId: string, date: string): Promise<Availability> {
    // Check cache first
    const cached = this.cache.get(`${dealId}:${date}`);
    if (cached) return cached;
    
    // Calculate from bookings
    const availability = await this.calculateAvailability(dealId, date);
    
    // Cache result
    this.cache.set(`${dealId}:${date}`, availability, { ttl: 300 }); // 5 minutes
    return availability;
  }
}
```

## UI Component Patterns

### Composition Pattern
- **Flexible Components**: Built for reusability across different contexts
- **Prop Injection**: Dependencies injected rather than hardcoded
- **Render Props**: For complex conditional rendering logic

```typescript
// Flexible component example
interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  onFiltersChange?: (filters: SearchFilters) => void;
  showFilters?: boolean;
}

const SearchBar = ({ 
  value, 
  onChange, 
  placeholder = "Search deals...",
  onFiltersChange,
  showFilters = false 
}: SearchBarProps) => {
  // Component implementation
};
```

### Loading States Pattern
- **Skeleton Components**: Better perceived performance than spinners
- **Progressive Loading**: Load content in stages for complex views
- **Error Boundaries**: Graceful error handling with recovery options

```typescript
// Loading state pattern
const DealCard = ({ dealId }: { dealId: string }) => {
  const { data: deal, isLoading, error } = useDeal(dealId);
  
  if (isLoading) return <DealCardSkeleton />;
  if (error) return <DealCardError onRetry={() => queryClient.invalidateQueries(['deal', dealId])} />;
  if (!deal) return <DealCardNotFound />;
  
  return <DealCardContent deal={deal} />;
};
```

## Real-time Patterns

### Supabase Subscription Management
- **Connection Pooling**: Efficient real-time connection management
- **Selective Subscriptions**: Only subscribe to data currently in view
- **Cleanup Patterns**: Proper subscription cleanup on unmount

```typescript
// Real-time hook pattern
const useRealTimeAvailability = (dealId: string) => {
  const queryClient = useQueryClient();
  
  useEffect(() => {
    const subscription = supabase
      .channel(`deal-${dealId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'time_slot_bookings',
        filter: `deal_id=eq.${dealId}`
      }, () => {
        // Invalidate availability cache
        queryClient.invalidateQueries(['availability', dealId]);
      })
      .subscribe();
      
    return () => subscription.unsubscribe();
  }, [dealId, queryClient]);
};
```

## Map Integration Patterns

### Google Maps Component Architecture
- **Lazy Loading**: Maps loaded only when needed
- **Marker Clustering**: Performance optimization for dense areas
- **Custom Markers**: React components as map markers
- **Event Handling**: Clean separation of map events and React state

```typescript
// Map component pattern
const DealMap = ({ deals, onMarkerClick }: DealMapProps) => {
  const { isLoaded } = useLoadScript({
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY!,
    libraries: ['places', 'geometry'],
  });
  
  if (!isLoaded) return <MapSkeleton />;
  
  return (
    <GoogleMap
      mapContainerStyle={mapContainerStyle}
      center={center}
      zoom={zoom}
    >
      <MarkerClusterer>
        {(clusterer) => deals.map(deal => (
          <CustomBusinessMarker
            key={deal.id}
            deal={deal}
            clusterer={clusterer}
            onClick={onMarkerClick}
          />
        ))}
      </MarkerClusterer>
    </GoogleMap>
  );
};
```

## Form Handling Patterns

### React Hook Form Integration
- **Schema Validation**: Zod schemas for type-safe validation
- **Error Handling**: Consistent error display across forms
- **Accessibility**: ARIA labels and keyboard navigation
- **Performance**: Minimal re-renders with uncontrolled inputs

```typescript
// Form pattern example
const BookingForm = ({ dealId, onSubmit }: BookingFormProps) => {
  const form = useForm<BookingFormData>({
    resolver: zodResolver(bookingSchema),
    defaultValues: {
      date: '',
      quantity: 1,
      specialRequests: '',
    },
  });
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="date"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Date</FormLabel>
              <FormControl>
                <DatePicker {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* Additional fields */}
      </form>
    </Form>
  );
};
```

## Error Handling Patterns

### Global Error Boundary
- **Error Classification**: Different handling for different error types
- **Recovery Actions**: User-actionable recovery options
- **Error Reporting**: Structured error logging for debugging

```typescript
// Error boundary pattern
class GlobalErrorBoundary extends Component<Props, State> {
  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error: this.classifyError(error),
    };
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to monitoring service
    this.logError(error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} onRetry={this.retry} />;
    }
    
    return this.props.children;
  }
}
```

## Performance Patterns

### Code Splitting Strategy
- **Route-based Splitting**: Lazy load page components
- **Feature-based Splitting**: Dynamic imports for heavy features
- **Component Splitting**: Lazy load complex components

```typescript
// Code splitting pattern
const AdminDataGenerator = lazy(() => import('../components/AdminDataGenerator'));
const MapView = lazy(() => import('../components/MapView'));

const App = () => (
  <Router>
    <Routes>
      <Route path="/admin" element={
        <Suspense fallback={<PageSkeleton />}>
          <AdminDataGenerator />
        </Suspense>
      } />
      <Route path="/map" element={
        <Suspense fallback={<MapSkeleton />}>
          <MapView />
        </Suspense>
      } />
    </Routes>
  </Router>
);
``` 
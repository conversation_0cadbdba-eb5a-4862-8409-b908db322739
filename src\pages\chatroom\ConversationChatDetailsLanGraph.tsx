import { useState, useRef, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Send,
  Mic,
  Square,
  Play,
  Pause,
  User,
  Building2,
} from "lucide-react";
import { useMessages } from "@/hooks/useMessages";
import { useConversations } from "@/hooks/messages/useConversations";
import { useAuth } from "@/hooks/auth/useAuth";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import ReactMarkdown from "react-markdown";
import { BOOKING_CHAT_AGENT_WEBHOOK_URL } from "@/data/n8nWebHookUrl";
import VoiceRecordingFeedback from "@/components/chat/VoiceRecordingFeedback";
import UserMessage from "@/components/chat/UserMessage";

import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";
import { v4 as uuidv4 } from "uuid";

import { useStream } from "@langchain/langgraph-sdk/react";
import { MessageWithRole } from "@/types/conversations";

import type { Message } from "@langchain/langgraph-sdk";

import {
  Annotation,
  END,
  MessagesAnnotation,
  type StateType,
  type UpdateType,
} from "@langchain/langgraph/web";
import AssistantMessage from "@/components/chat/AssistantMessage";
import BusinessMessage from "@/components/chat/AssistantMessage";

const ConversationChatDetails = () => {
  const { conversationId } = useParams();
  const navigate = useNavigate();
  const { user, userDetails } = useAuth();
  const { messages, isLoading, conversation } = useMessages(
    conversationId || ""
  );
  const { saveMessage } = useConversations();
  const [newMessage, setNewMessage] = useState("");
  const [isWaiting, setIsWaiting] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [threadId] = useState(() => uuidv4());
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const { userLocation } = useLocationManagement();

  const AgentStateAnnotation = Annotation.Root({
    ...MessagesAnnotation.spec,
    // The agent node that last performed work
    // next: Annotation<string>({
    //   reducer: (x, y) => y ?? x ?? END,
    //   default: () => END,
    // }),

    conversationId: Annotation<string>,
    userId: Annotation<string>,
    businessId: Annotation<string>,

    language: Annotation<string>,
    isVoiceMessage: Annotation<boolean>,
    userName: Annotation<string>,
    latitude: Annotation<number>,
    longitude: Annotation<number>,
    communicationMethod: Annotation<string>,

    //bookingContext: Annotation<Record<string, any>>,
  });
  const thread = useStream<
    StateType<typeof AgentStateAnnotation.spec>,
    { UpdateType: UpdateType<typeof AgentStateAnnotation.spec> }
  >({
    apiUrl: "http://localhost:2024",
    assistantId: "catchup",
  });
  useEffect(() => {
    scrollToBottom();
  }, [messages, thread?.messages]);

  if (!conversationId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-700 mb-2">
            Conversazione non trovata
          </h2>
          <p className="text-gray-500">
            L'ID della conversazione non è valido.
          </p>
          <button
            onClick={() => navigate("/conversations")}
            className="mt-4 px-4 py-2 bg-brand-primary text-white rounded-lg hover:bg-brand-primary/90"
          >
            Torna indietro
          </button>
        </div>
      </div>
    );
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunksRef.current.push(e.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(chunksRef.current, { type: "audio/webm" });
        const reader = new FileReader();
        reader.readAsDataURL(audioBlob);
        reader.onloadend = async () => {
          const base64Audio = reader.result as string;
          const base64Only = base64Audio.split(",")[1];
          await handleSend(base64Only, true);
        };

        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (e) {
      console.error(e);
      alert("Per favore, concedi l'accesso al microfono");
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const handleRecord = async () => {
    if (isRecording) {
      stopRecording();
    } else {
      await startRecording();
    }
  };

  const handleSend = async (content: string, isVoice = false) => {
    if ((!content.trim() && !isVoice) || !conversationId || !conversation)
      return;

    const messageText = content.trim();
    setNewMessage("");

    // User Message
    // The exclamation mark (!) is a TypeScript non-null assertion operator
    // It tells TypeScript that even though conversationId might be null or undefined according to its type,
    // we as developers are certain that it will have a value at runtime when this code executes
    // This is used here because we've already checked that conversationId exists in the condition above

    //   await sendMessage(conversationId!, user.id, messageText);

    // Check if the message is from the business owner
    const isBusinessOwner = user.id === conversation.owner_id;

    // if (!lanGraph && isBusinessOwner) {
    //   return;
    // }

    thread.submit({
      messages: [messageText],

      userId: user.id,
      businessId: conversation.business_id,
      conversationId: conversationId,
      language: "it-IT",
      isVoiceMessage: isVoice,
      userName: userDetails?.first_name,
      latitude: userLocation?.lat,
      longitude: userLocation?.lng,

      communicationMethod: isVoice ? "voice" : "text",
    });

    // if (!lanGraph) {
    //   setIsWaiting(true);
    //   try {
    //     const response = await fetch(BOOKING_CHAT_AGENT_WEBHOOK_URL, {
    //       method: "POST",
    //       headers: {
    //         "Content-Type": "application/json",
    //       },
    //       body: JSON.stringify({
    //         userinput: messageText,
    //         conversationId: conversationId!,
    //         userId: user?.id,
    //         businessId: conversation.business_id,
    //         language: "it-IT",
    //         isVoiceMessage: isVoice,
    //         userName: userDetails?.first_name,
    //         latitude: userLocation?.lat,
    //         longitude: userLocation?.lng,
    //       }),
    //     });

    //     if (!response.ok) {
    //       await sendMessage(
    //         conversationId!,
    //         conversation.owner_id,
    //         "Mi dispiace, c'è stato un errore nella generazione della risposta. (response.ok)"
    //       );
    //       throw new Error("Errore nella risposta del webhook");
    //     }

    //     const data = await response.json();

    //     // Agent response
    //     await sendMessage(
    //       conversationId!,
    //       conversation.owner_id,
    //       data.output ||
    //         "Mi dispiace, c'è stato un errore nella generazione della risposta."
    //     );
    //   } catch (error) {
    //     console.error("Error calling webhook:", error);
    //     await sendMessage(
    //       conversationId!,
    //       conversation.owner_id,
    //       "Mi dispiace, c'è stato un errore nella generazione della risposta."
    //     );
    //   } finally {
    //     setIsWaiting(false);
    //   }
    // }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend(newMessage);
    }
  };

  /**
   * Checks if a string is a valid base64 audio content
   * @param content - The string to check
   * @returns boolean - True if the content is likely a base64 encoded audio, false otherwise
   */
  const isBase64Audio = (content: string) => {
    try {
      // Check if the content is a regular text message
      if (
        content.trim().length === 0 ||
        /^[\p{L}\p{N}\p{P}\p{Z}]+$/u.test(content)
      ) {
        return false;
      }

      // Check if it's a valid base64 string (should only contain valid base64 characters)
      if (!/^[A-Za-z0-9+/=]+$/.test(content)) {
        return false;
      }

      // Additional check for minimum length of reasonable audio data
      if (content.length < 100) {
        return false;
      }

      // Try to decode it
      const decodedString = atob(content);
      return decodedString.length > 0;
    } catch {
      return false;
    }
  };

  const AudioMessage = ({ content }: { content: string }) => {
    const [isPlaying, setIsPlaying] = useState(false);
    const audioRef = useRef<HTMLAudioElement>(null);

    const togglePlay = () => {
      if (audioRef.current) {
        if (isPlaying) {
          audioRef.current.pause();
        } else {
          audioRef.current.play();
        }
      }
    };

    useEffect(() => {
      const audio = audioRef.current;
      if (audio) {
        audio.onplay = () => setIsPlaying(true);
        audio.onpause = () => setIsPlaying(false);
        audio.onended = () => setIsPlaying(false);
      }
    }, []);

    return (
      <div className="flex items-center gap-2">
        <button
          onClick={togglePlay}
          className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
        >
          {isPlaying ? (
            <Pause className="h-4 w-4" />
          ) : (
            <Play className="h-4 w-4" />
          )}
        </button>
        <audio ref={audioRef} src={`data:audio/webm;base64,${content}`} />
        <span className="text-sm">Messaggio vocale</span>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-pulse">Caricamento...</div>
      </div>
    );
  }

  // Ottiene il nome dell'attività dalla conversazione
  const businessName = conversation
    ? conversation.business_name || "Attività"
    : "Conversazione";

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <header className="fixed top-0 left-0 right-0 bg-white z-50 px-4 py-3 flex items-center justify-between border-b">
        <div className="flex items-center">
          <button onClick={() => navigate(-1)} className="p-2">
            <ArrowLeft className="h-5 w-5 text-gray-700" />
          </button>
          <h1 className="ml-2 text-lg font-semibold">{businessName}</h1>
        </div>
      </header>

      <main className="flex-1 pt-16 pb-20 px-4 overflow-y-auto">
        <div className="space-y-4">
          {LangGraphMessagesRender()}

          {(isWaiting || thread?.isLoading) && AgentThinkingRender()}

          <div ref={messagesEndRef} />
        </div>
      </main>

      <VoiceRecordingFeedback isRecording={isRecording} />

      <footer className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
        <div className="flex items-center gap-2">
          <textarea
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Scrivi un messaggio..."
            className="flex-1 resize-none border rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-brand-primary"
            rows={1}
          />
          <Button
            onClick={handleRecord}
            variant="outline"
            size="icon"
            className={`rounded-full ${
              isRecording ? "bg-red-500 text-white hover:bg-red-600" : ""
            }`}
          >
            {isRecording ? (
              <Square className="h-5 w-5" />
            ) : (
              <Mic className="h-5 w-5" />
            )}
          </Button>
          <Button
            onClick={() => handleSend(newMessage)}
            disabled={!newMessage.trim() || isWaiting}
            variant="default"
            size="icon"
            className="rounded-full"
          >
            <Send className="h-5 w-5" />
          </Button>
        </div>
      </footer>
    </div>
  );

  function AgentThinkingRender() {
    return (
      <div className="flex justify-start">
        <div className="flex items-end gap-2">
          <Avatar className="h-8 w-8 bg-indigo-500 text-white">
            <AvatarFallback>
              <Building2 className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
          <div className="bg-white border rounded-lg p-3 shadow-sm">
            <div className="flex space-x-2">
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "0ms" }}
              ></div>
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "200ms" }}
              ></div>
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "400ms" }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  function LangGraphMessagesRender() {
    return (
      <div>
        {thread?.messages.map((message) => (
          <div key={message.id}>
            {message.type === "human" ? (
              <UserMessage
                key={message.id}
                message={{
                  id: message.id,
                  content:
                    typeof message.content === "string" ? message.content : "",
                  created_at: new Date().toISOString(),
                  user_id: user.id,
                  role: "customer",
                  conversation_id: conversationId || null,
                  read_at: null,
                  metadata: {},
                }}
                isOwnMessage={true}
                AudioMessage={AudioMessage}
                isBase64Audio={isBase64Audio}
              />
            ) : (
              <AssistantMessage
                key={message.id}
                message={{
                  id: message.id,
                  content:
                    typeof message.content === "string" ? message.content : "",
                  created_at: new Date().toISOString(),
                  user_id: user.id,
                  role: "customer",
                  conversation_id: conversationId || null,
                  read_at: null,
                  metadata:{},
                }}
                isOwnMessage={false}
                AudioMessage={AudioMessage}
                isBase64Audio={isBase64Audio}
              />
            )}
          </div>
        ))}
      </div>
    );
  }

  
};

export default ConversationChatDetails;

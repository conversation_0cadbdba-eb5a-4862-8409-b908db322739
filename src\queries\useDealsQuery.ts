import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

type Deal = Database["public"]["Tables"]["deals"]["Row"] & {
  businesses?: {
    name: string;
    address: string;
    city: string;
    zip_code: string;
    state: string;
    country: string;
    category_id: string;
    owner_id: string;
  };
};

type Business = {
  id: string;
  name: string;
  latitude: number | null;
  longitude: number | null;
  deal_count_published: number | null;
  deals?: Deal[];
  photos: string[];
  fake: boolean;
  category_id: string;
  categories?: {
    id?: string;
    icon: string;
    name: string;
  };
};

interface BusinessesWithDealsData {
  businesses: Business[];
  deals: Deal[];
}

export const useBusinessesWithDealsQuery = (options = {}) => {
  return useQuery({
    queryKey: ["businesses-with-deals"],
    queryFn: async (): Promise<BusinessesWithDealsData> => {
      const { data: businessesWithDealsData, error: businessesError } =
        await supabase
          .from("businesses_with_counts")
          .select(
            `
          id, 
          name, 
          latitude, 
          longitude,
          deal_count_published,
          photos,
          fake,
          category_id,
          categories!inner (
        
            name,  
            icon
          ),
          deals!inner (
            *,
            businesses!inner (
              name,
              address,
              city,
              zip_code,
              state,
              country,
              category_id,
              owner_id
            )
          )
        `
          )
          .not("latitude", "is", null)
          .not("longitude", "is", null);

      if (businessesError) {
        throw new Error(
          `Errore nel caricamento delle attività con offerte: ${businessesError.message}`
        );
      }

      const rawData = businessesWithDealsData || [];
      console.log("businessesWithDealsData", rawData);

      const transformedBusinesses = rawData as unknown as Business[];

      const filteredBusinesses = transformedBusinesses
        .map((business) => ({
          ...business,
          icon: business.categories?.icon || null,
          deals: business.deals?.filter((deal) => {
            if (!deal || !deal.title) {
              return false;
            }
            return true;
          }),
        }))
        .filter((business) => business.deals && business.deals.length > 0);

      const allDeals = filteredBusinesses.flatMap(
        (business) => business.deals || []
      );

      return {
        businesses: transformedBusinesses,
        deals: allDeals as Deal[],
      };
    },
    staleTime: 1000 * 60 * 5, // 5 minutes cache
    retry: 2,
    ...options,
  });
};

export const useDealsQuery = (options = {}) => {
  return useQuery({
    queryKey: ["deals"],
    queryFn: async () => {
      const { data, error } = await supabase.from("deals").select("*");

      if (error) {
        throw new Error(`Errore nel recupero delle offerte: ${error.message}`);
      }

      return data || [];
    },
    ...options,
  });
};

export const useDealsByBusinessIdQuery = (businessId: string, options = {}) => {
  return useQuery({
    queryKey: ["deals", "business", businessId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("deals")
        .select("*")
        .eq("business_id", businessId)
        .eq("status", "published");

      if (error) {
        throw new Error(`Errore nel recupero delle offerte: ${error.message}`);
      }

      return data || [];
    },
    ...options,
  });
};

export const useRecentlyViewedDealsQuery = (userId: string, options = {}) => {
  return useQuery({
    queryKey: ["recently-viewed-deals", userId],
    queryFn: async () => {
      const { data: recentlyViewedDealIds, error: recentlyViewedDealsError } =
        await supabase
          .from("user_details")
          .select("recently_viewed_deals")
          .eq("id", userId)
          .order("created_at", { ascending: false })
          .single();

      if (recentlyViewedDealsError) throw recentlyViewedDealsError;
      if (recentlyViewedDealIds) {
        const { data: dealsData, error: dealsError } = await supabase
          .from("deals")
          .select("*, businesses(*)")
          .in("id", recentlyViewedDealIds.recently_viewed_deals as string[])
          .eq("status", "published");
        if (dealsError) throw dealsError;
        console.log("recentlyViewedDeals", dealsData);
        return dealsData || [];
      }
      return [];
    },
    ...options,
  });
};

export const useFavoriteDealsQuery = (
  userId: string,

  options = {}
) => {
  return useQuery({
    queryKey: ["favorite-deals", userId],
    queryFn: async () => {

      console.log("userId", userId);
      const { data: favoriteDealIds, error: favoriteDealsError } =
        await supabase
          .from("user_details")
          .select("favorite_deals, recently_viewed_deals")
          .eq("id", userId)
          .order("created_at", { ascending: false })
          .single();
      if (favoriteDealsError) throw favoriteDealsError;
      if (favoriteDealIds) {
        const { data: dealsData, error: dealsError } = await supabase
          .from("deals")
          .select("*, businesses(*)")
          .in("id", favoriteDealIds.favorite_deals as string[])
          .eq("status", "published");
        console.log("favoriteDeals", dealsData);
        if (dealsError) throw dealsError;
        return dealsData || [];
      }
      return [];
    },
    ...options,
  });
};


// TODO. Need a criteria
export const useFeaturedDealsQuery = (options = {}) => {
  return useQuery({
    queryKey: ["featured-deals"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("deals")
        .select("*")
        .eq("status", "published")
        .order("random()")
        .limit(10);

      if (error) {
        throw new Error(
          `Errore nel recupero delle offerte in evidenza: ${error.message}`
        );
      }

      return data || [];
    },
    ...options,
  });
};

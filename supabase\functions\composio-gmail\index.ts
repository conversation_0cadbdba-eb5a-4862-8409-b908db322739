import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.3";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface ComposioAuthRequest {
  action: 'start_auth' | 'handle_callback' | 'get_emails' | 'send_email';
  code?: string;
  state?: string;
  email_data?: {
    to: string;
    subject: string;
    body: string;
  };
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('Function started - checking Supabase connection');
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    console.log('Supabase client created');

    // Get user from auth header
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      console.error('Missing authorization header');
      return new Response(JSON.stringify({ error: 'Missing authorization header' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      });
    }

    console.log('Auth header found, getting user');

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      console.error('Auth error:', authError?.message);
      return new Response(JSON.stringify({ error: 'Invalid authentication' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      });
    }

    console.log('User authenticated:', user.id);

    const { action, code, state, email_data }: ComposioAuthRequest = await req.json();
    
    console.log('Received action:', action);
    console.log('Request origin:', req.headers.get('origin'));
    
    const composioApiKey = Deno.env.get('COMPOSIO_API_KEY');

    if (!composioApiKey) {
      console.error('Missing COMPOSIO_API_KEY');
      return new Response(JSON.stringify({ error: 'Composio API key not configured' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      });
    }

    console.log('Composio API key found:', !!composioApiKey);

    switch (action) {
      case 'start_auth':
        console.log('Starting Gmail OAuth for user:', user.id);
        
        // Use the correct Composio connected accounts API
        const authConfigId = Deno.env.get('COMPOSIO_AUTH_CONFIG_ID');
        const redirectUrl = `${req.headers.get('origin')}/email?auth=callback`;
        
        console.log('Auth config ID:', authConfigId);
        console.log('Redirect URL:', redirectUrl);
        
        // Use Composio's connected accounts initiate endpoint
        const initiateResponse = await fetch('https://backend.composio.dev/api/v1/connected_accounts/initiate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': composioApiKey,
          },
          body: JSON.stringify({
            user_id: user.id,
            auth_config_id: authConfigId,
            redirect_url: redirectUrl,
            config: {
              auth_scheme: "OAUTH2"
            }
          }),
        });
        
        console.log('Initiate response status:', initiateResponse.status);
        
        if (!initiateResponse.ok) {
          const error = await initiateResponse.text();
          console.error('Composio initiate failed:', error);
          return new Response(JSON.stringify({ error: 'Failed to initiate OAuth' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          });
        }

        const initiateData = await initiateResponse.json();
        console.log('Initiate successful:', initiateData);

        return new Response(JSON.stringify({
          success: true,
          authUrl: initiateData.redirect_url,
          connectionId: initiateData.id,
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        });

      case 'handle_callback':
        if (!code || !state) {
          return new Response(JSON.stringify({ error: 'Missing code or state' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          });
        }

        console.log('Handling OAuth callback for user:', user.id);

        // Exchange code for tokens using v3 API
        const tokenResponse = await fetch('https://backend.composio.dev/api/v3/integrations/callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': composioApiKey,
          },
          body: JSON.stringify({
            code,
            state,
            userId: user.id,
          }),
        });

        if (!tokenResponse.ok) {
          const error = await tokenResponse.text();
          console.error('Token exchange failed:', error);
          return new Response(JSON.stringify({ error: 'Failed to exchange tokens' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          });
        }

        const tokenData = await tokenResponse.json();
        console.log('Tokens received successfully');

        // Save integration to database
        const { error: dbError } = await supabase
          .from('integration_authorizations')
          .upsert({
            user_id: user.id,
            provider: 'gmail',
            integration_id: tokenData.integrationId,
            access_token: tokenData.accessToken,
            refresh_token: tokenData.refreshToken,
            expires_at: tokenData.expiresAt ? new Date(tokenData.expiresAt).toISOString() : null,
            metadata: {
              email: tokenData.email,
              scopes: tokenData.scopes,
            },
            is_active: true,
          }, {
            onConflict: 'user_id,provider'
          });

        if (dbError) {
          console.error('Database save error:', dbError);
          return new Response(JSON.stringify({ error: 'Failed to save integration' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          });
        }

        return new Response(JSON.stringify({ 
          success: true,
          message: 'Gmail integration completed successfully'
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        });

      case 'get_emails':
        console.log('Fetching emails for user:', user.id);

        // Get user's integration
        const { data: integration, error: integrationError } = await supabase
          .from('integration_authorizations')
          .select('*')
          .eq('user_id', user.id)
          .eq('provider', 'gmail')
          .eq('is_active', true)
          .single();

        if (integrationError || !integration) {
          return new Response(JSON.stringify({ error: 'Gmail not connected' }), {
            status: 404,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          });
        }

        // Fetch emails using Composio v3 API
        const emailsResponse = await fetch(`https://backend.composio.dev/api/v3/integrations/${integration.integration_id}/gmail/messages`, {
          headers: {
            'x-api-key': composioApiKey,
            'Authorization': `Bearer ${integration.access_token}`,
          },
        });

        if (!emailsResponse.ok) {
          const error = await emailsResponse.text();
          console.error('Failed to fetch emails:', error);
          return new Response(JSON.stringify({ error: 'Failed to fetch emails' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          });
        }

        const emailsData = await emailsResponse.json();

        return new Response(JSON.stringify({
          success: true,
          emails: emailsData.messages || [],
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        });

      case 'send_email':
        if (!email_data) {
          return new Response(JSON.stringify({ error: 'Missing email data' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          });
        }

        console.log('Sending email for user:', user.id);

        // Get user's integration
        const { data: sendIntegration, error: sendIntegrationError } = await supabase
          .from('integration_authorizations')
          .select('*')
          .eq('user_id', user.id)
          .eq('provider', 'gmail')
          .eq('is_active', true)
          .single();

        if (sendIntegrationError || !sendIntegration) {
          return new Response(JSON.stringify({ error: 'Gmail not connected' }), {
            status: 404,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          });
        }

        // Send email using Composio v3 API
        const sendResponse = await fetch(`https://backend.composio.dev/api/v3/integrations/${sendIntegration.integration_id}/gmail/send`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': composioApiKey,
            'Authorization': `Bearer ${sendIntegration.access_token}`,
          },
          body: JSON.stringify({
            to: email_data.to,
            subject: email_data.subject,
            body: email_data.body,
          }),
        });

        if (!sendResponse.ok) {
          const error = await sendResponse.text();
          console.error('Failed to send email:', error);
          return new Response(JSON.stringify({ error: 'Failed to send email' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          });
        }

        const sendData = await sendResponse.json();

        return new Response(JSON.stringify({
          success: true,
          messageId: sendData.messageId,
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        });

      default:
        return new Response(JSON.stringify({ error: 'Invalid action' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        });
    }
  } catch (error) {
    console.error('Edge function error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', ...corsHeaders },
    });
  }
};

serve(handler);
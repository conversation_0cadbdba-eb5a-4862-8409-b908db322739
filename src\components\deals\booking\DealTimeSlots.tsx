
import React, { useState } from 'react';
import { Clock, ChevronDown, ChevronUp, Users } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { daysNames } from '@/data/daysNames';
import { useTimeSlots } from '@/hooks/useTimeSlots';
import type { JsonCompatible } from '@/types/deals';
import { cn } from '@/lib/utils';

interface DealTimeSlotsProps {
  timeSlots: JsonCompatible;
  isShort?: boolean;
  hide?: boolean;
}

const DealTimeSlots: React.FC<DealTimeSlotsProps> = ({ timeSlots, isShort = true, hide = false }) => {
  if (hide) return null;
  
  const [isOpen, setIsOpen] = useState(false);
  const { 
    daysWithSlots, 
    parsedTimeSlots,
    getTimeSlotsForDay,
    isLowAvailability
  } = useTimeSlots(timeSlots);

  if (!parsedTimeSlots?.schedule || parsedTimeSlots.schedule.length === 0) {
    return null;
  }

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <Collapsible 
      open={isOpen} 
      onOpenChange={setIsOpen}
      className="w-full border-t mt-2 pt-2"
      onClick={handleClick}
    >
      <CollapsibleTrigger className="flex items-center justify-between w-full py-2 text-sm font-medium text-indigo-600" onClick={handleClick}>
        <span className="flex items-center gap-1">
          <Clock className="h-4 w-4" />
          {isShort ? "Visualizza orari disponibili" : "Orari disponibili per giorno"}
        </span>
        {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
      </CollapsibleTrigger>

      <CollapsibleContent onClick={handleClick}>
        <Accordion type="single" collapsible className="w-full">
          {daysWithSlots.map((dayNumber) => {
            const dayName = daysNames[dayNumber as keyof typeof daysNames];
            const slots = getTimeSlotsForDay(dayNumber);
            
            if (slots.length === 0) return null;
            
            return (
              <AccordionItem key={dayNumber} value={`day-${dayNumber}`} className="border-b">
                <AccordionTrigger className="py-2 text-sm capitalize">
                  {dayName}
                </AccordionTrigger>
                <AccordionContent className="pb-2">
                  <div className="grid grid-cols-1 gap-2 pl-2">
                    {slots.map((slot, index) => {
                      const available = slot.available_seats || 0;
                      const isLow = available <= 3 && available > 0;
                      
                      return (
                        <div 
                          key={index} 
                          className={cn(
                            "flex justify-between items-center p-2 rounded-md text-xs",
                            isLow ? "bg-amber-50" : "bg-green-50"
                          )}
                        >
                          <span className="font-medium">
                            {slot.formattedTime}
                          </span>
                          <div className="flex items-center gap-1">
                            <Users className={cn(
                              "h-3 w-3",
                              isLow ? "text-amber-600" : "text-green-600"
                            )} />
                            <span className={cn(
                              "font-medium",
                              isLow ? "text-amber-600" : "text-green-600"
                            )}>
                              {available} {available === 1 ? "posto" : "posti"}
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </AccordionContent>
              </AccordionItem>
            );
          })}
        </Accordion>
      </CollapsibleContent>
    </Collapsible>
  );
};

export default DealTimeSlots;

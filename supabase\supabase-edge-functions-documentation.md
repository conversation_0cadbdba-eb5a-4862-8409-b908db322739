# Supabase Edge Functions Documentation

## Overview

This document provides comprehensive documentation for two Supabase edge functions used in the CatchUp application for location-based business and deal discovery.

## Functions Overview

| Function | Purpose | Primary Use Case |
|----------|---------|------------------|
| `get-nearest-businesses` | Find businesses near a location | Discover nearby businesses regardless of deals |
| `get-nearby-deals-by-location` | Find deals near a location for specific dates/times | Search for available deals with time-based filtering |

---

## 1. get-nearest-businesses

### Purpose
Finds the nearest businesses to a given location using PostGIS for accurate distance calculations.

### Key Features
- ✅ PostGIS-based distance calculations
- ✅ Built-in caching (10-minute TTL)
- ✅ Deal count filtering
- ✅ Distance formatting (meters/kilometers)
- ✅ CORS support

### API Specification

#### Request
```typescript
interface NearestBusinessesRequest {
  latitude: number;           // Required: User's latitude
  longitude: number;          // Required: User's longitude
  limit?: number;            // Optional: Max results (default: 5)
  transportMode?: string;    // Optional: Transport mode (default: "DRIVING")
  useDirections?: boolean;   // Optional: Include routing details (default: false)
  maxDistance_meters?: number; // Optional: Max distance in meters (default: 5000)
  require_deals?: boolean;   // Optional: Only businesses with deals (default: false)
}
```

#### Response
```typescript
interface NearestBusinessesResponse {
  businesses: Business[];
  nearestBusiness: Business | null;
}

interface Business {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  deal_count: number;
  address: string;
  distance: number;        // Distance in meters
  distanceText: string;    // Formatted distance text
  photos: string[];
}
```

#### Example Usage
```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/get-nearest-businesses' \
  -H 'Content-Type: application/json' \
  -d '{
    "latitude": 45.4642,
    "longitude": 9.1900,
    "limit": 10,
    "require_deals": true
  }'
```

---

## 2. get-nearby-deals-by-location

### Purpose
Finds available deals near a location for specific dates and times with advanced filtering capabilities.

### Key Features
- ✅ Date/time-based filtering
- ✅ Location-based search with radius
- ✅ Category filtering
- ✅ Cursor-based pagination
- ✅ Time slot availability checking
- ✅ CORS support

### API Specification

#### Request
```typescript
interface NearbyDealsRequest {
  date: string;              // Required: Date in YYYY-MM-DD format
  time?: string;             // Optional: Time in HH:MM format
  location?: LocationParams; // Optional: Location parameters
  cityName?: string;         // Optional: City name filter
  category_id?: string;      // Optional: Category filter
  cursor?: string;           // Optional: Pagination cursor
  limit?: number;            // Optional: Results per page (default: 20)
}

interface LocationParams {
  lat: number;               // Latitude
  lng: number;               // Longitude
  radius?: number;           // Search radius in meters (default: 5000)
}
```

#### Response
```typescript
interface NearbyDealsResponse {
  date: string;
  time?: string;
  deal_count: number;
  user_location?: LocationParams;
  deals: DealAvailability[];
  next_cursor: string | null;
  cached?: boolean;
}

interface DealAvailability {
  id: string;
  name: string;
  description: string;
  original_price: number;
  discounted_price: number;
  discount_percentage: number;
  images: string[] | null;
  time_slots: any;
  status: "published" | "draft" | "expired";
  created_at: string;
  updated_at: string;
  auto_confirm: boolean;
  category_id: string;
  start_date: string;
  end_date: string;
  business: Business;
}
```

#### Example Usage
```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/get-nearby-deals-by-location' \
  -H 'Content-Type: application/json' \
  -d '{
    "date": "2024-01-15",
    "time": "14:30",
    "location": {
      "lat": 45.4642,
      "lng": 9.1900,
      "radius": 3000
    },
    "category_id": "restaurant",
    "limit": 10
  }'
```

---

## Key Differences

| Aspect | get-nearest-businesses | get-nearby-deals-by-location |
|--------|------------------------|------------------------------|
| **Primary Focus** | Finding businesses | Finding available deals |
| **Data Returned** | Business information | Deal information + business data |
| **Filtering** | Location, distance, deal presence | Date, time, location, category |
| **Caching** | ✅ 10-minute cache | ❌ No caching |
| **Pagination** | ❌ Limit-based only | ✅ Cursor-based pagination |
| **Database Query** | PostGIS RPC function | Stored procedure with complex logic |
| **Time Complexity** | Simple distance calculation | Complex availability filtering |
| **Use Case** | "Show me nearby businesses" | "Show me available deals for Friday at 2 PM" |

---

## Architecture & Flow

### get-nearest-businesses Flow
1. **Validate** latitude/longitude parameters
2. **Check cache** for existing results
3. **Query database** using PostGIS RPC function
4. **Format response** with distance calculations
5. **Cache results** for future requests
6. **Return formatted** business list

### get-nearby-deals-by-location Flow
1. **Validate** required date parameter
2. **Parse** optional time and location parameters
3. **Call stored procedure** with all filters
4. **Process results** and validate deal status
5. **Format response** with deal and business information
6. **Handle pagination** with cursor logic
7. **Return deals** with availability information

---

## Performance Considerations

### get-nearest-businesses
- **Strengths**: Caching reduces database load, PostGIS is optimized for geo queries
- **Weaknesses**: Cache invalidation strategy is time-based only
- **Optimization**: Consider geographic cache keys for better hit rates

### get-nearby-deals-by-location
- **Strengths**: Cursor pagination handles large datasets efficiently
- **Weaknesses**: No caching, complex filtering may impact performance
- **Optimization**: Consider adding Redis cache for popular queries

---

## Error Handling

Both functions implement comprehensive error handling:

### Common Error Patterns
- **Validation Errors** (400): Missing required parameters
- **Database Errors** (500): Connection or query failures
- **Unexpected Errors** (500): Unhandled exceptions

### Error Response Format
```typescript
interface ErrorResponse {
  error: string;
  details?: string;
}
```

---

## Security & CORS

Both functions implement:
- ✅ CORS headers for cross-origin requests
- ✅ Environment variable-based configuration
- ✅ Supabase service role authentication
- ✅ Input validation and sanitization

---

## Future Enhancements

### get-nearest-businesses
- [ ] Implement Google Maps Directions API integration
- [ ] Add more sophisticated caching strategies
- [ ] Support for multiple transportation modes

### get-nearby-deals-by-location
- [ ] Add caching layer for improved performance
- [ ] Implement real-time availability updates
- [ ] Add more granular category filtering

---

## Monitoring & Logging

Both functions include comprehensive logging:
- Parameter validation results
- Database query performance
- Cache hit/miss rates (get-nearest-businesses)
- Error tracking and debugging information

---

## Deployment Notes

### Environment Variables Required
```bash
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### Database Dependencies
- **get-nearest-businesses**: Requires `get_nearest_businesses_postgis` RPC function
- **get-nearby-deals-by-location**: Requires `get_nearby_deals` stored procedure

---

*Documentation generated for CatchUp MVP - Version 1.0* 
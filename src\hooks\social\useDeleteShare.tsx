import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface DeleteShareParams {
  id: string;
  type: 'deal_share' | 'booking_share';
}

export const useDeleteShare = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, type }: DeleteShareParams) => {
      const actualId = id.replace('deal-', '').replace('booking-', '');
      
      if (type === 'deal_share') {
        const { error } = await supabase
          .from('group_deal_shares')
          .delete()
          .eq('id', actualId);
        
        if (error) throw error;
      } else if (type === 'booking_share') {
        const { error } = await supabase
          .from('group_booking_shares')
          .delete()
          .eq('id', actualId);
        
        if (error) throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['social-feed'] });
      toast.success('Condivisione rimossa');
    },
    onError: (error) => {
      console.error('Error deleting share:', error);
      toast.error('Errore nel rimuovere la condivisione');
    },
  });
};
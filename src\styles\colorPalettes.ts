import { ColorPalette } from '../contexts/ABTestContext';

// Define the default color palette (current brand colors)
export const defaultPalette = {
  // Brand colors
  brandPrimary: '#FF385C',
  brandSecondary: '#FF5A5F',
  brandLight: '#FFE1E2',

  // CSS Variables (HSL format)
  primary: '346 100% 61%', // #FF385C
  primaryForeground: '355.7 100% 97.3%',
  secondary: '346 100% 68%', // #FF5A5F
  secondaryForeground: '240 5.9% 10%',
  ring: '346 100% 61%', // Match primary

  // Chart colors
  chart1: '12 76% 61%',
  chart2: '173 58% 39%',
  chart3: '197 37% 24%',
  chart4: '43 74% 66%',
  chart5: '27 87% 67%',
};

// Define the alternative color palette (orange theme from the screenshot)
export const alternativePalette = {
  // Brand colors
  brandPrimary: '#FF8C38', // Orange primary
  brandSecondary: '#FF9F5A', // Orange secondary
  brandLight: '#FFE8D9', // Light orange

  // CSS Variables (HSL format)
  primary: '28 100% 61%', // #FF8C38
  primaryForeground: '0 0% 98%',
  secondary: '28 100% 68%', // #FF9F5A
  secondaryForeground: '240 5.9% 10%',
  ring: '28 100% 61%', // Match primary

  // Chart colors (adjusted for orange theme)
  chart1: '28 76% 61%',
  chart2: '173 58% 39%',
  chart3: '197 37% 24%',
  chart4: '43 74% 66%',
  chart5: '27 87% 67%',
};

// Function to get the palette based on the variant
export const getPalette = (variant: ColorPalette) => {
  return variant === 'default' ? defaultPalette : alternativePalette;
};

// Function to generate CSS variables for a palette
export const generateCSSVariables = (palette: typeof defaultPalette) => {
  // Convert hex to HSL for brand colors
  const brandPrimaryHSL = hexToHSL(palette.brandPrimary);
  const brandSecondaryHSL = hexToHSL(palette.brandSecondary);
  const brandLightHSL = hexToHSL(palette.brandLight);

  return `
    --primary: ${palette.primary};
    --primary-foreground: ${palette.primaryForeground};
    --secondary: ${palette.secondary};
    --secondary-foreground: ${palette.secondaryForeground};
    --ring: ${palette.ring};
    --chart-1: ${palette.chart1};
    --chart-2: ${palette.chart2};
    --chart-3: ${palette.chart3};
    --chart-4: ${palette.chart4};
    --chart-5: ${palette.chart5};

    --brand-primary: ${brandPrimaryHSL};
    --brand-secondary: ${brandSecondaryHSL};
    --brand-light: ${brandLightHSL};


  /* ADD MISSING SHADCN VARIABLES REQUIRED FOR SHADCN UI COMPONENTS*/
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --radius: 0.5rem;

  `;
};

// Helper function to convert hex color to HSL format for CSS variables
function hexToHSL(hex: string): string {
  // Remove the # if present
  hex = hex.replace('#', '');

  // Convert hex to RGB
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  // Find min and max RGB components
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);

  let h = 0, s = 0, l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }

    h = Math.round(h * 60);
  }

  s = Math.round(s * 100);
  l = Math.round(l * 100);

  return `${h} ${s}% ${l}%`;
};

// Function to generate Tailwind colors for a palette
export const generateTailwindColors = (palette: typeof defaultPalette) => {
  return {
    brand: {
      primary: palette.brandPrimary,
      secondary: palette.brandSecondary,
      light: palette.brandLight,
    },
  };
};

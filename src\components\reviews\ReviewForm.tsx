import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Star } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { useCreateReview, ReviewFormData } from "@/hooks/useBusinessReviews";

interface ReviewFormProps {
  businessId: string;
  bookingId: string;
  businessName: string;
  onSuccess?: () => void;
}

export const ReviewForm = ({ 
  businessId, 
  bookingId, 
  businessName, 
  onSuccess 
}: ReviewFormProps) => {
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  
  const { 
    register, 
    handleSubmit, 
    formState: { errors },
    reset 
  } = useForm<{ comment: string }>();

  const createReviewMutation = useCreateReview();

  const onSubmit = (data: { comment: string }) => {
    const reviewData: ReviewFormData = {
      rating,
      comment: data.comment,
      booking_id: bookingId,
      business_id: businessId,
    };

    createReviewMutation.mutate(reviewData, {
      onSuccess: () => {
        reset();
        setRating(0);
        onSuccess?.();
      },
    });
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-xl">
          Recensisci {businessName}
        </CardTitle>
        <p className="text-sm text-gray-600">
          Condividi la tua esperienza per aiutare altri utenti
        </p>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Rating Selection */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Valutazione *</Label>
            <div className="flex gap-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  className="p-1 transition-colors"
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  onClick={() => setRating(star)}
                >
                  <Star
                    className={`h-8 w-8 ${
                      star <= (hoveredRating || rating)
                        ? "fill-yellow-400 text-yellow-400"
                        : "text-gray-300"
                    }`}
                  />
                </button>
              ))}
            </div>
            {rating === 0 && (
              <p className="text-sm text-red-500">
                Seleziona una valutazione
              </p>
            )}
          </div>

          {/* Comment */}
          <div className="space-y-2">
            <Label htmlFor="comment" className="text-sm font-medium">
              Commento (opzionale)
            </Label>
            <Textarea
              id="comment"
              placeholder="Descrivi la tua esperienza..."
              className="min-h-[100px] resize-none"
              {...register("comment", {
                maxLength: {
                  value: 500,
                  message: "Il commento non può superare i 500 caratteri",
                },
              })}
            />
            {errors.comment && (
              <p className="text-sm text-red-500">
                {errors.comment.message}
              </p>
            )}
          </div>

          {/* Error Message */}
          {createReviewMutation.error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">
                {createReviewMutation.error.message}
              </p>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex gap-3">
            <Button
              type="submit"
              disabled={rating === 0 || createReviewMutation.isPending}
              className="flex-1"
            >
              {createReviewMutation.isPending ? "Invio..." : "Invia Recensione"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
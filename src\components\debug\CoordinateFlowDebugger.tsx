import React, { useState } from "react";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";
import { useUserDetails } from "@/hooks/auth/useUserDetails";
import { useAuth } from "@/hooks/auth/useAuth";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, EyeOff, RefreshCw } from "lucide-react";
import { refreshLocationSettings } from "@/contexts/LocationContext";

const CoordinateFlowDebugger = () => {
  const { user } = useAuth();
  const { userDetails, getDemoCoordinates } = useUserDetails(user?.id);
  const { 
    userLocation, 
    demoEnabled, 
    demoLocation,
    source 
  } = useLocationManagement();
  
  const [isVisible, setIsVisible] = useState(false);
  
  const demoCoords = getDemoCoordinates();

  if (!isVisible) {
    return (
      <div className="fixed top-20 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          className="text-xs"
          onClick={() => setIsVisible(true)}
        >
          <Eye className="h-3 w-3 mr-1" />
          Debug Coords
        </Button>
      </div>
    );
  }

  const handleRefreshLocationService = async () => {
    await refreshLocationSettings();
  };

  return (
    <div className="fixed top-20 right-4 z-50 bg-white border rounded-lg p-3 shadow-lg max-w-xs text-xs">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-semibold">Coordinate Debug</h3>
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={handleRefreshLocationService}
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => setIsVisible(false)}
          >
            <EyeOff className="h-3 w-3" />
          </Button>
        </div>
      </div>
      
      <div className="space-y-2">
        <div>
          <Badge variant={demoEnabled ? "default" : "secondary"} className="mb-1">
            Demo Mode: {demoEnabled ? "ON" : "OFF"}
          </Badge>
          <p>Source: <code>{source}</code></p>
        </div>
        
        <div>
          <p className="font-medium">Current Location:</p>
          <p>Lat: {userLocation?.lat?.toFixed(6) || "null"}</p>
          <p>Lng: {userLocation?.lng?.toFixed(6) || "null"}</p>
        </div>
        
        <div>
          <p className="font-medium">Service Demo Location:</p>
          <p>Lat: {demoLocation?.lat?.toFixed(6) || "null"}</p>
          <p>Lng: {demoLocation?.lng?.toFixed(6) || "null"}</p>
        </div>
        
        <div>
          <p className="font-medium">DB Demo Coordinates:</p>
          <p>Lat: {demoCoords?.lat?.toFixed(6) || "null"}</p>
          <p>Lng: {demoCoords?.lng?.toFixed(6) || "null"}</p>
        </div>
        
        <div>
          <p className="font-medium">User Details:</p>
          <p>Map Demo: {userDetails?.map_demo ? "ON" : "OFF"}</p>
          <p>User ID: {user?.id?.slice(0, 8) || "null"}...</p>
        </div>
      </div>
    </div>
  );
};

export default CoordinateFlowDebugger; 
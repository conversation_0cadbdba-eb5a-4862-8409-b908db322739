
import { Check, Clock, X } from "lucide-react";

interface BookingStatusProps {
  status: string;
}

export const getStatusLabel = (status: string) => {
  switch (status) {
    case 'confirmed':
      return 'Confermata';
    case 'pending':
      return 'In Attesa';
    case 'cancelled':
      return 'Cancellata';
    default:
      return status;
  }
};

export const getStatusClass = (status: string) => {
  switch (status) {
    case 'confirmed':
      return 'bg-green-100 text-green-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'cancelled':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const BookingStatus = ({ status }: BookingStatusProps) => {
  return (
    <span className={`${getStatusClass(status)} px-3 py-1 rounded-full text-sm font-medium inline-flex items-center`}>
      {status === 'confirmed' && <Check size={14} className="mr-1" />}
      {status === 'pending' && <Clock size={14} className="mr-1" />}
      {status === 'cancelled' && <X size={14} className="mr-1" />}
      {getStatusLabel(status)}
    </span>
  );
};

export default BookingStatus;

import { Calendar } from "lucide-react";
import FlexibleDealCarousel from "./FlexibleDealCarousel";
import { useFlexibleDeals } from "@/hooks/useFlexibleDeals";

interface WeekendDealsCarouselProps {
  className?: string;
}

const WeekendDealsCarousel = ({ className = "" }: WeekendDealsCarouselProps) => {
  const { sections, isLoading } = useFlexibleDeals('weekend', {
    maxSections: 1,
    dealsPerSection: 15,
    userPreferences: false
  });

  return (
    <FlexibleDealCarousel
      title="Offerte Weekend"
      titleIcon={Calendar}
      sections={sections}
      isLoading={isLoading}
      className={className}
    />
  );
};

export default WeekendDealsCarousel;
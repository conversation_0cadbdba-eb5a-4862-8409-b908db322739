
import { Upload, Loader2 } from "lucide-react";
import { motion } from "framer-motion";

interface AvatarUploadProps {
  fileInputRef: React.RefObject<HTMLInputElement>;
  avatarPreview: string | null;
  isUploadingAvatar: boolean;
  onAvatarClick: () => void;
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export const AvatarUpload = ({
  fileInputRef,
  avatarPreview,
  isUploadingAvatar,
  onAvatarClick,
  onFileChange
}: AvatarUploadProps) => {
  return (
    <motion.div 
      initial={{
        opacity: 0,
        y: 20
      }} 
      animate={{
        opacity: 1,
        y: 0
      }} 
      transition={{
        delay: 0.1
      }} 
      className="flex flex-col items-center mb-8"
    >
      <input
        type="file"
        ref={fileInputRef}
        onChange={onFileChange}
        accept="image/*"
        capture="environment"
        className="hidden"
      />
      <div 
        onClick={onAvatarClick}
        className="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center mb-3 cursor-pointer overflow-hidden relative"
      >
        {avatarPreview ? (
          <img 
            src={avatarPreview} 
            alt="Avatar preview" 
            className="w-full h-full object-cover"
          />
        ) : (
          <Upload className="h-8 w-8 text-gray-400" />
        )}
      </div>
      <button 
        onClick={onAvatarClick}
        type="button" 
        className="text-brand-primary font-medium flex items-center gap-2"
        disabled={isUploadingAvatar}
      >
        {isUploadingAvatar ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin" />
            Caricamento...
          </>
        ) : (
          'Aggiungi foto (opzionale)'
        )}
      </button>
    </motion.div>
  );
};


import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { BookingWithDetails } from "@/types/booking";

interface BookingActionsProps {
  booking: BookingWithDetails;
  onBookingUpdate: (updatedBooking: BookingWithDetails) => void;
}

const BookingActions = ({ booking, onBookingUpdate }: BookingActionsProps) => {
  const [isCancelling, setIsCancelling] = useState(false);

  const isBookingInPast = () => {
    const bookingDate = new Date(booking.booking_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return bookingDate < today;
  };

  const handleCancelBooking = async () => {
    if (!booking || booking.status === "cancelled" || isBookingInPast()) return;

    try {
      setIsCancelling(true);

      // Update the qrcode to be cancelled
      const updatedQrData = {
        ...booking.qr_data,
        status: "cancelled",
      };

      // 1. Aggiorna lo stato della prenotazione
      const { error } = await supabase
        .from("bookings")
        .update({
          status: "cancelled",
          qr_data: updatedQrData,
        })
        .eq("id", booking.id);

      if (error) {
        toast.error("Errore durante la cancellazione");
        return;
      }

      // 2. Rimuovi la prenotazione dalla tabella time_slot_bookings
      const { error: timeSlotError } = await supabase
        .from("time_slot_bookings")
        .delete()
        .eq("booking_id", booking.id);

      if (timeSlotError) {
        console.error("Errore nella cancellazione degli slot orari:", timeSlotError);
        // Non blocchiamo il flusso ma registriamo l'errore
      }

      // 3. Aggiorniamo anche il conteggio dei posti prenotati nel time_slots dell'offerta
      try {
        if (booking.deal_id && booking.booking_time) {
          // Otteniamo prima i time_slots correnti e il giorno della settimana
          const { data: dealData } = await supabase
            .from('deals')
            .select('time_slots')
            .eq('id', booking.deal_id)
            .single();
            
          const bookingDate = new Date(booking.booking_date);
          const dayOfWeek = bookingDate.getDay() || 7; // Converte 0 (domenica) in 7
          
          if (dealData && dealData.time_slots) {
            // È necessario definire updatedTimeSlots prima di utilizzarlo
            let timeSlots = typeof dealData.time_slots === 'string' 
              ? JSON.parse(dealData.time_slots) 
              : dealData.time_slots;
            
            // Creiamo una copia aggiornata dei time_slots
            // Nota: questa è una semplificazione, in un caso reale dovresti
            // trovare lo slot specifico e decrementare il conteggio dei posti prenotati
            const updatedTimeSlots = timeSlots;
  
            await supabase
              .from('deals')
              .update({
                time_slots: updatedTimeSlots
              })
              .eq('id', booking.deal_id);
          }
        }
      } catch (error) {
        console.error("Errore nell'aggiornamento dei time_slots dell'offerta:", error);
      }

      onBookingUpdate({
        ...booking,
        status: "cancelled",
        qr_data: updatedQrData,
      });
      //toast.success("Prenotazione cancellata con successo");
    } catch (error) {
      console.error("Error cancelling booking:", error);
      toast.error("Si è verificato un errore");
    } finally {
      setIsCancelling(false);
    }
  };

  return (
    <div className="mt-6 pt-6 border-t">
      <div className="flex justify-between items-center mb-6 bg-gray-50 p-4 rounded-lg shadow-sm">
        {booking.original_price === 0 && booking.discounted_price === 0 ? (
          <>
            <div className="flex flex-col">
              <span className="text-gray-600 text-2xl font-medium">Sconto per te</span>
             
            </div>
            <div className="flex items-center">
              <span className="text-2xl font-bold text-brand-primary">
                {booking.discount_percentage}%
              </span>
              
            </div>
          </>
        ) : (
          <div className="flex flex-col w-full space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 font-medium">Prezzo originale</span>
              <span className="line-through text-sm text-gray-400">
                €{booking.original_price}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 text-2xl  font-bold">Prezzo per te</span>
              <span className="text-2xl font-bold text-brand-primary">
                €{booking.discounted_price}
              </span>
            </div>
          </div>
        )}
      </div>

      {booking.status !== "cancelled" && (
        <button
          onClick={handleCancelBooking}
          disabled={isCancelling || isBookingInPast()}
          className="w-full py-3 px-4 bg-red-600 text-white rounded-xl font-medium hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isCancelling
            ? "Cancellazione in corso..."
            : isBookingInPast()
            ? "Prenotazione passata"
            : "Cancella Prenotazione"}
        </button>
      )}

      {booking.status === "cancelled" && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4 text-center">
          <p className="text-red-600 font-medium">
            Questa prenotazione è stata cancellata
          </p>
          <p className="text-gray-500 text-sm mt-1">
            Non è più possibile utilizzare questa prenotazione
          </p>
        </div>
      )}
    </div>
  );
};

export default BookingActions;

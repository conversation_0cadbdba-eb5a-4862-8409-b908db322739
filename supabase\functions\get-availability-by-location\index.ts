import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";
import { corsHeaders } from "../_shared/cors.ts";

// Types for request parameters
interface LocationParams {
  lat: number;
  lng: number;
  radius?: number;
}

interface RequestParams {
  date: string;
  time?: string;
  location?: LocationParams;
  cityName?: string;
  category_id?: string;
  cursor?: string;
  limit?: number;
}

// Response types
interface BusinessAvailability {
  business_id: string;
  business_name: string;
  address: string;
  latitude: number;
  longitude: number;
  distance: number | null;
  photos: string[] | null;
  category_id: string;
  available_slots: any;
}

interface AvailabilityResponse {
  date: string;
  time?: string;
  businesses: BusinessAvailability[];
  next_cursor: string | null;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Extract and validate request parameters
    let params: RequestParams;
    try {
      params = await req.json();
    } catch (e) {
      return new Response(
        JSON.stringify({
          error: "Invalid parameters",
          details: "Request body must be valid JSON"
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    const { 
      date, 
      time, 
      location, 
      category_id, 
      cursor,
      limit = 20 
    } = params;

    // Validate required parameters
    if (!date) {
      return new Response(
        JSON.stringify({
          error: "Missing required parameter",
          details: "Date is required"
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    console.log(`Processing availability request for date: ${date}, cursor: ${cursor || 'initial'}`);

    const payLoad = {
        p_date: date,
        p_time: time || null,
        p_lat: location?.lat || null,
        p_lng: location?.lng || null,
        p_radius: location?.radius || 5000,
        p_category_id: category_id || null,
        p_cursor: cursor || null,
        p_limit: limit
      };
    console.log(`Processing with pay load: ${payLoad}`)
    // Call the optimized stored procedure
    const { data, error } = await supabase
      .rpc('get_available_businesses', {
        p_date: date,
        p_time: time || null,
        p_lat: location?.lat || null,
        p_lng: location?.lng || null,
        p_radius: location?.radius || 5000,
        p_category_id: category_id || null,
        p_cursor: cursor || null,
        p_limit: limit
      });

    if (error) {
      console.error("Database query error:", error);
      return new Response(
        JSON.stringify({
          error: "Database query failed",
          details: error.message
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    // Prepare the response
    // {
    //   business_id: string;
    //   business_name: string;
    //   address: string;
    //   latitude: number;
    //   longitude: number;
    //   distance: number | null;
    //   photos: string[] | null;
    //   category_id: string;
    //   available_slots: TimeSlot[];  // Direct array of slots
    // }
    const response: AvailabilityResponse = {
      date,
      time,
      businesses: data.map(business => ({
        business_id: business.business_id,
        business_name: business.business_name,
        address: business.address,
        latitude: business.latitude,
        longitude: business.longitude,
        distance: business.distance,
        photos: business.photos,
        category_id: business.category_id,
        available_slots: business.available_slots
      })),
      next_cursor: data[data.length - 1]?.next_cursor || null
    };

    // Log success metrics
    console.log(`Request completed successfully. Found ${response.businesses.length} businesses`);
    if (response.next_cursor) {
      console.log(`Next cursor available: ${response.next_cursor}`);
    }

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );

  } catch (error) {
    console.error("Unexpected error:", error);
    
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        details: error.message
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500
      }
    );
  }
});

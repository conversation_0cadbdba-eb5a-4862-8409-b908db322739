
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ChevronRight } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/auth/useAuth";
import { toast } from "sonner";
import type { Database } from "@/integrations/supabase/types";

type Booking = Database['public']['Tables']['bookings']['Row'] & {
  deals: {
    businesses: {
      name: string;
    } | null;
  } | null;
};

const NextBooking = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [nextBooking, setNextBooking] = useState<Booking | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchNextBooking = async () => {
      if (!user) return;

      try {
        const now = new Date();
        const today = now.toISOString().split('T')[0];
        const currentTime = now.toTimeString().split(' ')[0];

        const { data, error } = await supabase
          .from('bookings')
          .select(`
            *,
            deals (
              businesses (
                name
              )
            )
          `)
          .eq('user_id', user.id)
          .eq('status', 'confirmed')
          .or(`booking_date.gt.${today},and(booking_date.eq.${today},booking_time.gt.${currentTime})`)
          .order('booking_date', { ascending: true })
          .order('booking_time', { ascending: true })
          .limit(1)
          .maybeSingle();

        if (error) {
          console.error('Error fetching next booking:', error);
          return;
        }

        setNextBooking(data);
      } catch (error) {
        console.error('Error in fetchNextBooking:', error);
        toast.error("Errore nel caricamento della prenotazione");
      } finally {
        setIsLoading(false);
      }
    };

    fetchNextBooking();
  }, [user]);

  if (isLoading) {
    return (
      <section className="mt-4 bg-brand-light rounded-xl p-4">
        <div className="animate-pulse">
          <div className="h-4 w-32 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 w-48 bg-gray-200 rounded"></div>
        </div>
      </section>
    );
  }

  if (!nextBooking) {
    return (
      <section className="mt-4 bg-brand-light rounded-xl p-4">
        <h3 className="text-brand-primary font-semibold">Prossima Prenotazione</h3>
        <p className="text-gray-600 mt-1">Non hai prenotazioni in programma</p>
      </section>
    );
  }

  // Formatta l'orario rimuovendo i secondi
  const formattedTime = nextBooking.booking_time.split(':').slice(0, 2).join(':');
  
  const bookingDate = new Date(nextBooking.booking_date);
  const isToday = bookingDate.toDateString() === new Date().toDateString();
  const dateText = isToday ? "Oggi" : bookingDate.toLocaleDateString('it-IT', {
    weekday: 'long',
    day: 'numeric',
    month: 'long'
  });

  return (
    <section 
      className="mt-4 bg-brand-light rounded-xl p-4 cursor-pointer"
      onClick={() => navigate(`/prenotazione/${nextBooking.id}`)}
    >
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-brand-primary font-semibold">Prossima Prenotazione</h3>
          <p className="text-gray-700 mt-1">{nextBooking.deals?.businesses?.name || 'Attività'}</p>
          <p className="text-sm text-gray-600">{dateText} alle {formattedTime}</p>
          <span className="inline-block mt-2 text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
            Confermato
          </span>
        </div>
        <ChevronRight className="h-5 w-5 text-gray-400" />
      </div>
    </section>
  );
};

export default NextBooking;

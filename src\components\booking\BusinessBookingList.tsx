
import { Eye } from "lucide-react";
import { useNavigate } from "react-router-dom";
import type { BookingWithDetails } from "@/types/booking";
import BookingStatus from "./BookingStatus";
import BookingInfo from "./BookingInfo";
import CancellationForm from "./CancellationForm";

interface BusinessBookingListProps {
  bookings: BookingWithDetails[];
  businessId: string;
  selectedBookingId: string | null;
  setSelectedBookingId: (id: string | null) => void;
  onApprove: (bookingId: string) => Promise<void>;
  onCancel: (bookingId: string, note: string) => Promise<void>;
  isCancelling: boolean;
}

const BusinessBookingList = ({
  bookings,
  businessId,
  selectedBookingId,
  setSelectedBookingId,
  onApprove,
  onCancel,
  isCancelling
}: BusinessBookingListProps) => {
  const navigate = useNavigate();

  return (
    <div className="space-y-2">
      {bookings.map((booking) => (
        <div key={booking.id} className="bg-white rounded-lg shadow-sm p-2 space-y-2">
          <div className="flex items-start justify-between gap-2">
            <div className="space-y-0.5">
              <h3 className="font-medium text-sm">{booking.deals?.title}</h3>
              <p className="text-xs text-gray-500">
                {new Date(booking.booking_date).toLocaleDateString('it-IT', {
                  weekday: 'long',
                  day: 'numeric',
                  month: 'long'
                })} - {booking.booking_time}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => navigate(`/business/${businessId}/bookings/${booking.id}`)}
                className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
              >
                <Eye className="h-4 w-4 text-gray-600" />
              </button>
              <BookingStatus status={booking.status} />
            </div>
          </div>

          <BookingInfo booking={booking} />

          {booking.status === 'pending' && (
            <div className="flex gap-2">
              <button
                onClick={() => onApprove(booking.id)}
                className="flex-1 bg-green-600 text-white px-2 py-1 rounded text-xs font-medium hover:bg-green-700 transition-colors"
              >
                Approva
              </button>
              <button
                onClick={() => setSelectedBookingId(booking.id)}
                className="flex-1 bg-red-600 text-white px-2 py-1 rounded text-xs font-medium hover:bg-red-700 transition-colors"
              >
                Cancella
              </button>
            </div>
          )}

          {selectedBookingId === booking.id && (
            <CancellationForm
              onCancel={(note) => onCancel(booking.id, note)}
              onClose={() => setSelectedBookingId(null)}
              isCancelling={isCancelling}
            />
          )}
        </div>
      ))}
    </div>
  );
};

export default BusinessBookingList;

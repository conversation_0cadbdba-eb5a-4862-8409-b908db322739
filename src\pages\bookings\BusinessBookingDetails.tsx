import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ArrowLeft, User } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { BookingWithDetails } from "@/types/booking";
import BookingStatus from "@/components/booking/BookingStatus";
import BookingInfo from "@/components/booking/BookingInfo";
import { parseBookingFromDB } from "@/types/booking";

const BusinessBookingDetails = () => {
  const { bookingId } = useParams();
  const navigate = useNavigate();
  const [booking, setBooking] = useState<BookingWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [cancellationNote, setCancellationNote] = useState("");
  const [isCancelling, setIsCancelling] = useState(false);

  useEffect(() => {
    const fetchBooking = async () => {
      if (!bookingId) return;

      try {
        const { data: bookingData, error: bookingError } = await supabase
          .from('bookings')
          .select(`
            *,
            deals (
              title,
              images,
              description,
              discounted_price,
              businesses (
                name,
                address
              )
            )
          `)
          .eq('id', bookingId)
          .maybeSingle();

        if (bookingError) {
          toast.error("Errore nel caricamento della prenotazione");
          return;
        }

        if (bookingData) {
          const { data: userData } = await supabase
            .from('user_details')
            .select('first_name, last_name, avatar_url')
            .eq('id', bookingData.user_id)
            .maybeSingle();

          setBooking(parseBookingFromDB({
            ...bookingData,
            user_details: userData || null
          }));
        }
      } catch (error) {
        console.error('Error fetching booking:', error);
        toast.error("Si è verificato un errore");
      } finally {
        setIsLoading(false);
      }
    };

    fetchBooking();
  }, [bookingId]);

  const handleApprove = async () => {
    if (!booking) return;
    
    try {
      const { error } = await supabase
        .from('bookings')
        .update({ status: 'confirmed' })
        .eq('id', booking.id);

      if (error) throw error;
      
      setBooking({ ...booking, status: 'confirmed' });
  //    toast.success("Prenotazione approvata con successo");
    } catch (error) {
      console.error('Error approving booking:', error);
      toast.error("Errore nell'approvazione della prenotazione");
    }
  };

  const handleCancel = async () => {
    if (!booking || !cancellationNote) {
      toast.error("Inserisci una nota per la cancellazione");
      return;
    }

    setIsCancelling(true);
    try {
      const { error } = await supabase
        .from('bookings')
        .update({
          status: 'cancelled',
          cancellation_note: cancellationNote
        })
        .eq('id', booking.id);

      if (error) throw error;

      setBooking({ ...booking, status: 'cancelled', cancellation_note: cancellationNote });
   //   toast.success("Prenotazione cancellata con successo");
      setCancellationNote("");
    } catch (error) {
      console.error('Error cancelling booking:', error);
      toast.error("Errore nella cancellazione della prenotazione");
    } finally {
      setIsCancelling(false);
    }
  };

  const getFullName = () => {
    if (!booking.user_details) return 'Cliente';
    return [booking.user_details.first_name, booking.user_details.last_name]
      .filter(Boolean)
      .join(' ');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-pulse">Caricamento...</div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <p className="text-gray-500">Prenotazione non trovata</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50 px-4 py-4">
        <div className="flex items-center">
          <button onClick={() => navigate(-1)} className="text-gray-700">
            <ArrowLeft className="h-6 w-6" />
          </button>
          <h1 className="text-xl font-semibold ml-4">Dettagli Prenotazione</h1>
        </div>
      </header>

      <main className="pt-20 pb-24 px-4">
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="h-48 relative">
            <img 
              src={booking.deals?.images?.[0] || `https://picsum.photos/800/600?random=${booking.id}`}
              alt={booking.deals?.title || 'Prenotazione'}
              className="w-full h-full object-cover"
            />
            <div className="absolute top-4 right-4">
              <BookingStatus status={booking.status} />
            </div>
          </div>

          <div className="p-4 space-y-6">
            <div>
              <h2 className="text-xl font-semibold mb-4">{booking.deals?.title}</h2>
              <BookingInfo booking={booking} />
            </div>

            <div className="border-t pt-4">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                  {booking.user_details?.avatar_url ? (
                    <img 
                      src={booking.user_details.avatar_url} 
                      alt={getFullName()} 
                      className="h-full w-full rounded-full object-cover"
                    />
                  ) : (
                    <User className="h-5 w-5 text-gray-400" />
                  )}
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">
                    {getFullName()}
                  </h3>
                  <p className="text-sm text-gray-500">Cliente</p>
                </div>
              </div>
            </div>

            {booking.status === 'pending' && (
              <div className="space-y-4">
                <div className="flex gap-2">
                  <button
                    onClick={() => handleApprove()}
                    className="flex-1 bg-green-600 text-white px-4 py-2 rounded font-medium hover:bg-green-700 transition-colors"
                  >
                    Approva Prenotazione
                  </button>
                  <button
                    onClick={() => document.getElementById('cancellationNote')?.focus()}
                    className="flex-1 bg-red-600 text-white px-4 py-2 rounded font-medium hover:bg-red-700 transition-colors"
                  >
                    Cancella Prenotazione
                  </button>
                </div>

                <div className="space-y-2">
                  <textarea
                    id="cancellationNote"
                    placeholder="Inserisci una nota per la cancellazione..."
                    value={cancellationNote}
                    onChange={(e) => setCancellationNote(e.target.value)}
                    className="w-full p-3 border rounded text-sm"
                    rows={3}
                  />
                  <button
                    onClick={() => handleCancel()}
                    disabled={!cancellationNote || isCancelling}
                    className="w-full bg-red-600 text-white px-4 py-2 rounded font-medium hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isCancelling ? "Cancellazione in corso..." : "Conferma Cancellazione"}
                  </button>
                </div>
              </div>
            )}

            {booking.status === 'cancelled' && booking.cancellation_note && (
              <div className="bg-red-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-red-800 mb-2">Nota di Cancellazione:</h3>
                <p className="text-sm text-red-700">{booking.cancellation_note}</p>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default BusinessBookingDetails;

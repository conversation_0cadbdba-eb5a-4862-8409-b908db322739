import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface UserGroup {
  id: string;
  name: string;
  description?: string;
  avatar_url?: string;
  member_count: number;
  user_role: 'admin' | 'member';
}

export const useUserGroups = () => {
  return useQuery({
    queryKey: ['user-groups'],
    queryFn: async (): Promise<UserGroup[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get user's groups with member counts
      const { data: userGroups, error } = await supabase
        .from('group_members')
        .select(`
          role,
          groups!inner(
            id,
            name,
            description,
            avatar_url
          )
        `)
        .eq('user_id', user.id);

      if (error) throw error;

      if (!userGroups || userGroups.length === 0) {
        return [];
      }

      // Get member counts for each group
      const groupIds = userGroups.map(ug => ug.groups.id);
      const { data: memberCounts, error: memberCountError } = await supabase
        .from('group_members')
        .select('group_id')
        .in('group_id', groupIds);

      if (memberCountError) throw memberCountError;

      // Count members per group
      const memberCountMap = memberCounts?.reduce((acc, member) => {
        acc[member.group_id] = (acc[member.group_id] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

      return userGroups.map(ug => ({
        id: ug.groups.id,
        name: ug.groups.name,
        description: ug.groups.description,
        avatar_url: ug.groups.avatar_url,
        member_count: memberCountMap[ug.groups.id] || 0,
        user_role: ug.role as 'admin' | 'member',
      }));
    },
    enabled: true,
  });
};

import React from 'react';
import { AnimatePresence } from 'framer-motion';
import OnboardingScreen from '@/components/onboarding/OnboardingScreen';
import { onboardingScreens } from '@/data/onboardingScreens';
import { useOnboarding } from '@/hooks/useOnboarding';
import { Navigate } from 'react-router-dom';

const Onboarding: React.FC = () => {
  const {
    hasCompletedOnboarding,
    currentStep,
    nextStep,
    previousStep,
    skipOnboarding,
    completeOnboarding
  } = useOnboarding();

  // Se l'onboarding è già stato completato, reindirizza alla dashboard
  if (hasCompletedOnboarding === true) {
    return <Navigate to="/dashboard" />;
  }

  // Se lo stato dell'onboarding non è ancora stato determinato, mostra un loader
  if (hasCompletedOnboarding === null) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="w-10 h-10 border-4 border-brand-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  const currentScreen = onboardingScreens[currentStep];

  return (
    <div className="h-screen bg-white">
      <AnimatePresence mode="wait">
        <OnboardingScreen
          key={currentScreen.id}
          image={currentScreen.image}
          title={currentScreen.title}
          description={currentScreen.description}
          currentStep={currentStep}
          totalSteps={onboardingScreens.length}
          onNext={nextStep}
          onPrevious={previousStep}
          onSkip={skipOnboarding}
          onComplete={completeOnboarding}
        />
      </AnimatePresence>
    </div>
  );
};

export default Onboarding;

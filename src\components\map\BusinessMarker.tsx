import { AdvancedMarker } from "@vis.gl/react-google-maps";
import { useCallback, useEffect, useRef, useState } from "react";
import { useMap } from "@vis.gl/react-google-maps";

import { RealEstateIcon } from "@/icons/real-estate-icon";
import { Business } from "./types";
import { MarkerClusterer } from "@googlemaps/markerclusterer";
import { RealEstateGallery } from "./real-estate-gallery";
import { cn } from "@/lib/utils";

import type { Marker as MarkerCluster } from "@googlemaps/markerclusterer";

import "./custom-advanced-marker";
/**
 * BusinessMarker Component
 * 
 * @component
 * @param {Object} props - Component props
 * @param {Business[]} props.businesses - Array of business objects to display as markers
 * @param {function} [props.onBusinessClick] - Optional callback function triggered when a business marker is clicked
 * @returns {JSX.Element} Rendered component
 */
export const BusinessMarker = ({ 
  businesses, 
  onBusinessClick 
}: { 
  businesses: Business[];
  onBusinessClick?: (business: Business) => void;
}) => {
    const map = useMap();
    const [clickedMarkerId, setClickedMarkerId] = useState<string | null>(null);
    const [hoveredMarkerId, setHoveredMarkerId] = useState<string | null>(null);
    const [markers, setMarkers] = useState<{ [key: string]: MarkerCluster }>(
      {}
    );
    const clusterer = useRef<MarkerClusterer | null>(null);

    const renderCustomPin = (business: Business) => {
 
      console.log(business);
      return (
        <>
          <div className="custom-pin">
            <button className="close-button">
              <span className="material-symbols-outlined"> close </span>
            </button>

            <div className="image-container">
              <RealEstateGallery images={business.photos} isExtended={ clickedMarkerId === business.id} />
              <span className="icon">
                <RealEstateIcon />
              </span>
            </div>

            {/* <RealEstateListingDetails details={realEstateListing.details} /> */}
          </div>

          <div className="tip" />
        </>
      );
    };

    useEffect(() => {
      if (!map) return;
      if (!clusterer.current) {
        clusterer.current = new MarkerClusterer({ map });
      }
    }, [map]);

    useEffect(() => {
      clusterer.current?.clearMarkers();
      clusterer.current?.addMarkers(Object.values(markers));
    }, [markers]);

    const setMarkerRef = (marker: MarkerCluster | null, key: string) => {
      if (marker && markers[key]) return;
      if (!marker && !markers[key]) return;

      setMarkers((prev) => {
        if (marker) {
          return { ...prev, [key]: marker };
        } else {
          const newMarkers = { ...prev };
          delete newMarkers[key];
          return newMarkers;
        }
      });
    };

    const handleMarkerClick = useCallback(
      (business: Business) => {
        if (!map) return;

        console.log("business clicked:", business.name);
        setClickedMarkerId((prevId) =>
          prevId === business.id ? null : business.id
        );
        // Centro la mappa sulla posizione del business
        if (business.latitude && business.longitude) {
          map.panTo({ lat: business.latitude, lng: business.longitude });
        }

        // Chiamata alla callback se definita
        if (onBusinessClick) {
          onBusinessClick(business);
        }
      },
      [map, onBusinessClick]
    );

    return (
      <>
        {businesses.map((business) => {
          if (!business.latitude || !business.longitude) return null;
          const isClicked = clickedMarkerId === business.id;
          const isHovered = hoveredMarkerId === business.id;
          return (
            <AdvancedMarker
              key={business.id}
              position={{ lat: business.latitude, lng: business.longitude }}
              ref={(marker) => setMarkerRef(marker, business.id)}
              //  onClick={() => handleMarkerClick(business)}
              title={business.name}
              onMouseEnter={() => setHoveredMarkerId(business.id)}
              onMouseLeave={() => setHoveredMarkerId(null)}
              className={cn("real-estate-marker", {
                clicked: isClicked,
                hovered: isHovered,
              })}
              onClick={() => handleMarkerClick(business)}
            >
              {renderCustomPin(business)}
            </AdvancedMarker>
          );
        })}
      </>
    );
  };


  

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface ReceivedInvite {
  id: string;
  group_id: string;
  invited_by: string;
  status: string;
  created_at: string;
  group: {
    name: string;
    description?: string;
    avatar_url?: string;
  };
  invited_by_user: {
    first_name: string;
    last_name: string;
    avatar_url?: string;
  };
}

export const useReceivedInvites = () => {
  const query = useQuery({
    queryKey: ['received-invites'],
    queryFn: async (): Promise<ReceivedInvite[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return [];
      }

      const { data, error } = await supabase
        .from('group_invites')
        .select(`
          id,
          group_id,
          invited_by,
          status,
          created_at,
          group:groups!group_invites_group_id_fkey(
            name,
            description,
            avatar_url
          ),
          invited_by_user:users_with_details!group_invites_invited_by_fkey(
            first_name,
            last_name,
            avatar_url
          )
        `)
        .eq('invited_user_id', user.id)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

    //  console.log('data', data);
      if (error) {
        console.error('Error fetching received invites:', error);
        throw error;
      }

      return data || [];
    }
  });

  return query;
};

export const useAcceptInvite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (inviteId: string) => {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get invite details first
      const { data: invite, error: inviteError } = await supabase
        .from('group_invites')
        .select('group_id, invited_user_id')
        .eq('id', inviteId)
        .maybeSingle();

      if (inviteError) {
        console.error('Error fetching invite details:', inviteError);
        throw inviteError;
      }

      // Start a transaction by using multiple operations
      // First, add the user to group_members
      const { error: memberError } = await supabase
        .from('group_members')
        .insert({
          group_id: invite.group_id,
          user_id: invite.invited_user_id,
          role: 'member'
        });

      if (memberError) {
        console.error('Error adding group member:', memberError);
        throw memberError;
      }

      // Then delete the invite
      const { error: deleteError } = await supabase
        .from('group_invites')
        .delete()
        .eq('id', inviteId);

    
      if (deleteError) {
        console.error('Error deleting invite:', deleteError);
        // Try to rollback by removing the member we just added
        await supabase
          .from('group_members')
          .delete()
          .eq('group_id', invite.group_id)
          .eq('user_id', invite.invited_user_id);
        throw deleteError;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['received-invites'] });
      queryClient.invalidateQueries({ queryKey: ['groups'] });
      queryClient.invalidateQueries({ queryKey: ['user-groups'] });
      queryClient.invalidateQueries({ queryKey: ['group-details'] });
      toast.success('Invito accettato con successo');
    },
    onError: () => {
      toast.error('Errore nell\'accettazione dell\'invito');
    }
  });
};

export const useDeclineInvite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (inviteId: string) => {
      const { error } = await supabase
        .from('group_invites')
        .update({ status: 'declined' })
        .eq('id', inviteId);

      if (error) {
        console.error('Error declining invite:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['received-invites'] });
      queryClient.invalidateQueries({ queryKey: ['groups'] });
      toast.success('Invito rifiutato');
    },
    onError: () => {
      toast.error('Errore nel rifiuto dell\'invito');
    }
  });
};

import { useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useLocation } from '@/contexts/LocationContext';
import { useAuth } from '@/hooks/auth/useAuth';

export const useLocationTracking = () => {
  const { coordinates, demoEnabled } = useLocation();
  const { user } = useAuth();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastPositionRef = useRef<{ lat: number; lng: number } | null>(null);
  const realLocationRef = useRef<{ lat: number; lng: number } | null>(null);

  // Get real location when demo mode is active
  const getRealLocation = (): Promise<{ lat: number; lng: number } | null> => {
    return new Promise((resolve) => {
      if (!demoEnabled || !navigator.geolocation) {
        resolve(null);
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const realCoords = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          realLocationRef.current = realCoords;
          resolve(realCoords);
        },
        (error) => {
          console.error('Error getting real location:', error);
          resolve(null);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );
    });
  };

  const saveLocation = async () => {
    if (!user?.id) {
      return;
    }

    let locationToSave = coordinates;
    
    // If demo mode is active, try to get real location for database storage
    if (demoEnabled) {
      const realLocation = await getRealLocation();
      if (realLocation) {
        locationToSave = realLocation;
      } else if (!coordinates) {
        return; // No coordinates available at all
      }
    } else if (!coordinates) {
      return; // No coordinates available
    }

    // Check if position has changed significantly (within ~10 meters)
    if (lastPositionRef.current && locationToSave) {
      const latDiff = Math.abs(locationToSave.lat - lastPositionRef.current.lat);
      const lngDiff = Math.abs(locationToSave.lng - lastPositionRef.current.lng);
      
      // Skip if position change is less than ~0.0001 degrees (~10 meters)
      if (latDiff < 0.0001 && lngDiff < 0.0001) {
        return;
      }
    }

    if (!locationToSave) {
      return;
    }

    try {
      const { error } = await supabase
        .from('user_location_history')
        .insert({
          user_id: user.id,
          latitude: locationToSave.lat,
          longitude: locationToSave.lng,
          source: demoEnabled ? 'real_during_demo' : 'automatic'
        });

      if (error) {
        console.error('Errore nel salvare la posizione:', error);
      } else {
        // Update last position only on successful save
        lastPositionRef.current = { lat: locationToSave.lat, lng: locationToSave.lng };
      }
    } catch (error) {
      console.error('Errore imprevisto nel salvare la posizione:', error);
    }
  };

  useEffect(() => {
    if (!user?.id) {
      // Clear interval if user is not logged in
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Only set up interval if there isn't one already
    if (!intervalRef.current) {
      // Set up interval to save location every minute (60000ms)
      intervalRef.current = setInterval(saveLocation, 60000);
      
      // Save location immediately when tracking starts
      saveLocation();
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [user?.id]);

  return {
    saveLocation
  };
};
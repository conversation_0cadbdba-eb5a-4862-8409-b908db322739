import { useState, useEffect } from 'react';
import { SubscriptionService } from '@/services/subscriptionService';
import { SubscriptionPlanDetails } from '@/types/subscription';

export interface UsePricingPlansReturn {
  plans: Record<string, SubscriptionPlanDetails>;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const usePricingPlans = (): UsePricingPlansReturn => {
  const [plans, setPlans] = useState<Record<string, SubscriptionPlanDetails>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPlans = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const subscriptionPlans = await SubscriptionService.getSubscriptionPlans();
      setPlans(subscriptionPlans);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch pricing plans';
      setError(errorMessage);
      console.error('Error fetching pricing plans:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const refetch = async () => {
    await fetchPlans();
  };

  useEffect(() => {
    fetchPlans();
  }, []);

  return {
    plans,
    isLoading,
    error,
    refetch,
  };
}; 

import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export const useTextToSpeechSample = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null);

  const playVoiceSample = async (voiceId: string | null, text: string = "Ciao, sono il tuo assistente vocale. Come posso aiutarti oggi?") => {
    if (!voiceId) {
      toast.error("ID voce non disponibile per questo assistente");
      return;
    }

    try {
      setIsLoading(true);

      // Ferma qualsiasi riproduzione precedente
      if (audioElement) {
        audioElement.pause();
      }

      // Chiama la funzione edge per la conversione testo-voce
      const { data, error } = await supabase.functions.invoke("text-to-speech", {
        body: { text, voiceId }
      });

      if (error) {
        throw error;
      }

      if (!data?.audio) {
        throw new Error("Nessun audio ricevuto dal server");
      }

      // Crea un nuovo elemento audio e riproduci il risultato
      const audio = new Audio(`data:audio/mpeg;base64,${data.audio}`);
      audio.onended = () => setIsLoading(false);
      audio.onerror = () => {
        toast.error("Errore nella riproduzione dell'audio");
        setIsLoading(false);
      };

      setAudioElement(audio);
      await audio.play();
    } catch (error) {
      console.error("Errore nell'ascolto del campione vocale:", error);
      toast.error("Impossibile riprodurre il campione vocale");
      setIsLoading(false);
    }
  };

  const stopPlayback = () => {
    if (audioElement) {
      audioElement.pause();
      setIsLoading(false);
    }
  };

  return {
    playVoiceSample,
    stopPlayback,
    isPlaying: isLoading
  };
};

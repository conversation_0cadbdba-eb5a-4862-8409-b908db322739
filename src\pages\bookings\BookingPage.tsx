import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { DateAvailabilitySelector } from '@/components/DateAvailabilitySelector';
import { formatDate } from '@/utils/dateUtils';
import { Deal, DealFromDB } from '@/types/deals';
import { toast } from 'sonner';

interface BookingAvailability {
  available: number;
  capacity: number;
  booked: number;
}

/**
 * Page component for booking a deal
 */
export function BookingPage() {
  const { id } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const initialDate = searchParams.get('date');
  const initialQuantity = parseInt(searchParams.get('quantity') || '1', 10);
  
  const [step, setStep] = useState(initialDate ? 2 : 1);
  const [deal, setDeal] = useState<Deal | null>(null);
  const [selectedDate, setSelectedDate] = useState<string | null>(initialDate);
  const [quantity, setQuantity] = useState(initialQuantity);
  const [availability, setAvailability] = useState<BookingAvailability | null>(null);
  const [loading, setLoading] = useState(true);
  const [bookingInProgress, setBookingInProgress] = useState(false);
  const [bookingComplete, setBookingComplete] = useState(false);
  const [bookingError, setBookingError] = useState<string | null>(null);
  
  const navigate = useNavigate();
  
  // Fetch deal details
  useEffect(() => {
    async function fetchDeal() {
      if (!id) return;
      
      try {
        const { data, error } = await supabase
          .from('deals')
          .select(`
            *,
            businesses (
              name,
              address
            )
          `)
          .eq('id', id)
          .single();
        
        if (error) {
          console.error('Error fetching deal:', error);
          toast.error('Errore nel caricamento dell\'offerta');
          return;
        }
        
        if (data) {
          // Converti i dati dal DB al formato richiesto dall'interfaccia Deal
          const dealData: Deal = {
            id: data.id,
            title: data.title,
            description: data.description || '',
            business_id: data.business_id,
            discounted_price: data.discounted_price,
            original_price: data.original_price,
            discount_percentage: data.discount_percentage,
            start_date: data.start_date,
            end_date: data.end_date,
            time_slots: data.time_slots,
            status: data.status,
            created_at: data.created_at,
            updated_at: data.updated_at,
            images: data.images,
            auto_confirm: data.auto_confirm,
            category_id: data.category_id,
            fake: data.fake,
            terms_conditions: data.terms_conditions,
            // Campi aggiunti per compatibilità
            price: data.discounted_price,
            image_url: data.images && data.images.length > 0 ? data.images[0] : '/placeholder.svg',
            business_name: data.businesses?.name || 'Business',
            capacity: 10, // Valore di default
            businesses: data.businesses
          };
          
          setDeal(dealData);
        }
      } catch (err) {
        console.error('Error:', err);
        toast.error('Si è verificato un errore');
      } finally {
        setLoading(false);
      }
    }
    
    fetchDeal();
  }, [id]);
  
  const handleDateSelection = (date: string, avail: BookingAvailability) => {
    setSelectedDate(date);
    setAvailability(avail);
    
    // Reset quantity if it exceeds available seats
    if (quantity > avail.available) {
      setQuantity(Math.max(1, avail.available));
    }
  };
  
  const handleContinueToReview = () => {
    if (selectedDate && availability && availability.available >= quantity) {
      setStep(2);
    }
  };
  
  const handleContinueToPayment = () => {
    setStep(3);
  };
  
  const handleCompleteBooking = async () => {
    if (!id || !selectedDate || !quantity || !deal) return;
    
    setBookingInProgress(true);
    setBookingError(null);
    
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        setBookingError('Devi essere autenticato per prenotare un\'offerta');
        setBookingInProgress(false);
        return;
      }
      
      // Genera dati QR per la prenotazione
      const qrData = {
        dealId: id,
        userId: user.id,
        date: selectedDate,
        quantity: quantity,
        timestamp: new Date().toISOString()
      };
      
      // Create the booking in bookings table first
      const { data: bookingData, error: bookingError } = await supabase
        .from('bookings')
        .insert({
          deal_id: id,
          user_id: user.id,
          booking_date: selectedDate,
          booking_time: "09:00:00", // Orario predefinito
          booking_end_time: "10:00:00", // Orario predefinito
          discount_percentage: deal.discount_percentage,
          discounted_price: deal.discounted_price,
          original_price: deal.original_price,
          status: 'confirmed',
          qr_data: qrData
        })
        .select()
        .single();
        
      if (bookingError) {
        console.error('Booking error:', bookingError);
        setBookingError('Si è verificato un errore durante la prenotazione. Riprova più tardi.');
        setBookingInProgress(false);
        return;
      }
      
      // Then create the time_slot_booking entry
      if (bookingData) {
        const { error: timeSlotError } = await supabase
          .from('time_slot_bookings')
          .insert({
            booking_id: bookingData.id,
            deal_id: id,
            booking_date: selectedDate,
            day_of_week: new Date(selectedDate).getDay(),
            start_time: "09:00:00", // Orario predefinito
            end_time: "10:00:00", // Orario predefinito
            booked_seats: quantity
          });
          
        if (timeSlotError) {
          console.error('Time slot booking error:', timeSlotError);
          // Anche se questo fallisce, non è necessario fallire l'intera prenotazione
        }
      }
      
      // Prenotazione completata con successo
      setBookingComplete(true);
      setBookingInProgress(false);
     // toast.success('Prenotazione completata con successo!');
    } catch (error) {
      console.error('Booking error:', error);
      setBookingError('Si è verificato un errore durante la prenotazione. Riprova più tardi.');
      setBookingInProgress(false);
    }
  };
  
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/2 mb-6"></div>
          <div className="h-40 bg-gray-200 rounded mb-6"></div>
        </div>
      </div>
    );
  }
  
  if (!deal) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="bg-red-50 text-red-800 p-4 rounded-md">
          Offerta non trovata
        </div>
      </div>
    );
  }
  
  if (bookingComplete) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold mb-2">Booking Confirmed!</h2>
          <p className="text-gray-600 mb-6">
            Your booking for {quantity} {quantity === 1 ? 'seat' : 'seats'} on {selectedDate && formatDate(selectedDate)} has been confirmed.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-md font-medium transition-colors"
              onClick={() => navigate('/my-bookings')}
            >
              View My Bookings
            </button>
            <button
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-6 rounded-md font-medium transition-colors"
              onClick={() => navigate('/')}
            >
              Back to Home
            </button>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">Prenota la tua offerta</h1>
      
      {/* Booking progress indicator */}
      <div className="flex mb-8">
        <div className={`flex-1 text-center ${step >= 1 ? 'text-blue-600 font-medium' : 'text-gray-400'}`}>
          1. Seleziona data
        </div>
        <div className={`flex-1 text-center ${step >= 2 ? 'text-blue-600 font-medium' : 'text-gray-400'}`}>
          2. Rivedi dettagli
        </div>
        <div className={`flex-1 text-center ${step >= 3 ? 'text-blue-600 font-medium' : 'text-gray-400'}`}>
          3. Pagamento
        </div>
      </div>
      
      {step === 1 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-4">Select a Date</h2>
          <DateAvailabilitySelector 
            dealId={id || ''} 
            onSelectDate={handleDateSelection}
            initialDate={selectedDate}
          />
          
          {selectedDate && availability && (
            <div className="mt-6">
              <h3 className="font-medium mb-2">How many seats?</h3>
              <div className="flex items-center">
                <button 
                  className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  disabled={quantity <= 1}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                  </svg>
                </button>
                <span className="mx-4 text-xl font-medium">{quantity}</span>
                <button 
                  className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center"
                  onClick={() => setQuantity(Math.min(availability.available, quantity + 1))}
                  disabled={quantity >= availability.available}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>
              
              <button 
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg mt-6 font-medium transition-colors"
                onClick={handleContinueToReview}
                disabled={!selectedDate || !availability || availability.available < quantity}
              >
                Continue
              </button>
            </div>
          )}
        </div>
      )}
      
      {step === 2 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-4">Review Your Booking</h2>
          
          <div className="flex items-start mb-6">
            <img 
              src={deal.image_url} 
              alt={deal.title} 
              className="w-24 h-24 object-cover rounded-md mr-4"
            />
            <div>
              <h3 className="font-medium">{deal.title}</h3>
              <p className="text-sm text-gray-600">{deal.business_name}</p>
              <div className="mt-1">
                <span className="font-bold">${deal.price}</span>
                <span className="text-gray-500 line-through ml-2">${deal.original_price}</span>
              </div>
            </div>
          </div>
          
          <div className="border-t border-b py-4 mb-4">
            <div className="flex justify-between mb-2">
              <span>Date:</span>
              <span className="font-medium">{selectedDate && formatDate(selectedDate)}</span>
            </div>
            <div className="flex justify-between">
              <span>Quantity:</span>
              <span className="font-medium">{quantity} {quantity === 1 ? 'seat' : 'seats'}</span>
            </div>
          </div>
          
          <div className="flex justify-between mb-6">
            <span className="text-lg">Total:</span>
            <span className="text-lg font-bold">${(deal.price * quantity).toFixed(2)}</span>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <button 
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-md font-medium transition-colors"
              onClick={() => setStep(1)}
            >
              Back
            </button>
            <button 
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md font-medium transition-colors flex-1"
              onClick={handleContinueToPayment}
            >
              Continue to Payment
            </button>
          </div>
        </div>
      )}
      
      {step === 3 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-4">Payment</h2>
          
          <div className="border-b pb-4 mb-4">
            <div className="flex justify-between mb-2">
              <span>Deal:</span>
              <span className="font-medium">{deal.title}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span>Date:</span>
              <span className="font-medium">{selectedDate && formatDate(selectedDate)}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span>Quantity:</span>
              <span className="font-medium">{quantity} {quantity === 1 ? 'seat' : 'seats'}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Total:</span>
              <span className="font-bold">${(deal.price * quantity).toFixed(2)}</span>
            </div>
          </div>
          
          {/* Payment form would go here */}
          <div className="mb-6">
            <p className="text-gray-600 mb-4">
              For demo purposes, we'll simulate a successful payment without collecting actual payment details.
            </p>
            
            {bookingError && (
              <div className="bg-red-50 text-red-800 p-3 rounded-md mb-4">
                {bookingError}
              </div>
            )}
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <button 
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-md font-medium transition-colors"
              onClick={() => setStep(2)}
              disabled={bookingInProgress}
            >
              Back
            </button>
            <button 
              className={`bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md font-medium transition-colors flex-1 ${
                bookingInProgress ? 'opacity-70 cursor-not-allowed' : ''
              }`}
              onClick={handleCompleteBooking}
              disabled={bookingInProgress}
            >
              {bookingInProgress ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </span>
              ) : 'Complete Booking'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

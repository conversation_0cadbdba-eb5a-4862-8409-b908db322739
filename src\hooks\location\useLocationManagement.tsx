// src/hooks/useLocationManagement.tsx
import { useLocation, setFallbackMode } from '@/contexts/LocationContext';

export function useLocationManagement() {
  const locationState = useLocation();
  
  return {
    userLocation: locationState.coordinates,
    demoEnabled: locationState.demoEnabled,
    demoLocation: locationState.demoLocation,
    fallbackLocation: locationState.fallbackLocation,
    fallbackMode: locationState.fallbackMode,
    isLoading: locationState.isLoading,
    isPermissionDenied: locationState.isPermissionDenied,
    source: locationState.source,
    setFallbackMode
  };
}





// import { useState, useEffect } from "react";
// import { supabase } from "@/integrations/supabase/client";
// import { toast } from "sonner";

// interface LocationState {
//   userLocation: { lat: number; lng: number } | null;
//   demoEnabled: boolean;
//   demoLocation: { lat: number; lng: number };
//   fallbackLocation: { lat: number; lng: number };
//   fallbackMode: boolean;
//   isLoading: boolean;
// }

// export function useLocationManagement() {
//   const [state, setState] = useState<LocationState>({
//     userLocation: null,
//     demoEnabled: true,
//     demoLocation: { lat: 45.4666, lng: 9.1832 }, // Milano, default
//     fallbackLocation: { lat: 45.4671, lng: 9.1526 }, // Sempre Milano ma in un punto diverso
//     fallbackMode: false,
//     isLoading: true,
//   });

//   useEffect(() => {
//     const fetchGlobalSettings = async () => {
//       try {
//         const { data, error } = await supabase
//           .from("globalsetting")
//           .select("map_demo, demo_location, fallback_location")
//           .eq("id", 1)
//           .maybeSingle();

//         if (error) {
//           console.error(
//             "Errore nel recupero delle impostazioni globali:",
//             error
//           );
//           return;
//         }
    
//         // Utilizziamo i valori predefiniti se non ci sono dati
//         if (data) {
//           setState(prev => ({
//             ...prev,
//             demoEnabled: data.map_demo
//           }));

//           if (data.demo_location) {
//             try {
//               // Assicuriamoci che demo_location sia un oggetto valido
//               const demoLocationObj =
//                 typeof data.demo_location === "string"
//                   ? JSON.parse(data.demo_location)
//                   : data.demo_location;

//               setState(prev => ({
//                 ...prev,
//                 demoLocation: demoLocationObj
//               }));
//             } catch (e) {
//               console.error("Errore nel parsing di demo_location:", e);
//             }
//           }

//           if (data.fallback_location) {
//             try {
//               // Assicuriamoci che fallback_location sia un oggetto valido
//               const fallbackLocationObj =
//                 typeof data.fallback_location === "string"
//                   ? JSON.parse(data.fallback_location)
//                   : data.fallback_location;

//               setState(prev => ({
//                 ...prev,
//                 fallbackLocation: fallbackLocationObj
//               }));
//             } catch (e) {
//               console.error("Errore nel parsing di fallback_location:", e);
//             }
//           }
//         }

//         // Sempre impostiamo una posizione iniziale, indipendentemente dai dati del DB
//         const initialLocation = data?.map_demo
//           ? state.demoLocation
//           : state.fallbackLocation;
          
//         setState(prev => ({
//           ...prev, 
//           userLocation: initialLocation,
//           isLoading: false
//         }));
//       } catch (error) {
//         console.error("Errore nel processo di impostazioni globali:", error);
        
//         // In caso di errore, usiamo la posizione di fallback
//         setState(prev => ({
//           ...prev, 
//           userLocation: prev.fallbackLocation,
//           isLoading: false
//         }));
//       }
//     };

//     fetchGlobalSettings();
//   }, []);

//   // Aggiungiamo la geolocalizzazione vera se demo non è abilitato
//   useEffect(() => {
//     if (state.demoEnabled) return;
    
//     if (navigator.geolocation) {
//       navigator.geolocation.getCurrentPosition(
//         (position) => {
//           const newLocation = {
//             lat: position.coords.latitude,
//             lng: position.coords.longitude,
//           };
//           setState(prev => ({
//             ...prev,
//             userLocation: newLocation
//           }));
//         },
//         (error) => {
//           console.error("Geolocation error:", error);
//           toast.error("Impossibile ottenere la posizione attuale");
          
//           // In caso di errore, usiamo la posizione di fallback
//           setState(prev => ({
//             ...prev,
//             userLocation: prev.fallbackLocation
//           }));
//         },
//         {
//           enableHighAccuracy: true,
//           timeout: 5000,
//           maximumAge: 0,
//         }
//       );
//     }
//   }, [state.demoEnabled]);

//   const setFallbackMode = (isActive: boolean) => {
//     setState(prev => ({
//       ...prev,
//       fallbackMode: isActive
//     }));
//   };

//   return {
//     ...state,
//     setFallbackMode
//   };
// }

import React from "react";
import { Star, User } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Review } from "@/hooks/useBusinessReviews";
import { formatDate } from "@/lib/format-date";

interface ReviewCardProps {
  review: Review;
}

export const ReviewCard = ({ review }: ReviewCardProps) => {
  const getUserDisplayName = () => {
    return "Utente";
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating
                ? "fill-yellow-400 text-yellow-400"
                : "text-gray-300"
            }`}
          />
        ))}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        {/* User Info and Rating */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full">
              <User className="h-5 w-5 text-gray-600" />
            </div>
            <div>
              <p className="font-medium text-gray-900">
                {getUserDisplayName()}
              </p>
              <p className="text-sm text-gray-500">
                {formatDate(review.created_at)}
              </p>
            </div>
          </div>
          
          <div className="flex flex-col items-end gap-1">
            {renderStars(review.rating)}
            <span className="text-sm text-gray-600">
              {review.rating}/5
            </span>
          </div>
        </div>

        {/* Comment */}
        {review.comment && (
          <div className="mt-4">
            <p className="text-gray-700 leading-relaxed">
              {review.comment}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
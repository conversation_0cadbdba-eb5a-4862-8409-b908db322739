-- Check and update RLS policies for group_deal_shares and group_booking_shares
-- to allow users to delete their own shares

-- First, let's see the current policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check 
FROM pg_policies 
WHERE tablename IN ('group_deal_shares', 'group_booking_shares');

-- Update policies to allow users to delete their own shares
DROP POLICY IF EXISTS "Users can delete their own deal shares" ON group_deal_shares;
DROP POLICY IF EXISTS "Users can delete their own booking shares" ON group_booking_shares;

-- Create proper delete policies for group_deal_shares
CREATE POLICY "Users can delete their own deal shares" 
ON group_deal_shares 
FOR DELETE 
USING (auth.uid() = shared_by);

-- Create proper delete policies for group_booking_shares  
CREATE POLICY "Users can delete their own booking shares"
ON group_booking_shares
FOR DELETE
USING (auth.uid() = shared_by);

-- Also ensure users can insert their own shares
CREATE POLICY "Users can insert their own deal shares" 
ON group_deal_shares 
FOR INSERT 
WITH CHECK (auth.uid() = shared_by);

CREATE POLICY "Users can insert their own booking shares"
ON group_booking_shares
FOR INSERT
WITH CHECK (auth.uid() = shared_by);
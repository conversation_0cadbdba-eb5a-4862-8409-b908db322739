import { format, isToday, isBefore } from "date-fns";
import { it } from "date-fns/locale";

interface ButtonDateProps {
  date: Date;
  isSelected: boolean;
  today: Date;
  onSelect: (date: Date) => void;
}

const ButtonDate = ({ date, isSelected, today, onSelect }: ButtonDateProps) => {
  
  const dayName = isToday(date)
    ? "Oggi"
    : format(date, "EEE", { locale: it }).substring(0, 3);
  const dayNumber = format(date, "d");
  const isPast = isBefore(date, today) && !isToday(date);
  const monthName = format(date, "MMM", { locale: it }).substring(0, 3);

  return (
    <button 
      onClick={() => onSelect(date)}
      className="flex flex-col items-center min-w-[60px]"
    >
      <div
        className={`h-12 w-12 rounded-full ${
          isSelected ? "bg-primary" : "bg-gray-100"
        } flex flex-col items-center justify-center`}
      >
        <span className={`text-xs ${isSelected ? "text-white" : "text-gray-600"}`}>
          {dayName}
        </span>
        <span className={`text-sm ${isSelected ? "text-white" : "text-gray-600"} font-bold`}>
          {dayNumber}
        </span>
      </div>
      <span className={`text-xs mt-1 ${isSelected ? "text-brand-primary" : "text-gray-600"}`}>
        {monthName}
      </span>
    </button>
  );
};

export default ButtonDate;
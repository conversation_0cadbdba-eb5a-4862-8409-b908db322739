
import React, { useEffect, useRef, useState, useMemo } from "react";

import { useMap } from "@vis.gl/react-google-maps";
import { MarkerClusterer as GoogleMarkerClusterer, GridAlgorithm } from "@googlemaps/markerclusterer";


import { CustomBusinessMarker } from "./custom-business-marker";
import { Business, DEFAULT_ZOOM } from "./types";

/**
 * MarkerClusterer component for clustering nearby business markers
 */
export const MarkerClustererComponent = ({ 
  businesses, 
  onMarkerClick,
  isGoogleLoaded,
  onDealClick
}: { 
  businesses: Business[], 
  onMarkerClick: (business: Business) => void,
  isGoogleLoaded: boolean,
  onDealClick?: (dealId: string) => void
}) => {
  const map = useMap();
  const markersRef = useRef<google.maps.Marker[]>([]);
  const clustererRef = useRef<GoogleMarkerClusterer | null>(null);
  
  const [currentZoom, setCurrentZoom] = useState<number>(DEFAULT_ZOOM);
  
  useEffect(() => {
    if (map) {
      const updateZoom = () => {
        setCurrentZoom(map.getZoom() || DEFAULT_ZOOM);
      };
      
      map.addListener('zoom_changed', updateZoom);
      updateZoom();
      
      return () => {
        if (window.google && window.google.maps) {
          window.google.maps.event.clearListeners(map, 'zoom_changed');
        }
      };
    }
  }, [map]);
  
  const useClusteringView = useMemo(() => {
    return businesses.length >= 30 && currentZoom < 14;
  }, [businesses.length, currentZoom]);

  useEffect(() => {
    if (!useClusteringView) {
      if (clustererRef.current) {
        clustererRef.current.clearMarkers();
        clustererRef.current = null;
      }
      
      markersRef.current.forEach(marker => marker.setMap(null));
      markersRef.current = [];
      return;
    }
    
    if (clustererRef.current) {
      clustererRef.current.clearMarkers();
      clustererRef.current = null;
    }

    markersRef.current.forEach(marker => marker.setMap(null));
    markersRef.current = [];

    if (!map || !isGoogleLoaded || businesses.length === 0) return;

    const markers = businesses.map(business => {
      if (!business.latitude || !business.longitude) return null;

      const position = {
        lat: business.latitude,
        lng: business.longitude
      };

      const customMarkerSVG = `
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">
          <g transform="translate(20,20)">
            <circle cx="0" cy="0" r="12" fill="#4F46E5" />
            <circle cx="0" cy="0" r="10" fill="white" />
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" fill="none" stroke="#4F46E5" stroke-width="1.5" transform="translate(-10.5, -9)" />
            <line x1="-3" y1="9" x2="-3" y2="9" stroke="#4F46E5" stroke-width="1.5" />
            <line x1="-10.5" y1="2" x2="10.5" y2="2" stroke="#4F46E5" stroke-width="1.5" />
          </g>
        </svg>
      `;

      const marker = new google.maps.Marker({
        position,
        map,
        title: business.name,
        icon: {
          url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(customMarkerSVG),
          scaledSize: new google.maps.Size(40, 40),
          anchor: new google.maps.Point(20, 20),
        },
        zIndex: 1
      });

      marker.addListener('click', () => {
        onMarkerClick(business);
      });

      return marker;
    }).filter(Boolean) as google.maps.Marker[];

    markersRef.current = markers;

    if (markers.length > 0) {
      clustererRef.current = new GoogleMarkerClusterer({
        map,
        markers,
        algorithm: new GridAlgorithm({
          gridSize: 60,
          maxZoom: 14,
        }),
        renderer: {
          render: ({ count, position }) => {
            return new google.maps.Marker({
              position,
              label: {
                text: String(count),
                color: 'white',
                fontSize: '12px',
                fontWeight: 'bold'
              },
              icon: {
                path: google.maps.SymbolPath.CIRCLE,
                fillColor: '#4F46E5',
                fillOpacity: 0.9,
                strokeWeight: 1,
                strokeColor: '#FFF',
                scale: 20
              },
              zIndex: 1000
            });
          }
        }
      });
    }

  }, [map, businesses, isGoogleLoaded, onMarkerClick, useClusteringView]);

  return (
    <>
      {!useClusteringView && businesses.map(business => (
        <CustomBusinessMarker 
          key={business.id}
          business={business}
          onBusinessClick={onMarkerClick}
          onDealClick={onDealClick}
        />
      ))}
    </>
  );
};

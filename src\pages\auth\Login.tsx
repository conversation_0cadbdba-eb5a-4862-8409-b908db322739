import { motion } from "framer-motion";
import { <PERSON>Left, Eye, EyeOff, Loader2, Mail, Lock } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

const Login = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  useEffect(() => {
    const checkSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        navigate("/");
      }
    };
    checkSession();
  }, [navigate]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        toast.error(error.message);
      } else if (data.session) {
       // toast.success("Accesso effettuato con successo!");
        navigate("/");
      }
    } catch (error) {
      console.error("Errore di login:", error);
      toast.error("Si è verificato un errore durante l'accesso");
    } finally {
      setIsLoading(false);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-brand-light to-white px-6 py-8 flex flex-col">
      <header className="flex items-center justify-between mb-8">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => navigate("/")}
          className="p-2 hover:bg-white/50 rounded-full transition-colors"
          aria-label="Torna indietro"
        >
          <ArrowLeft className="h-6 w-6 text-gray-700" />
        </motion.button>

        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex items-center"
        >
          <div className="text-2xl font-bold bg-gradient-to-r from-brand-secondary to-brand-primary bg-clip-text text-transparent">
            CatchUp
          </div>
        </motion.div>

        <div className="w-8" />
      </header>

      <main className="flex-1 flex flex-col justify-center">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-8"
        >
          <motion.div
            variants={itemVariants}
            className="text-center space-y-2"
          >
            <h1 className="text-3xl font-bold text-gray-800">Bentornato!</h1>
            <p className="text-gray-600">Accedi per scoprire offerte locali</p>
          </motion.div>

          <motion.form
            variants={itemVariants}
            className="space-y-6"
            onSubmit={handleLogin}
          >
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-gray-700 font-medium">
                  Email
                </Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="La tua email"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                    className="pl-10 py-6 rounded-xl border-gray-200 focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20"
                    required
                    autoComplete="email"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-gray-700 font-medium">
                  Password
                </Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="La tua password"
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    className="pl-10 py-6 rounded-xl border-gray-200 focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20"
                    required
                    autoComplete="current-password"
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 -translate-y-1/2 p-2 text-gray-500 hover:text-gray-700 transition-colors"
                    onClick={() => setShowPassword(!showPassword)}
                    aria-label={showPassword ? "Nascondi password" : "Mostra password"}
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={rememberMe}
                  onChange={e => setRememberMe(e.target.checked)}
                  className="w-4 h-4 rounded border-gray-300 text-brand-primary focus:ring-brand-primary"
                />
                <span className="text-sm text-gray-600">Ricordami</span>
              </label>

              <button
                type="button"
                className="text-sm text-brand-primary hover:text-brand-secondary transition font-medium"
              >
                Password dimenticata?
              </button>
            </div>

            <motion.div variants={itemVariants}>
              <Button
                type="submit"
                className="w-full py-6 bg-gradient-to-r from-brand-secondary to-brand-primary text-white font-semibold rounded-xl hover:opacity-90 transition disabled:opacity-70 flex items-center justify-center gap-2"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span>Accesso in corso...</span>
                  </>
                ) : (
                  <span>Accedi</span>
                )}
              </Button>
            </motion.div>
          </motion.form>

          <motion.div
            variants={itemVariants}
            className="space-y-6"
          >
            <div className="flex items-center gap-4">
              <hr className="flex-1 border-gray-200" />
              <span className="text-gray-500 text-sm font-medium">oppure</span>
              <hr className="flex-1 border-gray-200" />
            </div>

            <div className="grid grid-cols-3 gap-4">
              {[
                { provider: "google", label: "Google" },
                { provider: "apple", label: "Apple" },
                { provider: "facebook", label: "Facebook" }
              ].map(({ provider, label }) => (
                <motion.button
                  key={provider}
                  whileHover={{ scale: 1.05, boxShadow: "0 4px 12px rgba(0,0,0,0.05)" }}
                  whileTap={{ scale: 0.95 }}
                  className="p-4 border border-gray-200 rounded-xl hover:bg-gray-50 transition flex items-center justify-center"
                  aria-label={`Accedi con ${label}`}
                >
                  <i className={`fa-brands fa-${provider} text-xl text-gray-700`} />
                </motion.button>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </main>

      <footer className="mt-8 text-center">
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="text-gray-600"
        >
          Non hai un account?{" "}
          <button
            onClick={() => navigate("/signup")}
            className="text-brand-primary font-semibold hover:text-brand-secondary transition"
          >
            Registrati
          </button>
        </motion.p>
      </footer>
    </div>
  );
};

export default Login;
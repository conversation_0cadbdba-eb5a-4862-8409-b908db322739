import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface ShareBookingParams {
  bookingId: string;
  groupIds: string[];
  message?: string;
}

export const useShareBooking = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ bookingId, groupIds, message }: ShareBookingParams) => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Insert shares for each selected group
      const sharesToInsert = groupIds.map(groupId => ({
        booking_id: bookingId,
        group_id: groupId,
        shared_by: user.id,
        message: message || null,
      }));

      const { data, error } = await supabase
        .from('group_booking_shares')
        .insert(sharesToInsert)
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: (data, variables) => {
      // Invalidate social feed to show new shares
      queryClient.invalidateQueries({ queryKey: ['social-feed'] });
      
      const groupCount = variables.groupIds.length;
      toast.success(
        groupCount === 1 
          ? 'Prenotazione condivisa nel gruppo!'
          : `Prenotazione condivisa in ${groupCount} gruppi!`
      );
    },
    onError: (error) => {
      console.error('Error sharing booking:', error);
      toast.error('Errore nella condivisione della prenotazione');
    },
  });
};

import { motion } from "framer-motion";
import { Mic, Volume2 } from "lucide-react";

interface VoiceFeedbackProps {
  isListening: boolean;
  isSpeaking: boolean;
}

export const VoiceFeedback = ({ isListening, isSpeaking }: VoiceFeedbackProps) => {
  return (
    <div className="relative">
      {isListening && [1, 2, 3, 4].map((i) => (
        <motion.div
          key={i}
          animate={{
            scale: [1, 2.5],
            opacity: [0.6, 0],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: i * 0.4,
            ease: "easeOut",
          }}
          className="absolute inset-0 bg-brand-primary/20 rounded-full"
          style={{
            filter: "blur(2px)",
          }}
        />
      ))}
      
      <motion.div
        animate={{
          scale: isListening ? [1, 1.1, 1] : 1,
        }}
        transition={{
          duration: 1.5,
          repeat: isListening ? Infinity : 0,
          ease: "easeInOut",
        }}
        className="relative w-24 h-24 bg-gradient-to-br from-brand-primary to-brand-secondary rounded-full flex items-center justify-center shadow-lg"
      >
        {isSpeaking ? (
          <Volume2 className="h-8 w-8 text-white" />
        ) : (
          <Mic className="h-8 w-8 text-white" />
        )}
      </motion.div>
      
      <div className="absolute inset-x-0 -bottom-8">
        <motion.div 
          className="flex justify-center space-x-1"
          animate={{
            y: isListening ? [-2, 2, -2] : 0
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={i}
              animate={{
                y: isListening ? [-8, 0, -8] : 0,
                opacity: isListening ? [0.4, 1, 0.4] : 0.4
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                delay: i * 0.1,
                ease: "easeInOut"
              }}
              className="w-1 h-6 bg-brand-primary rounded-full"
            />
          ))}
        </motion.div>
      </div>
    </div>
  );
};

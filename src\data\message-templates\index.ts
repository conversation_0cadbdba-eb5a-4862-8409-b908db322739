export const welcomeBookingMessage = ({
  businessName,
  address,
  formattedDate,
  title,
  description,
  startTime,
  discounted_price,
discount_percentage
}: {
  businessName: string;
  address: string;
  formattedDate: string;
  title:string;
  description:string;
  startTime: string;
  discounted_price: number;
  discount_percentage: number;
}
) => `# Benvenuto nella chat della tua prenotazione! 👋

**${title}** - ${description}

Qui puoi comunicare direttamente con noi per qualsiasi necessità riguardante la tua prenotazione.

** DETTAGLI DELLA PRENOTAZIONE **

**🏢 Attività:** ${businessName}

**📍 Indirizzo:** ${address || "Non specificato"}

**📅 Data:** ${formattedDate}

**⏰ Ora:** ${startTime}

${discounted_price === 0 ? "**💰 Sconto CatchUp:** " + discount_percentage + " %": "**💰 Prezzo:** € " + discounted_price.toFixed(2) + " (Sconto del " + discount_percentage + " %)"}

Per qualsiasi domanda o modifica, scrivici pure in questa chat. Saremo felici di aiutarti!`;

const bookingCancelledMessage = ({
  businessName,
  address,
  formattedDate,
  title,
  description,
  startTime,
  discounted_price,
  discount_percentage
}: {
  businessName: string;
  address: string;
  formattedDate: string;
  title: string;
  description: string;
  startTime: string;
  discounted_price: number;
  discount_percentage: number;
}) => `# Purtroppo la tua prenotazione è stata cancellata. 😔

**${title}** - ${description}

** Dettagli della prenotazione **

**🏢 Attività:** ${businessName}

**📍 Indirizzo:** ${address || "Non specificato"}

**📅 Data:** ${formattedDate}

**⏰ Ora:** ${startTime}

${discounted_price === 0 ? "**💰 Sconto CatchUp:** " + discount_percentage + " %": "**💰 Prezzo:** € " + discounted_price.toFixed(2) + " (Sconto del " + discount_percentage + " %)"}

Se hai bisogno di ulteriori informazioni, non esitare a contattarci. Siamo qui per aiutarti!`;

export const welcomeBusinessChatMessage = ({
  businessName
}: {
  businessName: string;

}) => `# Benvenuto nella chat con ${businessName}! 👋

Siamo qui per risponderti al più presto! 🙌`;

// export const bookingMessages = {
//   welcomeBooking: bookingWelcomeMessage,
//   cancelledBooking: bookingCancelledMessage,
// };

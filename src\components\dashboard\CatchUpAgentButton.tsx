
import { MessageCir<PERSON>, <PERSON><PERSON> } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

const CatchUpAgentButton = () => {
  const navigate = useNavigate();

  return (
    <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl p-4 mb-6 text-white">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-white/20 rounded-full">
            <Bot className="h-6 w-6" />
          </div>
          <div>
            <h3 className="font-semibold text-lg">Assistente CatchUp</h3>
            <p className="text-white/80 text-sm">
              Trova le migliori offerte con l'aiuto dell'AI
            </p>
          </div>
        </div>
        <Button
          onClick={() => navigate("/chat-catchup")}
          variant="secondary"
          size="sm"
          className="bg-white text-purple-600 hover:bg-white/90"
        >
          <MessageCircle className="h-4 w-4 mr-2" />
          Chatta
        </Button>
      </div>
    </div>
  );
};

export default CatchUpAgentButton;

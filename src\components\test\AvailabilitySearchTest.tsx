import { useState, useCallback } from "react";
import {  BusinessAvailability, useAvailabilitySearch, DaySchedule } from "@/hooks/useAvailabilitySearch";

// Override the type to match the actual data structure
interface ActualBusinessAvailability extends Omit<BusinessAvailability, 'available_slots'> {
  available_slots: {
    schedule: DaySchedule[];
    exceptions: any[];
  };
}

const AvailabilitySearchTest = () => {
  
  const [selectedLocation, setSelectedLocation] = useState("Milano");
  const [searchParams, setSearchParams] = useState({
    date: new Date().toISOString().split("T")[0],
    time: "14:00",
    location: {
      lat: 45.4642,
      lng: 9.19,
      radius: 5000,
    },
  });

  const {
    results,
    isLoading,
    isLoadingMore,
    error,
    params,
    metrics,
    hasMore,
    updateSearchParams,
    loadMore,
    refresh,
  } = useAvailabilitySearch(searchParams);

  const handleChangeLocation = useCallback(
    (city: "Milano" | "Roma" | "Napoli" | "Bonaduz") => {
      const locations = {
        Milano: { lat: 45.4642, lng: 9.19 },
        Roma: { lat: 41.9028, lng: 12.4964 },
        Napoli: { lat: 40.8518, lng: 14.2681 },
        Bonaduz: {lat:46.810076, lng:9.396332}
      };
      setSelectedLocation(city);
      updateSearchParams({
        location: {
          ...locations[city],
          radius: params.location?.radius || 5000,
        },
      });
    },
    [updateSearchParams, params.location]
  );

  const handleChangeRadius = useCallback(
    (radius: number) => {
      if (!params.location) return;
      updateSearchParams({
        location: {
          lat: params.location.lat,
          lng: params.location.lng,
          radius,
        },
      });
    },
    [updateSearchParams, params.location]
  );

  return (
    <div className="p-4 bg-white rounded-xl shadow">
      <h2 className="text-xl font-bold mb-4">Test Ricerca Disponibilità</h2>
      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium mb-1">Data:</label>
          <input
            type="date"
            value={params.date}
            onChange={(e) => updateSearchParams({ date: e.target.value })}
            className="w-full p-2 border rounded"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Ora:</label>
          <input
            type="time"
            value={params.time || ""}
            onChange={(e) => updateSearchParams({ time: e.target.value })}
            className="w-full p-2 border rounded"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Città:</label>
          <div className="flex space-x-2">
            <button
              onClick={() => handleChangeLocation("Milano")}
              className={`px-3 py-1 rounded ${
                selectedLocation === "Milano"
                  ? "bg-blue-500 text-white"
                  : "bg-blue-100 hover:bg-blue-200"
              }`}
            >
              
              Milano
            </button>
            <button
              onClick={() => handleChangeLocation("Roma")}
              className={`px-3 py-1 rounded ${
                selectedLocation === "Roma"
                  ? "bg-blue-500 text-white"
                  : "bg-blue-100 hover:bg-blue-200"
              }`}
            >
              Roma
            </button>
            <button
              onClick={() => handleChangeLocation("Napoli")}
              className={`px-3 py-1 rounded ${
                selectedLocation === "Napoli"
                  ? "bg-blue-500 text-white"
                  : "bg-blue-100 hover:bg-blue-200"
              }`}
            >
              Napoli
            </button>
            <button
              onClick={() => handleChangeLocation("Bonaduz")}
              className={`px-3 py-1 rounded ${
                selectedLocation === "Napoli"
                  ? "bg-blue-500 text-white"
                  : "bg-blue-100 hover:bg-blue-200"
              }`}
            >
              Bonaduz
            </button>
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">
            Raggio di ricerca:
          </label>
          <div className="flex space-x-2">
            {[1000, 2000, 5000, 10000].map((radius) => (
              <button
                key={radius}
                onClick={() => handleChangeRadius(radius)}
                className={`px-3 py-1 rounded ${
                  params.location?.radius === radius
                    ? "bg-blue-500 text-white"
                    : "bg-blue-100 hover:bg-blue-200"
                }`}
              >
                {radius / 1000}km
              </button>
            ))}
          </div>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={refresh}
            className="flex-1 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            disabled={isLoading}
          >
            {isLoading ? "Ricerca in corso..." : "Aggiorna ricerca"}
          </button>
        </div>
      </div>
      <div className="mb-4 p-2 bg-gray-100 rounded">
        <h3 className="font-medium">Metriche:</h3>
        <div className="text-sm">
          <div>Tempo query: {metrics.queryTime.toFixed(2)}ms</div>
          <div>Totale risultati: {metrics.totalResults}</div>
          <div>Pagina: {metrics.pageNumber}</div>
          <div>Origine dati: {metrics.isCached ? "Cache" : "Database"}</div>
        </div>
      </div>
      <div className="mb-4">
        <h3 className="font-medium">Stato:</h3>
        {isLoading && (
          <div className="text-blue-600">Caricamento in corso...</div>
        )}
        {error && <div className="text-red-600">Errore: {error}</div>}
        {!isLoading && !error && <div className="text-green-600">Pronto</div>}
      </div>
      <div>
        <h3 className="font-medium mb-2">
          Risultati ({results?.length || 0}):
        </h3>
        {results?.length > 0 ? (
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {results.map((result, index) => {
              // Cast to actual structure
              const typedResult = result as unknown as ActualBusinessAvailability;
              
              return (
                <div key={index} className="border p-2 rounded">
                  <div className="font-medium">{typedResult.business_name}</div>
                  <div className="text-xs mt-1">
                    <strong>Slot disponibili:</strong>
                    {typedResult.available_slots?.schedule && 
                     Array.isArray(typedResult.available_slots.schedule) &&
                     typedResult.available_slots.schedule.length > 0 ? (
                      <ul className="mt-1 pl-2 space-y-0.5 list-disc list-inside">
                        {typedResult.available_slots.schedule.map((day) => (
                          <li key={`day-${day.day}`} className="text-green-800">
                            {day.day_name} <span className="text-gray-600">({day.time_slots?.length || 0} slot)</span>
                            {day.time_slots && day.time_slots.length > 0 && (
                              <ul className="pl-4 mt-0.5 space-y-0.5 list-none">
                                {day.time_slots.map((slot, slotIdx) => (
                                  <li key={`slot-${day.day}-${slotIdx}`} className="text-xs text-gray-700">
                                    {slot.start_time} - {slot.end_time} 
                                    <span className="text-gray-500 ml-1">
                                      (Disponibili: {slot.available_seats})
                                    </span>
                                  </li>
                                ))}
                              </ul>
                            )}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <div className="mt-1 text-gray-400">Nessuno slot disponibile</div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-gray-500">Nessun risultato trovato</div>
        )}
        {hasMore && !isLoading && (
          <button
            onClick={loadMore}
            className="mt-4 w-full py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
            disabled={isLoadingMore}
          >
            {isLoadingMore ? "Caricamento..." : "Carica altri risultati"}
          </button>
        )}
      </div>
      <div className="mt-4">
        <details>
          <summary className="cursor-pointer text-blue-600">
            Debug: Parametri di ricerca
          </summary>
          <pre className="text-xs mt-2 bg-gray-100 p-2 rounded overflow-x-auto">
            {JSON.stringify(params, null, 2)}
          </pre>
        </details>
      </div>
    </div>
  );
};

export default AvailabilitySearchTest;
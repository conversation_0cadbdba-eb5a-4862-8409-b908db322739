
import { motion } from "framer-motion";
import { Search, Mic, Menu } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";

import type { Database } from "@/integrations/supabase/types";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import DealCard from "@/components/deals/core/DealCard";

type Deal = Database['public']['Tables']['deals']['Row'] & {
  businesses?: {
    name: string;
    address: string;
  };
};

const LandingPage = () => {
  const navigate = useNavigate();
  const [randomDeals, setRandomDeals] = useState<Deal[]>([]);

  useEffect(() => {
    const fetchRandomDeals = async () => {
      try {
        const { data, error } = await supabase
          .from('deals')
          .select(`
            *,
            businesses (
              name,
              address
            )
          `)
          .limit(10); // Prendiamo 10 offerte per avere più varietà nella selezione random

        if (error) {
          console.error("Errore nel caricamento delle offerte:", error);
          return;
        }

        if (data && data.length > 0) {
          // Mescoliamo l'array e prendiamo le prime 2 offerte
          const shuffled = [...data].sort(() => 0.5 - Math.random());
          setRandomDeals(shuffled.slice(0, 2));
        }
      } catch (error) {
        console.error("Si è verificato un errore:", error);
      }
    };

    fetchRandomDeals();
  }, []);

  const handleStartOnboarding = () => {
    navigate('/onboarding');
  };

  return (
    <div className="min-h-screen bg-white">
      <header className="fixed top-0 left-0 right-0 bg-white/80 backdrop-blur-md shadow-sm z-50">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between px-4 py-3">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="text-2xl font-bold bg-gradient-to-r from-brand-secondary to-brand-primary bg-clip-text text-transparent"
            >
              CatchUp
            </motion.div>
            <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
              <Menu className="w-6 h-6 text-gray-600" />
            </button>
          </div>
        </div>
      </header>

      <main className="pt-16">
        <section className="px-4 py-8 bg-gradient-to-b from-brand-light to-white">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-6"
          >
            <h1 className="text-2xl font-bold mb-2">Scopri Offerte Locali</h1>
            <p className="text-gray-600">Risparmia con offerte esclusive nella tua zona</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="relative bg-white rounded-full shadow-lg p-4 flex items-center mb-8 transition-shadow hover:shadow-xl"
          >
            <Search className="w-5 h-5 text-brand-primary" />
            <input
              type="text"
              placeholder="Cerca offerte vicino a te..."
              className="w-full ml-3 outline-none"
            />
            <button className="ml-2 text-brand-primary hover:text-brand-secondary transition-colors">
              <Mic className="w-5 h-5" />
            </button>
          </motion.div>
        </section>

        <section className="px-4 py-6">
          <h2 className="text-xl font-semibold mb-4">Offerte in Evidenza</h2>
          <div className="relative">
            <Carousel
              opts={{
                align: "start",
                loop: true,
              }}
              className="w-full"
            >
              <CarouselContent className="-ml-2 md:-ml-4">
                {randomDeals.map((deal) => (
                  <CarouselItem key={deal.id} className="pl-2 md:pl-4 md:basis-1/2">
                    <DealCard 
                      deal={deal} 
                      variant="compact"
                      showVisitBusiness={true}
                    />
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
          </div>
        </section>

        <section className="px-4 py-8 bg-gradient-to-t from-brand-light to-white">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-4">Inizia a Risparmiare Oggi</h2>
            <div className="space-y-3">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => navigate('/signup')}
                className="w-full bg-brand-primary text-white rounded-full py-3 font-semibold transition-colors hover:bg-brand-secondary"
              >
                Registrati
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => navigate('/login')}
                className="w-full border-2 border-brand-primary text-brand-primary rounded-full py-3 font-semibold transition-colors hover:bg-brand-light"
              >
                Accedi
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleStartOnboarding}
                className="w-full border-2 border-brand-secondary text-brand-secondary rounded-full py-3 font-semibold transition-colors hover:bg-brand-light"
              >
                Scopri CatchUp
              </motion.button>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default LandingPage;

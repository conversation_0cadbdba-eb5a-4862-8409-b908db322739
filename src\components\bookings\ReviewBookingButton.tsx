import React, { useState } from "react";
import { Star, MessageSquare } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useCanReviewBooking, useUserBookingReview } from "@/hooks/useBusinessReviews";
import { ReviewForm } from "@/components/reviews/ReviewForm";
import type { Booking } from "@/types/booking";

interface ReviewBookingButtonProps {
  booking: Booking;
}

export const ReviewBookingButton = ({ booking }: ReviewBookingButtonProps) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const { data: canReview, isLoading: checkingPermission } = useCanReviewBooking(booking.id);
  const { data: existingReview, isLoading: loadingReview } = useUserBookingReview(booking.id);

  if (checkingPermission || loadingReview) {
    return null;
  }

  // Se l'utente non può recensire (booking non completato o non suo) non mostrare il pulsante
  if (!canReview) {
    return null;
  }

  // Se esiste già una recensione, mostra lo stato
  if (existingReview) {
    return (
      <div className="flex items-center gap-1 text-sm text-green-600">
        <Star className="h-4 w-4 fill-current" />
        <span>Recensito</span>
      </div>
    );
  }

  // Ottieni il business_id dal booking
  const businessId = booking.deals?.business_id;
  const businessName = booking.deals?.businesses?.name || "questa attività";

  if (!businessId) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-1">
          <MessageSquare className="h-4 w-4" />
          Recensisci
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Recensisci la tua esperienza</DialogTitle>
        </DialogHeader>
        
        <ReviewForm
          businessId={businessId}
          bookingId={booking.id}
          businessName={businessName}
          onSuccess={() => {
            setIsOpen(false);
          }}
        />
      </DialogContent>
    </Dialog>
  );
};
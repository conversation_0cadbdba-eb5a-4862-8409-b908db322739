
import { useNavigate } from "react-router-dom";

interface DealFooterProps {
  onSubmit: (e: React.FormEvent) => void;
  isSubmitting: boolean;
  submitText?: string;
}

 const   DealFooter = ({ onSubmit, isSubmitting, submitText }: DealFooterProps) => {
  const navigate = useNavigate();
  
  return (
    <footer className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 p-4 max-w-md mx-auto">
      <div className="flex gap-4">
        <button
          type="button"
          onClick={() => navigate(-1)}
          className="flex-1 px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200"
        >
          Annulla
        </button>
        <button
          onClick={onSubmit}
          disabled={isSubmitting}
          className="flex-1 px-6 py-3 bg-gradient-to-r from-brand-primary to-brand-primary/90 text-white rounded-xl hover:from-brand-primary/90 hover:to-brand-primary/80 disabled:opacity-50"
        >
          {isSubmitting ? "Pubblicazione..." : submitText || "Pubblica"}
        </button>
      </div>
    </footer>
  );
};

export default DealFooter;


import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

/**
 * Hook per ottenere la versione corrente dell'applicazione dal database.
 * Utilizza React Query per caching e gestione dello stato.
 */
export const useAppVersion = () => {
  return useQuery({
    queryKey: ["appVersion"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("app_version")
        .select("major, minor, patch")
        .order("id", { ascending: false })
        .limit(1)
        .single();

      if (error) {
        console.error("Errore nel recupero della versione:", error);
        throw new Error(error.message);
      }

      if (!data) {
        return null;
      }

      return `${data.major}.${data.minor}.${data.patch}`;
    },
    staleTime: 1000 * 60 * 60, // 1 ora
    refetchOnWindowFocus: false,
  });
};

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { Group, GroupWithMemberCount } from "@/types/groups";

export const useGroups = () => {
  return useQuery({
    queryKey: ["groups"],
    queryFn: async (): Promise<GroupWithMemberCount[]> => {
      // First, get all groups the user is a member of or created
      const { data: groups, error: groupsError } = await supabase
        .from("groups")
        .select("*")
        .order("created_at", { ascending: false });

      if (groupsError) throw groupsError;

      if (!groups || groups.length === 0) {
        return [];
      }

      // Get member counts for each group
      const groupIds = groups.map((group) => group.id);
      const { data: memberCounts, error: memberCountsError } = await supabase
        .from("group_members")
        .select("group_id")
        .in("group_id", groupIds);

      if (memberCountsError) throw memberCountsError;

      // Get user roles for each group
      const { data: userRoles, error: userRolesError } = await supabase
        .from("group_members")
        .select("group_id, role")
        .in("group_id", groupIds)
        .eq("user_id", (await supabase.auth.getUser()).data.user?.id || "");

      if (userRolesError) throw userRolesError;

      // Get pending invites count for each group
      const { data: pendingInvites, error: pendingInvitesError } =
        await supabase
          .from("group_invites")
          .select("group_id")
          .in("group_id", groupIds)
          .eq("status", "pending");

      if (pendingInvitesError) throw pendingInvitesError;

      // Count members per group
      const memberCountMap =
        memberCounts?.reduce((acc, member) => {
          acc[member.group_id] = (acc[member.group_id] || 0) + 1;
          return acc;
        }, {} as Record<string, number>) || {};

      // Map user roles
      const userRoleMap =
        userRoles?.reduce((acc, userRole) => {
          acc[userRole.group_id] = userRole.role as "admin" | "member";
          return acc;
        }, {} as Record<string, "admin" | "member">) || {};

      // Count pending invites per group
      const pendingInvitesCountMap =
        pendingInvites?.reduce((acc, invite) => {
          acc[invite.group_id] = (acc[invite.group_id] || 0) + 1;
          return acc;
        }, {} as Record<string, number>) || {};

      return groups.map((group) => ({
        ...group,
        member_count: memberCountMap[group.id] || 0,
        user_role: userRoleMap[group.id] || "member",
        pending_invites_count: pendingInvitesCountMap[group.id] || 0,
      }));
    },
  });
};

export const useCreateGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (groupData: {
      name: string;
      description?: string;
      avatar_url?: string;
    }) => {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      const { data, error } = await supabase
        .from("groups")
        .insert([
          {
            ...groupData,
            created_by: user.id,
          },
        ])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["groups"] });
      toast.success("Gruppo creato con successo!");
    },
    onError: (error) => {
      toast.error("Errore durante la creazione del gruppo");
      console.error("Error creating group:", error);
    },
  });
};

export const useUpdateGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      ...updateData
    }: Partial<Group> & { id: string }) => {
      const { data, error } = await supabase
        .from("groups")
        .update(updateData)
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["groups"] });
      toast.success("Gruppo aggiornato con successo!");
    },
    onError: (error) => {
      toast.error("Errore durante l'aggiornamento del gruppo");
      console.error("Error updating group:", error);
    },
  });
};

export const useDeleteGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (groupId: string) => {
      // search for group invites for the group
      const { data: groupInvites, error: groupInvitesError } = await supabase
        .from("group_invites")
        .select("*")
        .eq("group_id", groupId);

      if (groupInvitesError) throw groupInvitesError;

      if (groupInvites && groupInvites.length > 0) {
        // delete notifications for group invites
        const { error: deleteNotificationsError } = await supabase
          .from("notifications")
          .delete()
          .eq("entity", "group_invites")
          .in(
            "entity_id",
            groupInvites.map((invite) => invite.id)
          );
        if (deleteNotificationsError) throw deleteNotificationsError;
      }
      // delete group
      const { error } = await supabase
        .from("groups")
        .delete()
        .eq("id", groupId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["groups"] });
      // TODO: check invalidate queries for group-invites and group-details and others
      // queryClient.invalidateQueries({ queryKey: ['group-invites'] });
      // queryClient.invalidateQueries({ queryKey: ['group-details'] });

      toast.success("Gruppo eliminato con successo!");
    },
    onError: (error) => {
      toast.error("Errore durante l'eliminazione del gruppo");
      console.error("Error deleting group:", error);
    },
  });
};

export const useLeaveGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (groupId: string) => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error("User not authenticated");
      }

      const { error } = await supabase
        .from("group_members")
        .delete()
        .eq("group_id", groupId)
        .eq("user_id", user.id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["groups"] });
      queryClient.invalidateQueries({ queryKey: ["group-details"] });
      toast.success("Hai lasciato il gruppo con successo!");
    },
    onError: (error) => {
      toast.error("Errore nell'abbandonare il gruppo");
      console.error("Error leaving group:", error);
    },
  });
};

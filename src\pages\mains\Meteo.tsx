import { useWeather } from '@/hooks/weather/useWeather';
import { WeatherCard } from '@/components/weather/WeatherCard';
import { WeatherForecast } from '@/components/weather/WeatherForecast';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON>ert<PERSON>riangle, MapPin, Loader2, RefreshCw, Sun } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

export default function Meteo() {
  const { 
    weatherData, 
    alerts, 
    isLoading, 
    error, 
    lastUpdated, 
    refreshWeather, 
    hasLocation 
  } = useWeather();

  // Loading state
  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <h2 className="text-xl font-semibold mb-2">Caricamento Meteo</h2>
            <p className="text-muted-foreground">
              Ottenimento dati meteorologici...
            </p>
          </div>
        </div>
      </div>
    );
  }

  // No location available
  if (!hasLocation) {
    return (
      <div className="max-w-6xl mx-auto space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <MapPin className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h2 className="text-xl font-semibold mb-2">Posizione Non Disponibile</h2>
              <p className="text-muted-foreground mb-4">
                Per visualizzare i dati meteo, abilita la geolocalizzazione nelle impostazioni del browser.
              </p>
              <Button onClick={refreshWeather} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Riprova
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="max-w-6xl mx-auto space-y-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Errore Meteo</AlertTitle>
          <AlertDescription className="mt-2">
            {error}
            <Button
              onClick={refreshWeather}
              variant="outline"
              size="sm"
              className="ml-4"
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Riprova
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Main weather display
  if (!weatherData) {
    return (
      <div className="max-w-6xl mx-auto space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Nessun Dato Meteo</h2>
              <p className="text-muted-foreground mb-4">
                Impossibile ottenere i dati meteorologici per la tua posizione.
              </p>
              <Button onClick={refreshWeather}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Ricarica
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      <div className="flex-1 max-w-4xl mx-auto w-full">
        <div className="space-y-4">
        {/* Page Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
            ☀️ Meteo
          </h1>
          <p className="text-muted-foreground text-lg">
            Condizioni meteorologiche attuali e previsioni dettagliate
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full mx-auto mt-4"></div>
        </div>

        {/* Weather Alerts */}
        {alerts.length > 0 && (
          <div className="space-y-3">
            {alerts.map((alert, index) => (
              <Alert 
                key={index} 
                variant={alert.severity === 'severe' || alert.severity === 'extreme' ? 'destructive' : 'default'}
                className="border-0 shadow-lg backdrop-blur-sm"
              >
                <AlertTriangle className="h-5 w-5" />
                <AlertTitle className="text-lg">{alert.title}</AlertTitle>
                <AlertDescription className="text-base">{alert.description}</AlertDescription>
              </Alert>
            ))}
          </div>
        )}

        {/* Current Weather */}
        <WeatherCard
          weatherData={weatherData}
          onRefresh={refreshWeather}
          isLoading={isLoading}
          lastUpdated={lastUpdated}
        />

        {/* 5-Day Forecast */}
        {weatherData.forecast && weatherData.forecast.length > 0 && (
          <WeatherForecast forecast={weatherData.forecast} />
        )}

        {/* Hourly Forecast */}
        {weatherData.hourly && weatherData.hourly.length > 0 && (
          <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50">
            <CardContent className="p-6">
              <h3 className="text-xl font-semibold mb-6 flex items-center gap-3">
                <div className="bg-indigo-100 rounded-full p-2">
                  <RefreshCw className="h-5 w-5 text-indigo-600" />
                </div>
                Previsioni Orarie
              </h3>
              <div className="flex gap-3 overflow-x-auto pb-4 scrollbar-hide">
                {weatherData.hourly.slice(0, 12).map((hour, index) => (
                  <div
                    key={index}
                    className="flex-none bg-white rounded-xl p-3 shadow-sm border border-gray-200 hover:shadow-md transition-shadow min-w-[90px] text-center"
                  >
                    <div className="text-xs font-medium text-gray-600 mb-2">
                      {new Date(hour.time).toLocaleTimeString('it-IT', { 
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                    
                    <div className="flex justify-center mb-2">
                      <img
                        src={`https://openweathermap.org/img/wn/${hour.icon}@2x.png`}
                        alt={hour.condition}
                        className="h-10 w-10"
                      />
                    </div>
                    
                    <div className="text-sm font-bold text-gray-900 mb-1">
                      {Math.round(hour.temp)}°C
                    </div>
                    
                    {hour.precipitation > 0 && (
                      <div className="text-xs text-blue-600 bg-blue-50 rounded-full px-1 py-0.5">
                        💧 {hour.precipitation.toFixed(0)}%
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Fun Weather Facts */}
        <Card className="shadow-lg border-0 bg-gradient-to-br from-green-50 to-emerald-50 mb-8">
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-3">
              <div className="bg-green-100 rounded-full p-2">
                <Sun className="h-5 w-5 text-green-600" />
              </div>
              Consigli Meteo
            </h3>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-2xl">🌡️</span>
                  <span className="font-semibold">Temperatura</span>
                </div>
                <p className="text-sm text-gray-600">
                  {weatherData.current.temp > 25 
                    ? "Giornata calda! Ricordati di idratarti spesso." 
                    : weatherData.current.temp < 10 
                    ? "Fa freddo! Vestiti a strati e copriti bene."
                    : "Temperatura ideale per stare all'aperto."}
                </p>
              </div>
              
              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-2xl">💨</span>
                  <span className="font-semibold">Vento</span>
                </div>
                <p className="text-sm text-gray-600">
                  {weatherData.current.windSpeed > 10 
                    ? "Vento forte! Fai attenzione se sei in bicicletta."
                    : "Condizioni ventose ideali per una passeggiata."}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        </div>
      </div>
    </div>
  );
}
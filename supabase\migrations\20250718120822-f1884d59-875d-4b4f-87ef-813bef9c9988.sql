-- Create table for tracking user location history
CREATE TABLE public.user_location_history (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  latitude DOUBLE PRECISION NOT NULL,
  longitude <PERSON>O<PERSON><PERSON><PERSON> PRECISION NOT NULL,
  recorded_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  source TEXT DEFAULT 'automatic',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.user_location_history ENABLE ROW LEVEL SECURITY;

-- Create policies for user access
CREATE POLICY "Users can view their own location history" 
ON public.user_location_history 
FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own location history" 
ON public.user_location_history 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Create index for better performance
CREATE INDEX idx_user_location_history_user_id ON public.user_location_history(user_id);
CREATE INDEX idx_user_location_history_recorded_at ON public.user_location_history(recorded_at DESC);
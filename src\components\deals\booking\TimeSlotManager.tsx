import { useState, useEffect, useCallback } from "react";
import { Plus, Trash2, Clock, Calendar, Copy, Check } from "lucide-react";
import { DaySchedule, TimeSlot, WeeklySchedule } from "@/types/deals";
import { But<PERSON> } from "@/components/ui/button";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";

import useEmblaCarousel from "embla-carousel-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";


/**
 * TimeSlotManager Component
 *
 * A component for managing weekly time slots and exception dates.
 * Optimized for mobile view with a carousel for day selection.
 *
 * @param {WeeklySchedule} value - The current schedule data
 * @param {Function} onChange - Callback function when schedule changes
 */
interface TimeSlotManagerProps {
  value: WeeklySchedule;
  onChange: (schedule: WeeklySchedule) => void;
}

 const TimeSlotManager = ({ value, onChange }: TimeSlotManagerProps) => {
  const [newStartTime, setNewStartTime] = useState("");
  const [newEndTime, setNewEndTime] = useState("");
  const [newSeats, setNewSeats] = useState("");
  const [selectedDay, setSelectedDay] = useState<number>(1);
  const [newException, setNewException] = useState("");
  const [copyDialogOpen, setCopyDialogOpen] = useState(false);
  const [selectedDays, setSelectedDays] = useState<number[]>([]);

  // Create embla carousel ref to access API
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "center",
    loop: true,
    slidesToScroll: 1,
    containScroll: "trimSnaps",
    dragFree: false,
  });

  // Update selected day when carousel slide changes
  const onCarouselSelect = useCallback(() => {
    if (!emblaApi) return;

    // Get the current slide index
    const selectedIndex = emblaApi.selectedScrollSnap();
    // Get the day at that index (adding 1 because our days are 1-indexed)
    const newSelectedDay = value.schedule[selectedIndex]?.day || 1;

    // Update selected day if it's different
    if (newSelectedDay !== selectedDay) {
      setSelectedDay(newSelectedDay);
    }
  }, [emblaApi, selectedDay, value.schedule]);

  // Set up carousel event listeners
  useEffect(() => {
    if (!emblaApi) return;

    // Add event listener for slide selection
    emblaApi.on("select", onCarouselSelect);

    // Clean up event listener on unmount
    return () => {
      emblaApi.off("select", onCarouselSelect);
    };
  }, [emblaApi, onCarouselSelect]);

  // Programmatically scroll to the selected day when it changes via button click
  useEffect(() => {
    if (!emblaApi) return;

    // Find the index of the selected day
    const dayIndex = value.schedule.findIndex((day) => day.day === selectedDay);
    if (dayIndex !== -1) {
      emblaApi.scrollTo(dayIndex);
    }
  }, [selectedDay, emblaApi, value.schedule]);

  const handleAddTimeSlot = () => {
    if (!newStartTime || !newEndTime) return;

    const seatsValue = parseInt(newSeats);
    if (isNaN(seatsValue) || seatsValue < 1) return;

    const updatedSchedule = value.schedule.map((daySchedule) => {
      if (daySchedule.day === selectedDay) {
        return {
          ...daySchedule,
          time_slots: [
            ...daySchedule.time_slots,
            {
              start_time: newStartTime,
              end_time: newEndTime,
              available_seats: seatsValue,
           
            },
          ],
        };
      }
      return daySchedule;
    });

    onChange({
      ...value,
      schedule: updatedSchedule,
    });

    setNewStartTime("");
    setNewEndTime("");
    setNewSeats("");
  };

  const handleRemoveTimeSlot = (dayIndex: number, slotIndex: number) => {
    const updatedSchedule = value.schedule.map((daySchedule, idx) => {
      if (idx === dayIndex) {
        return {
          ...daySchedule,
          time_slots: daySchedule.time_slots.filter((_, i) => i !== slotIndex),
        };
      }
      return daySchedule;
    });

    onChange({
      ...value,
      schedule: updatedSchedule,
    });
  };

  const handleAddException = () => {
    if (!newException) return;

    onChange({
      ...value,
      exceptions: [...value.exceptions, newException],
    });

    setNewException("");
  };

  const handleRemoveException = (index: number) => {
    onChange({
      ...value,
      exceptions: value.exceptions.filter((_, i) => i !== index),
    });
  };

  const handleCopyTimeSlots = () => {
    // Se non ci sono giorni selezionati o il giorno corrente non ha fasce orarie, non fare nulla
    if (selectedDays.length === 0) {
      setCopyDialogOpen(false);
      return;
    }

    // Trova le fasce orarie del giorno selezionato
    const sourceDay = value.schedule.find((day) => day.day === selectedDay);
    if (!sourceDay || sourceDay.time_slots.length === 0) {
      setCopyDialogOpen(false);
      return;
    }

    // Copia le fasce orarie nei giorni selezionati
    const updatedSchedule = value.schedule.map((day) => {
      if (selectedDays.includes(day.day) && day.day !== selectedDay) {
        return {
          ...day,
          time_slots: [...sourceDay.time_slots],
        };
      }
      return day;
    });

    onChange({
      ...value,
      schedule: updatedSchedule,
    });

    // Chiudi il dialog e resetta i giorni selezionati
    setCopyDialogOpen(false);
    setSelectedDays([]);
  };

  const toggleDaySelection = (day: number) => {
    setSelectedDays((prev) =>
      prev.includes(day) ? prev.filter((d) => d !== day) : [...prev, day]
    );
  };

  // Get the selected day's time slots
  const selectedDayTimeSlots =
    value.schedule.find((d) => d.day === selectedDay)?.time_slots || [];
  const hasTimeSlots = selectedDayTimeSlots.length > 0;

  return (
    <div className="space-y-6">
      {/* Weekly Schedule Section */}
      <Card className="overflow-hidden">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-bold">Orari Settimanali</CardTitle>
          <CardDescription className="text-sm">
            Seleziona un giorno e aggiungi gli orari disponibili
          </CardDescription>
        </CardHeader>

        <CardContent className="px-0 sm:px-6">
          {/* Day Selection Carousel */}
          <div className="mb-4">
            <div className="overflow-hidden" ref={emblaRef}>
              <div className="flex">
                {value.schedule.map((day) => {
                  const hasTimeSlots = day.time_slots.length > 0;
                  return (
                    <div
                      key={day.day}
                      className="flex-[0_0_100%] flex justify-center items-center py-2"
                    >
                      <div className="w-[220px] max-w-[80%]">
                        <button
                          type="button"
                          onClick={() => setSelectedDay(day.day)}
                          className={`
                            relative w-full px-4 py-4 rounded-2xl text-sm font-medium transition-all duration-200
                            ${
                              selectedDay === day.day
                                ? "bg-brand-primary text-white shadow-md"
                                : hasTimeSlots
                                ? "bg-green-50 border-2 border-green-200 text-green-700 hover:bg-green-100"
                                : "bg-gray-50 border border-gray-200 text-gray-600 hover:bg-gray-100"
                            }
                          `}
                        >
                          <span className="block text-sm font-bold truncate">
                            {day.day_name}
                          </span>
                          {hasTimeSlots && (
                            <div className="absolute top-2 right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs">
                              {day.time_slots.length}
                            </div>
                          )}
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Selected Day Info */}
          <div className="mb-4 px-6">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-md font-medium text-gray-700">
                {value.schedule.find((d) => d.day === selectedDay)?.day_name}
              </h4>

              {/* Pulsante copia fasce orarie */}
              {hasTimeSlots && (
                <Dialog open={copyDialogOpen} onOpenChange={setCopyDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1 text-xs"
                    >
                      <Copy className="h-3.5 w-3.5 mr-1" />
                      Copia orari
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-md bg-white !bg-opacity-100 border border-gray-300 rounded-lg">
                    <DialogHeader>
                      <DialogTitle>Copia fasce orarie</DialogTitle>
                      <DialogDescription>
                        Seleziona i giorni in cui vuoi copiare le fasce orarie
                        di{" "}
                        {
                          value.schedule.find((d) => d.day === selectedDay)
                            ?.day_name
                        }
                      </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-3 mt-4">
                      {value.schedule
                        .filter((day) => day.day !== selectedDay)
                        .map((day) => (
                          <div
                            key={day.day}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`day-${day.day}`}
                              checked={selectedDays.includes(day.day)}
                              onCheckedChange={() =>
                                toggleDaySelection(day.day)
                              }
                            />
                            <label
                              htmlFor={`day-${day.day}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {day.day_name}
                              {day.time_slots.length > 0 && (
                                <span className="text-xs text-amber-600 ml-2">
                                  (Sostituirà {day.time_slots.length} fasce
                                  orarie esistenti)
                                </span>
                              )}
                            </label>
                          </div>
                        ))}
                    </div>

                    <DialogFooter className="mt-4">
                      <Button
                        type="button"
                        onClick={() => setCopyDialogOpen(false)}
                        variant="outline"
                      >
                        Annulla
                      </Button>
                      <Button
                        type="button"
                        onClick={handleCopyTimeSlots}
                        variant="default"
                        className="ml-2"
                        disabled={selectedDays.length === 0}
                      >
                        <Check className="h-4 w-4 mr-2" />
                        Conferma
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              )}
            </div>

            {/* Time Slot Input */}
            <div className="flex gap-2 items-center mb-4">
              <div className="flex-1 space-y-1">
                <label
                  htmlFor="start-time"
                  className="text-xs text-gray-500 font-medium"
                >
                  Ora inizio
                </label>
                <Input
                  id="start_time"
                  type="time"
                  value={newStartTime}
                  onChange={(e) => setNewStartTime(e.target.value)}
                  className="border-gray-300 focus:border-brand-primary"
                />
              </div>
              <div className="flex-1 space-y-1">
                <label
                  htmlFor="end-time"
                  className="text-xs text-gray-500 font-medium"
                >
                  Ora fine
                </label>
                <Input
                  id="end_time"
                  type="time"
                  value={newEndTime}
                  onChange={(e) => setNewEndTime(e.target.value)}
                  className="border-gray-300 focus:border-brand-primary"
                />
              </div>
              <div className="w-[45px] space-y-1">
                <label
                  htmlFor="seats"
                  className="text-xs text-gray-500 font-medium"
                >
                  Posti
                </label>
                <Input
                  id="seats"
                  type="number"
                  min={0}
                  value={newSeats}
                  onChange={(e) => setNewSeats(e.target.value)}
                  className="border-gray-300 focus:border-brand-primary w-[45px]"
                />
              </div>
              <div className="pt-6 ml-1">
                <Button
                  type="button"
                  onClick={handleAddTimeSlot}
                  variant="secondary"
                  size="sm"
                  className="h-10"
                  disabled={!newStartTime || !newEndTime}
                >
                  <Plus className="h-5 w-5" />
                  <span className="sr-only">Aggiungi</span>
                </Button>
              </div>
            </div>
          </div>

          {/* Time Slots List */}
          <div className="px-6">
            {selectedDayTimeSlots.length > 0 ? (
              <div className="space-y-2 max-h-60 overflow-y-auto pr-2">
                {selectedDayTimeSlots.map((slot, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-3 bg-gray-50 p-3 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors"
                  >
                    <div className="bg-brand-primary/10 p-2 rounded-full">
                      <Clock className="h-4 w-4 text-brand-primary" />
                    </div>
                    <div className="flex-1">
                       <span className="font-medium block">
                         {slot.start_time} - {slot.end_time}
                       </span>
                       <span className="text-xs text-gray-500">
                         Posti: {slot.available_seats}
                       </span>
                     </div>
                    <Button
                      type="button"
                      onClick={() =>
                        handleRemoveTimeSlot(selectedDay - 1, index)
                      }
                      variant="ghost"
                      size="sm"
                      className="text-red-500 hover:bg-red-50 hover:text-red-600 p-2 h-8 w-8"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 bg-gray-50 rounded-lg border border-dashed border-gray-200">
                <Clock className="h-6 w-6 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500 text-sm">
                  Nessun orario impostato per questo giorno
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Exceptions Section */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-bold">Date di Chiusura</CardTitle>
          <CardDescription className="text-sm">
            Aggiungi le date in cui non sarai disponibile
          </CardDescription>
        </CardHeader>

        <CardContent>
          <div className="flex gap-2 items-end mb-4">
            <div className="flex-1 space-y-1">
              <label
                htmlFor="exception-date"
                className="text-xs text-gray-500 font-medium"
              >
                Data di chiusura
              </label>
              <div className="relative">
                <Input
                  id="exception-date"
                  type="date"
                  value={newException}
                  onChange={(e) => setNewException(e.target.value)}
                  className="border-gray-300 focus:border-brand-primary pl-10"
                />
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>
            <Button
              type="button"
              onClick={handleAddException}
              variant="secondary"
              size="sm"
              className="h-10"
              disabled={!newException}
            >
              <Plus className="h-5 w-5" />
              <span className="sr-only">Aggiungi</span>
            </Button>
          </div>

          {/* Exceptions List */}
          {value.exceptions.length > 0 ? (
            <div className="space-y-2 max-h-60 overflow-y-auto pr-2">
              {value.exceptions.map((date, index) => (
                <div
                  key={index}
                  className="flex items-center gap-3 bg-gray-50 p-3 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors"
                >
                  <div className="bg-red-50 p-2 rounded-full">
                    <Calendar className="h-4 w-4 text-red-500" />
                  </div>
                  <span className="flex-1 font-medium">
                    {format(new Date(date), "EEEE d MMMM yyyy", { locale: it })}
                  </span>
                  <Button
                    type="button"
                    onClick={() => handleRemoveException(index)}
                    variant="ghost"
                    size="sm"
                    className="text-red-500 hover:bg-red-50 hover:text-red-600 p-2 h-8 w-8"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 bg-gray-50 rounded-lg border border-dashed border-gray-200">
              <Calendar className="h-6 w-6 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500 text-sm">
                Nessuna data di chiusura impostata
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TimeSlotManager;

-- Create group_invites table
CREATE TABLE IF NOT EXISTS group_invites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    invited_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    invited_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(group_id, invited_user_id)
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_group_invites_group_id ON group_invites(group_id);
CREATE INDEX IF NOT EXISTS idx_group_invites_invited_user_id ON group_invites(invited_user_id);
CREATE INDEX IF NOT EXISTS idx_group_invites_status ON group_invites(status);

-- Add RLS policies
ALTER TABLE group_invites ENABLE ROW LEVEL SECURITY;

-- Policy for viewing invites (users can see invites they sent or received)
CREATE POLICY "Users can view their own invites" ON group_invites
    FOR SELECT USING (
        invited_user_id = auth.uid() OR 
        invited_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM group_members 
            WHERE group_id = group_invites.group_id 
            AND user_id = auth.uid() 
            AND role = 'admin'
        )
    );

-- Policy for creating invites (only group admins)
CREATE POLICY "Group admins can create invites" ON group_invites
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM group_members 
            WHERE group_id = group_invites.group_id 
            AND user_id = auth.uid() 
            AND role = 'admin'
        )
    );

-- Policy for updating invites (invited users can update their own invites)
CREATE POLICY "Users can update their own invites" ON group_invites
    FOR UPDATE USING (invited_user_id = auth.uid());

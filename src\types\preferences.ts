
export interface UserPreference {
  categoryId: string | null;
  categoryName: string | null;
  businessId: string | null;
  businessName: string | null;
  priceRange: {
    min: number;
    max: number;
  } | null;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  } | null;
  score: number; // 0-100, più alto = preferenza più forte
}

export interface RecommendedDeal {
  id: string;
  title: string;
  discountedPrice: number;
  originalPrice: number;
  discountPercentage: number;
  businessName: string;
  images: string[];
  matchScore: number; // 0-100, percentuale di corrispondenza con le preferenze
  categoryName: string | null;
  endDate: string;
}

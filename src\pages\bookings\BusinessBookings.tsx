
import { useParams, useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import BusinessBookingList from "@/components/booking/BusinessBookingList";
import { useBusinessBookings } from "@/hooks/useBusinessBookings";

const BusinessBookings = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const {
    bookings,
    isLoading,
    selectedBookingId,
    setSelectedBookingId,
    isCancelling,
    handleApprove,
    handleCancel
  } = useBusinessBookings(id);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="h-16 bg-gray-200 animate-pulse" />
        <div className="p-4 space-y-4">
          {[1, 2, 3].map(item => (
            <div key={item} className="h-24 bg-gray-200 rounded-xl animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <header className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50 px-4 py-3">
        <div className="flex items-center gap-3">
          <button 
            onClick={() => navigate(-1)}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-700" />
          </button>
          <h1 className="font-semibold text-gray-800">Prenotazioni</h1>
        </div>
      </header>
      
      <main className="p-2 space-y-2 my-[59px]">
        {bookings.length > 0 ? (
          <BusinessBookingList
            bookings={bookings}
            businessId={id || ''}
            selectedBookingId={selectedBookingId}
            setSelectedBookingId={setSelectedBookingId}
            onApprove={handleApprove}
            onCancel={handleCancel}
            isCancelling={isCancelling}
          />
        ) : (
          <div className="text-center py-12 text-gray-500">
            <p>Nessuna prenotazione disponibile</p>
          </div>
        )}
      </main>
    </div>
  );
};

export default BusinessBookings;

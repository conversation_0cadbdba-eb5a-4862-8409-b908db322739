import { Info, Calendar, MessageCircle, PhoneCall } from "lucide-react";
import { format, parseISO } from "date-fns";
import { it } from "date-fns/locale";
import { DAYS_OF_WEEK } from "@/data/daysNames";
import type { WeeklySchedule } from "@/types/deals";


interface DealAvailabilityProps {
  timeSlots: WeeklySchedule | null;
  startDate: string;
  endDate: string;
  
}

const DealAvailability = ({
  timeSlots,
  startDate,
  endDate,
  
}: DealAvailabilityProps) => {
  
  
  // Estrai i giorni unici dal nuovo formato timeSlots
  const availableDays = Array.from(
    new Set(
      timeSlots?.schedule?.flatMap((day) => {
        if (day.time_slots.length > 0) {
          return [day.day];
        }
        return [];
      }) || []
    )
  );

  return (
    <div className="bg-gray-50 p-4 rounded-lg mb-6">
      <h3 className="text-sm font-semibold mb-4">Disponibilità</h3>

      <div className="flex flex-wrap gap-1 mb-4">
        {DAYS_OF_WEEK.filter((day) => availableDays.includes(day.value)).map(
          (day) => (
            <span
              key={day.value}
              className="px-2 py-1 rounded-md text-xs bg-brand-primary/10 text-brand-primary"
            >
              {day.label}
            </span>
          )
        )}
      </div>

      <ul className="text-sm text-gray-600 space-y-2">
        <li className="flex items-start">
          <Info className="h-4 w-4 text-gray-400 mt-1 mr-2" />
          <span>
            Valido dal{" "}
            {format(parseISO(startDate), "d MMMM yyyy", { locale: it })} al{" "}
            {format(parseISO(endDate), "d MMMM yyyy", { locale: it })}
          </span>
        </li>
        <li className="flex items-start">
          <Calendar className="h-4 w-4 text-gray-400 mt-1 mr-2" />
          <span>Prenotazione obbligatoria</span>
        </li>

        {/*   <li className="mt-3">

   
  
       

        <button
            onClick={handleMessage}
            className="flex items-center w-full text-left text-brand-primary hover:text-brand-primary/80 transition-colors group bg-brand-primary/5 p-2 rounded-lg"
          >
            <MessageCircle className="h-4 w-4 mr-2 shrink-0" />
            <span className="font-medium">
              Hai domande? Chatta con il gestore
            </span>
          </button> 
        </li> */}
      </ul>
    </div>
  );
};

export default DealAvailability;

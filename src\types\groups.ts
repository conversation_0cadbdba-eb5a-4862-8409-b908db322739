
export interface Group {
  id: string;
  name: string;
  description?: string;
  avatar_url?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface GroupMember {
  id: string;
  group_id: string;
  user_id: string;
  role: 'admin' | 'member';
  joined_at: string;
}

export interface GroupDealShare {
  id: string;
  group_id: string;
  deal_id: string;
  shared_by: string;
  message?: string;
  created_at: string;
}

export interface GroupBookingShare {
  id: string;
  group_id: string;
  booking_id: string;
  shared_by: string;
  message?: string;
  created_at: string;
}

export interface GroupWithMemberCount extends Group {
  member_count: number;
  user_role?: 'admin' | 'member';
  pending_invites_count?: number;
}

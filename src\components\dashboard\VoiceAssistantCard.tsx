
import React from 'react';
import { Mic, Mail, Calendar, Search, Bookmark } from 'lucide-react';
import { motion } from 'framer-motion';

interface QuickAction {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
}

const VoiceAssistantCard = () => {
  const userName = 'Alex'; // In una versione reale, questo verrebbe preso dal contesto dell'utente
  
  const quickActions: QuickAction[] = [
    {
      icon: <Mail className="h-6 w-6 text-blue-500" />,
      label: 'Emails',
      onClick: () => console.log('Emails clicked'),
    },
    {
      icon: <Calendar className="h-6 w-6 text-green-500" />,
      label: 'Calendar',
      onClick: () => console.log('Calendar clicked'),
    },
    {
      icon: <Search className="h-6 w-6 text-purple-500" />,
      label: 'Research',
      onClick: () => console.log('Research clicked'),
    },
    {
      icon: <Bookmark className="h-6 w-6 text-orange-500" />,
      label: 'Booking',
      onClick: () => console.log('Booking clicked'),
    },
  ];

  const handleMicClick = () => {
    console.log('Microphone clicked');
    // Qui andrebbe la logica per attivare l'assistente vocale
  };

  return (
    <div className="px-4 pt-4 pb-2">
      <div className="mb-4">
        <h1 className="text-3xl font-bold text-gray-800">Ciao, {userName}</h1>
        <p className="text-gray-600">Come posso aiutarti oggi?</p>
      </div>

      <motion.div 
        className="bg-indigo-500 rounded-xl p-4 text-white mb-6"
        whileHover={{ scale: 1.02 }}
        transition={{ type: "spring", stiffness: 300 }}
      >
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-xl font-medium">Assistente Vocale</h2>
          <span className="bg-indigo-400 bg-opacity-70 px-3 py-1 rounded-full text-sm">
            Attivo
          </span>
        </div>
        <p className="mb-4 text-indigo-100">Tocca il microfono per parlare</p>
        <div className="flex justify-center">
          <motion.button
            className="bg-white rounded-full p-4 shadow-lg"
            whileTap={{ scale: 0.95 }}
            onClick={handleMicClick}
          >
            <Mic className="h-6 w-6 text-indigo-500" />
          </motion.button>
        </div>
      </motion.div>

      <div>
        <h2 className="text-xl font-bold text-gray-800 mb-4">Azioni Rapide</h2>
        <div className="grid grid-cols-4 gap-2">
          {quickActions.map((action, index) => (
            <motion.div
              key={index}
              className="flex flex-col items-center"
              whileHover={{ y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.button
                onClick={action.onClick}
                className="w-14 h-14 rounded-full flex items-center justify-center mb-1"
                style={{ 
                  backgroundColor: index === 0 ? '#EBF5FF' : 
                                   index === 1 ? '#E6FAF0' :
                                   index === 2 ? '#F3EEFF' : '#FEF3E7'
                }}
              >
                {action.icon}
              </motion.button>
              <span className="text-sm text-gray-700">{action.label}</span>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default VoiceAssistantCard;

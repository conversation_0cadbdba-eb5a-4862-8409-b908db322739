
interface DealImagesProps {
  images: File[];
  existingImages?: string[];
  onImagesChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onImageRemove: (index: number) => void;
  onExistingImageRemove?: (index: number) => void;
}
 const DealImages = ({ 
  images, 
  existingImages, 
  onImagesChange, 
  onImageRemove,
  onExistingImageRemove 
}: DealImagesProps) => {
  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-1">
        Foto dell'offerta ({existingImages?.length || 0 + images.length}/5)
      </label>
      <div 
        onClick={() => document.getElementById('image-upload')?.click()}
        className="border border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-brand-primary/50 relative"
      >
        <input
          id="image-upload"
          type="file"
          multiple
          accept="image/*"
          className="hidden"
          onChange={onImagesChange}
        />
        <div className="flex items-center justify-center border-2 border-dashed border-gray-200 rounded-lg p-6">
          <div className="text-center">
            <div className="mb-2">
              <svg 
                className="mx-auto h-8 w-8 text-gray-400" 
                xmlns="http://www.w3.org/2000/svg" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2"
              >
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                <polyline points="17 8 12 3 7 8" />
                <line x1="12" y1="3" x2="12" y2="15" />
              </svg>
            </div>
            <p className="text-sm text-gray-600 font-medium">
              Clicca per caricare le foto
            </p>
            <p className="text-xs text-gray-400 mt-1">
              Formato: JPG, PNG. Max 5MB per foto
            </p>
          </div>
        </div>
      </div>

      {/* Immagini esistenti */}
      {existingImages && existingImages.length > 0 && (
        <div className="mt-4">
          <div className="flex gap-2 flex-wrap">
            {existingImages.map((url, index) => (
              <div key={index} className="relative">
                <img
                  src={url}
                  alt={`Immagine esistente ${index + 1}`}
                  className="w-20 h-20 object-cover rounded-lg border border-gray-200"
                />
                {onExistingImageRemove && (
                  <button
                    type="button"
                    onClick={() => onExistingImageRemove(index)}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-sm shadow-md"
                  >
                    ×
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Nuove immagini */}
      {images.length > 0 && (
        <div className="mt-4">
          <div className="flex gap-2 flex-wrap">
            {images.map((file, index) => (
              <div key={index} className="relative">
                <img
                  src={URL.createObjectURL(file)}
                  alt={`Nuova immagine ${index + 1}`}
                  className="w-20 h-20 object-cover rounded-lg border border-gray-200"
                />
                <button
                  type="button"
                  onClick={() => onImageRemove(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-sm shadow-md"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DealImages;
import { Share } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ShareButtonProps {
  onClick: (e?: React.MouseEvent) => void;
  className?: string;
  size?: "sm" | "default" | "lg" | "icon";
  variant?: "default" | "outline" | "ghost" | "link" | "destructive" | "secondary";
  disabled?: boolean;
  showText?: boolean;
}

export const ShareButton = ({ 
  onClick, 
  className, 
  size = "sm", 
  variant = "outline",
  disabled = false,
  showText = false
}: ShareButtonProps) => {
  return (
    <Button
      onClick={onClick}
      size={size}
      variant={variant}
      disabled={disabled}
      className={cn("flex items-center gap-2", className)}
    >
      <Share className="h-4 w-4" />
      {showText && <span>Condividi</span>}
    </Button>
  );
};
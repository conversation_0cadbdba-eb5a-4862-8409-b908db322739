
import { useMemo, useState } from "react";
import { daysNames } from "@/data/daysNames";
import type { WeeklySchedule, DaySchedule, TimeSlot } from "@/types/deals";
import type { Database } from "@/integrations/supabase/types";
import { formatTime } from "@/utils/dateUtils";
import { availabilityService } from "@/services/availabilityService";
import { parse, format } from "date-fns";

type JsonTimeSlots = Database['public']['Tables']['deals']['Row']['time_slots'];

/**
 * Hook per gestire le fasce orarie delle offerte in CatchUp
 * 
 * @param timeSlots JSON con le fasce orarie
 * @returns Oggetto con le funzioni utili per gestire le fasce orarie
 */
export const useTimeSlots = (timeSlots: JsonTimeSlots) => {
  // Parse time_slots data safely
  const parsedTimeSlots = useMemo<WeeklySchedule>(() => {
    if (!timeSlots) return { schedule: [], exceptions: [] };
    
    if (typeof timeSlots === 'string') {
      try {
        return JSON.parse(timeSlots);
      } catch (e) {
        console.error('Error parsing time_slots:', e);
        return { schedule: [], exceptions: [] };
      }
    }
    
    return timeSlots as WeeklySchedule;
  }, [timeSlots]);
  
  // Trova i giorni che hanno slot orari disponibili
  const daysWithSlots = useMemo(() => {
    if (!parsedTimeSlots?.schedule) return [];
    
    return parsedTimeSlots.schedule
      .filter(day => day.time_slots.length > 0)
      .map(day => day.day);
  }, [parsedTimeSlots]);
  
  // Trova il primo giorno disponibile
  const firstAvailableDay = useMemo(() => {
    if (!parsedTimeSlots?.schedule) return null;
    
    const dayWithSlots = parsedTimeSlots.schedule.find(day => day.time_slots.length > 0);
    if (!dayWithSlots) return null;
    
    return dayWithSlots.day;
  }, [parsedTimeSlots]);
  
  // Trova gli slot orari per un giorno specifico
  const getTimeSlotsForDay = (day: number) => {
    if (!parsedTimeSlots?.schedule) return [];
    
    const daySchedule = parsedTimeSlots.schedule.find(d => d.day === day);
    if (!daySchedule) return [];
    
    const timeSlotsByDay = daySchedule.time_slots;
    return timeSlotsByDay.map(slot => ({
      ...slot,
      formattedTime: `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`
    }));
  };
  
  // Calcola il totale dei posti disponibili per tutti gli slot
  const getTotalAvailableSeats = useMemo(() => {
    if (!parsedTimeSlots?.schedule) return 0;

    let totalSeats = 0;
    
    parsedTimeSlots.schedule.forEach(day => {
      day.time_slots.forEach(slot => {
        // Use the availabilityService to calculate remaining seats
        // Since we don't have booking data here, we're just using the total available seats
        // In a real implementation, you would need to fetch the bookings for each slot
        totalSeats += slot.available_seats || 0;
      });
    });

    return totalSeats;
  }, [parsedTimeSlots]);
  
  // Calcola il totale dei posti per tutti gli slot
  const getTotalSeats = useMemo(() => {
    if (!parsedTimeSlots?.schedule) return 0;

    let totalSeats = 0;
    
    parsedTimeSlots.schedule.forEach(day => {
      day.time_slots.forEach(slot => {
        totalSeats += slot.available_seats;
      });
    });

    return totalSeats;
  }, [parsedTimeSlots]);
  
  // Verifica se ci sono slot orari disponibili
  const hasTimeSlots = useMemo(() => {
    if (!parsedTimeSlots?.schedule) return false;
    
    return parsedTimeSlots.schedule.some(day => day.time_slots.length > 0);
  }, [parsedTimeSlots]);
  
  // Ottiene i posti disponibili per uno slot orario specifico
  const getAvailableSeatsForTimeSlot = (day: number, startTime: string): number => {
    if (!parsedTimeSlots?.schedule) return 0;
    
    const daySchedule = parsedTimeSlots.schedule.find(d => d.day === day);
    if (!daySchedule) return 0;
    
    const timeSlot = daySchedule.time_slots.find(slot => slot.start_time === startTime);
    if (!timeSlot) return 0;
    
    // In a real implementation, you would need to fetch the bookings for this slot
    // and use availabilityService.calculateRemainingSeats
    return Math.max(0, timeSlot.available_seats  || 0);
  };
  
  // Formatta il range orario (es. "10:00 - 12:00")
  const formatTimeRange = useMemo(() => {
    if (!parsedTimeSlots?.schedule) return null;
    
    // Troviamo il primo giorno con time slots
    const dayWithSlots = parsedTimeSlots.schedule.find(day => day.time_slots.length > 0);
    if (!dayWithSlots) return null;
    
    // Se ci sono più fasce orarie nello stesso giorno, mostriamo la prima e l'ultima
    const timeSlotsByDay = dayWithSlots.time_slots;
    
    if (timeSlotsByDay.length === 0) return null;
   
    const firstSlot = timeSlotsByDay[0];
    const lastSlot = timeSlotsByDay[timeSlotsByDay.length - 1];

    // Se c'è solo un time slot, mostra solo quello
    if (timeSlotsByDay.length === 1) {
      return `${firstSlot.start_time} - ${firstSlot.end_time}`;
    }

    // Altrimenti mostra primo e ultimo orario per dare un'idea del range completo
    return `${firstSlot.start_time} - ${lastSlot.end_time}`;
  }, [parsedTimeSlots]);

  // Calcola il testo dei giorni disponibili (es. "Ogni lunedì, martedì e mercoledì")
  const getAvailableDaysText = useMemo(() => {
    if (!daysWithSlots.length) return null;
    
    if (daysWithSlots.length === 1) {
      return `Solo il ${daysNames[daysWithSlots[0]]}`;
    }

    const formattedDays = daysWithSlots.map(day => daysNames[day as keyof typeof daysNames]);
    if (daysWithSlots.length === 2) {
      return `Ogni ${formattedDays.join(' e ')}`;
    }
    
    const lastDay = formattedDays.pop();
    return `Ogni ${formattedDays.join(', ')} e ${lastDay}`;
  }, [daysWithSlots]);

  // Verifica se ci sono posti disponibili
  const hasAvailableSeats = useMemo(() => {
    return getTotalAvailableSeats > 0;
  }, [getTotalAvailableSeats]);

  // Testo informativo sui posti disponibili
  const getAvailableSeatsText = useMemo(() => {
    const totalSeats = getTotalAvailableSeats;
    
    if (totalSeats === 0) return "Posti esauriti";
    if (totalSeats === 1) return "Ultimo posto disponibile";
    
    return `${totalSeats} posti disponibili`;
  }, [getTotalAvailableSeats]);

  // Calcola la percentuale di posti ancora disponibili (per visualizzazioni grafiche)
  const getAvailabilityPercentage = useMemo(() => {
    if (!parsedTimeSlots?.schedule) return 0;
    
    let totalSeats = 0;
    let bookedSeats = 0;
    
    parsedTimeSlots.schedule.forEach(day => {
      day.time_slots.forEach(slot => {
        totalSeats += slot.available_seats;
        bookedSeats += slot.booked_seats || 0;
      });
    });
    
    if (totalSeats === 0) return 0;
    return Math.round(((totalSeats - bookedSeats) / totalSeats) * 100);
  }, [parsedTimeSlots]);

  // Verifica se la disponibilità è bassa (meno del 20%)
  const isLowAvailability = useMemo(() => {
    return getAvailabilityPercentage < 20 && getAvailabilityPercentage > 0;
  }, [getAvailabilityPercentage]);

  // NUOVA FUNZIONE: Ottiene le informazioni per uno slot orario specifico basato sulla data e sull'ora
  const getSpecificTimeSlotInfo = (dateStr: string, timeStr: string) => {
    if (!parsedTimeSlots?.schedule) return null;
    
    try {
      // Convertiamo la data in numero del giorno della settimana (1-7, dove 1 è lunedì)
      const date = new Date(dateStr);
      let dayOfWeek = date.getDay(); // 0-6, dove 0 è domenica
      
      // Convertiamo al formato 1-7 dove 1 è lunedì (formato usato nel nostro database)
      dayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
      
      // Cerchiamo gli slot per quel giorno
      const daySchedule = parsedTimeSlots.schedule.find(d => d.day === dayOfWeek);
      if (!daySchedule) return null;
      
      // Troviamo lo slot orario che include l'ora specificata
      // Convertiamo l'ora in formato 24 ore in minuti per confronto più semplice
      const [hour, minute] = timeStr.split(':').map(Number);
      const requestedTimeInMinutes = hour * 60 + (minute || 0);
      
      for (const slot of daySchedule.time_slots) {
        const [startHour, startMinute] = slot.start_time.split(':').map(Number);
        const [endHour, endMinute] = slot.end_time.split(':').map(Number);
        
        const startTimeInMinutes = startHour * 60 + startMinute;
        const endTimeInMinutes = endHour * 60 + endMinute;
        
        // Verifichiamo se l'ora richiesta è all'interno di questo slot
        if (requestedTimeInMinutes >= startTimeInMinutes && requestedTimeInMinutes <= endTimeInMinutes) {
          // Restituiamo le informazioni dello slot che include l'ora richiesta
          return {
            start_time: slot.start_time,
            end_time: slot.end_time,
            available_seats: slot.available_seats,
            day: dayOfWeek,
            day_name: daysNames[dayOfWeek as keyof typeof daysNames]
          };
        }
      }
      
      // Se non troviamo uno slot che include l'ora specifica, restituiamo null
      return null;
    } catch (error) {
      console.error('Errore nel calcolo delle informazioni dello slot orario:', error);
      return null;
    }
  };

  return {
    parsedTimeSlots,
    daysWithSlots,
    firstAvailableDay,
    getTimeSlotsForDay,
    getTotalAvailableSeats,
    getTotalSeats,
    hasTimeSlots,
    getAvailableSeatsForTimeSlot,
    formatTimeRange,
    getAvailableDaysText,
    hasAvailableSeats,
    getAvailableSeatsText,
    getAvailabilityPercentage,
    isLowAvailability,
    getSpecificTimeSlotInfo
  };
};

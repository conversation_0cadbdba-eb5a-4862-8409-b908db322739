import {
    Toolt<PERSON>,
    Too<PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Toolt<PERSON>Trigger,
  } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils";


interface Props {
    children: React.ReactNode;
    text: string;
    side?: "top" | "right" | "bottom" | "left";
    align?: "start" | "center" | "end";
    className?: string;
}

const Hint = ({children,text,side="top",align="center",className}:Props) => {
    return (
        <TooltipProvider>
            <Tooltip delayDuration={100}>
                <TooltipTrigger asChild>
                    {children}
                </TooltipTrigger>
                <TooltipContent side={side} align={align} className={cn(className)}>
                    {text}
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    )
}

export default Hint;

// "bg-popover text-popover-foreground border-border"
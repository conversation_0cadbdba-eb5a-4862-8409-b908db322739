import React from "react";

interface Business {
  id: string;
  name: string;
  address?: string;
  [key: string]: any; // Allow for other properties
}

interface BusinessSelectionListProps {
  businesses: Business[];
  selectedBusinesses: string[];
  onToggleBusiness: (businessId: string) => void;
  onSelectAll: () => void;
  title?: string;
  selectAllText?: string;
  deselectAllText?: string;
  idPrefix?: string;
}

/**
 * A reusable component for selecting businesses from a list with checkboxes.
 * 
 * @param businesses - Array of business objects to display
 * @param selectedBusinesses - Array of selected business IDs
 * @param onToggleBusiness - Function to call when a business is toggled
 * @param onSelectAll - Function to call when "Select All" button is clicked
 * @param title - Title text to display above the list (optional)
 * @param selectAllText - Text to display for the "Select All" button
 * @param deselectAllText - Text to display for the "Deselect All" button
 * @param idPrefix - Prefix for the checkbox IDs to ensure uniqueness
 */
const BusinessSelectionList: React.FC<BusinessSelectionListProps> = ({
  businesses,
  selectedBusinesses,
  onToggleBusiness,
  onSelectAll,
  title,
  selectAllText = "Seleziona tutte",
  deselectAllText = "Deseleziona tutte",
  idPrefix = "business"
}) => {
  return (
    <div>
      {title && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {title.includes(")")
            ? title.replace(')', ` / ${businesses.length} totali)`)
            : `${title} (${selectedBusinesses.length} / ${businesses.length} totali)`}
        </label>
      )}
      <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-2 bg-white">
        {businesses.length === 0 ? (
          <p className="text-gray-500 text-sm py-2 text-center">
            Nessuna attività trovata
          </p>
        ) : (
          <>
            <div className="flex items-center py-1 mb-2 border-b border-gray-200">
              <button
                onClick={onSelectAll}
                className="w-full text-left text-sm font-medium text-brand-primary hover:text-brand-primary-dark"
              >
                {selectedBusinesses.length === businesses.length
                  ? deselectAllText
                  : selectAllText}
              </button>
            </div>
            {businesses.map((business) => (
              <div
                key={business.id}
                className="flex items-center py-1"
              >
                <input
                  type="checkbox"
                  id={`${idPrefix}-${business.id}`}
                  checked={selectedBusinesses.includes(business.id)}
                  onChange={() => onToggleBusiness(business.id)}
                  className="mr-2 h-4 w-4 rounded border-gray-300 text-brand-primary focus:ring-brand-primary"
                />
                <label
                  htmlFor={`${idPrefix}-${business.id}`}
                  className="text-sm text-gray-700 cursor-pointer flex-1 overflow-hidden overflow-ellipsis whitespace-nowrap"
                >
                  {business.name}{" "}
                  {business.address ? `(${business.address} - ${business.city})` : ""}
                </label>
              </div>
            ))}
          </>
        )}
      </div>
    </div>
  );
};

export default BusinessSelectionList;

import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/auth/useAuth";
import { useState, useEffect } from "react";
import { Users } from "lucide-react";
import { availabilityService } from "@/services/availabilityService";

interface DealBookingFooterProps {
  originalPrice: number;
  discountedPrice: number;
  discount: number;
  selectedDate: string | null;
  selectedTime: string | null;
  endTime: string | null;
  selectedDay: number;
  dealId: string;
  hasTimeSlots: boolean;
  businessId: string;
}

const DealBookingFooter = ({
  originalPrice,
  discountedPrice,
  discount,
  selectedDate,
  selectedTime,
  endTime,
  selectedDay,
  dealId,
  hasTimeSlots,
  businessId,
}: DealBookingFooterProps) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isOwner, setIsOwner] = useState(false);
  const [availableSeats, setAvailableSeats] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Check if the current user is the owner of the business
  useEffect(() => {
    const checkOwnership = async () => {
      if (!user || !businessId) return;

      try {
        const { data } = await supabase
          .from("businesses")
          .select("owner_id")
          .eq("id", businessId)
          .single();

        if (data && data.owner_id === user.id) {
          setIsOwner(true);
        }
      } catch (error) {
        console.error("Error checking business ownership:", error);
      }
    };

    checkOwnership();
  }, [user, businessId]);

  // Controlla la disponibilità di posti quando cambia la data o l'orario selezionato
  useEffect(() => {
    const checkAvailability = async () => {
      if (!dealId || !selectedDate || !selectedTime) {
        setAvailableSeats(null);
        return;
      }

      setIsLoading(true);
      try {
        // Fetch time slot details from the deal
        const { data: dealData, error } = await supabase
          .from("deals")
          .select("time_slots")
          .eq("id", dealId)
          .single();

        if (error || !dealData) {
          console.error("Error fetching deal time slots:", error);
          return;
        }

        // Parse time_slots data safely
        const timeSlots = dealData.time_slots;
        let parsedTimeSlots;

        if (typeof timeSlots === "string") {
          parsedTimeSlots = JSON.parse(timeSlots);
        } else {
          parsedTimeSlots = timeSlots;
        }

        // Get the day of week from the selected date
        const dayOfWeek = new Date(selectedDate).getDay() || 7; // Converte 0 (domenica) in 7 per compatibilità

        if (parsedTimeSlots && parsedTimeSlots.schedule) {
          // Find the specific time slot
          const daySchedule = parsedTimeSlots.schedule.find(
            (day: any) => day.day === dayOfWeek
          );
          const timeSlot = daySchedule?.time_slots.find(
            (slot: any) => slot.start_time === selectedTime
          );

          if (timeSlot) {
            // Get the total available seats for this time slot
            const totalSeats = timeSlot.available_seats || 0;

            // Get bookings for this date and time slot
            const bookings = await availabilityService.getTimeSlotBookings(
              dealId,
              selectedDate
            );

            // Get booked seats for this time slot (add ":00" to match the database format)
            const bookedSeats = bookings[selectedTime + ":00"] || 0;

            // Calculate remaining seats
            const remainingSeats = availabilityService.calculateRemainingSeats(
              totalSeats,
              bookedSeats
            );

            setAvailableSeats(remainingSeats);
          } else {
            setAvailableSeats(null);
          }
        }
      } catch (error) {
        console.error("Error checking availability:", error);
      } finally {
        setIsLoading(false);
      }
    };
    console.log("Checking availability...");
    checkAvailability();
  }, [dealId, selectedDate, selectedTime]);

  const handleBooking = () => {
    if (isOwner) {
      toast.error("Non puoi prenotare un'offerta della tua attività");
      return;
    }

    if (!selectedDate) {
      toast.error("Seleziona una data per procedere");
      return;
    }

    if (hasTimeSlots && !selectedTime) {
      toast.error("Seleziona un orario per procedere");
      return;
    }

    if (availableSeats !== null && availableSeats <= 0) {
      toast.error("Non ci sono più posti disponibili per questo orario");
      return;
    }

    navigate("/confirm-booking", {
      state: {
        dealId,
        bookingDetails: {
          date: selectedDate,
          day: new Date(selectedDate).getDay() || 7,
          start_time: selectedTime || "00:00",
          end_time: endTime || "00:00",
        },
      },
    });
  };

  // Determina se il pulsante dovrebbe essere disabilitato
  const isButtonDisabled =
    isOwner ||
    (hasTimeSlots ? !selectedDate || !selectedTime : !selectedDate) ||
    (availableSeats !== null && availableSeats <= 0);

  // Determina il testo del pulsante in base allo stato
  const getButtonText = () => {
    if (isOwner) return "Non prenotabile";
    if (isLoading) return "Verifica disponibilità...";
    if (availableSeats !== null && availableSeats <= 0) return "Posti esauriti";

    if (hasTimeSlots && (!selectedDate || !selectedTime))
      return "Seleziona slot";

    if (!selectedDate) return "Seleziona una data";
    return "Prenota";
  };

  return (
    <nav className="fixed bottom-0 w-full bg-white border-t border-gray-200 px-6 py-2">
      <div className="flex justify-between items-center">
        {/* Mostra il prezzo originale e il prezzo scontato */}
        <div className="flex flex-col">
          {originalPrice !== 0 ? (
            <>
              <span className="text-gray-500 line-through text-sm">
                €{originalPrice}
              </span>
              <span className="text-2xl font-bold">€{discountedPrice}</span>
            </>
          ) : (
            <span className="text-2xl font-bold">Sconto: {discount}%</span>
          )}

          {/* Mostra informazioni sui posti solo se disponibili e non è il proprietario */}
          {!isOwner && selectedTime && availableSeats !== null && (
            <div className="flex items-center gap-1 mt-1">
              <Users className="h-4 w-4 text-green-600" />
              <span
                className={`text-sm ${
                  availableSeats > 0 ? "text-green-600" : "text-red-500"
                }`}
              >
                {availableSeats === 0
                  ? "Posti esauriti"
                  : availableSeats === 1
                  ? "Ultimo posto disponibile"
                  : `${availableSeats} posti disponibili`}
              </span>
            </div>
          )}
        </div>

        <button
          className={`px-8 py-3 rounded-full font-semibold ${
            isButtonDisabled
              ? "bg-gray-200 text-gray-500 cursor-not-allowed"
              : "bg-brand-primary text-white hover:bg-brand-primary/90 transition-colors"
          }`}
          onClick={handleBooking}
          disabled={isButtonDisabled}
          title={
            isOwner ? "Non puoi prenotare un'offerta della tua attività" : ""
          }
        >
          {getButtonText()}
        </button>
      </div>
    </nav>
  );
};

export default DealBookingFooter;

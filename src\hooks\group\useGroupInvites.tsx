
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface PendingInvite {
  id: string;
  invited_user_id: string;
  invited_by: string;
  status: string;
  created_at: string;
  invited_user: {
    first_name: string;
    last_name: string;
    email: string;
    avatar_url?: string;
  };
}

export const useGroupInvites = (groupId: string) => {
  return useQuery({
    queryKey: ['group-invites', groupId],
    queryFn: async (): Promise<PendingInvite[]> => {
      const { data, error } = await supabase
        .from('group_invites')
        .select(`
          id,
          invited_user_id,
          invited_by,
          status,
          created_at,
          invited_user:users_with_details!group_invites_invited_user_id_fkey(
            first_name,
            last_name,
            email,
            avatar_url
          )
        `)
        .eq('group_id', groupId)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching group invites:', error);
        throw error;
      }

      return data || [];
    },
    enabled: !!groupId
  });
};

export const useRevokeInvite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (inviteId: string) => {
      const { error } = await supabase
        .from('group_invites')
        .delete()
        .eq('id', inviteId);

      if (error) {
        console.error('Error revoking invite:', error);
        throw error;
      }
    },
    onSuccess: (_, inviteId) => {
      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['group-invites'] });
      toast.success('Invito revocato con successo');
    },
    onError: () => {
      toast.error('Errore nella revoca dell\'invito');
    }
  });
};

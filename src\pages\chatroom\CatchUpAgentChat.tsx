
import { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Send,
  Mic,
  Square,
  Play,
  Pause,
  Bot,
} from "lucide-react";
import { useAuth } from "@/hooks/auth/useAuth";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import ReactMarkdown from "react-markdown";
import VoiceRecordingFeedback from "@/components/chat/VoiceRecordingFeedback";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  isVoice?: boolean;
}

const CatchUpAgentChat = () => {
  const navigate = useNavigate();
  const { user, userDetails } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: "welcome",
      content: "Ciao! Sono l'assistente CatchUp 🎯 Sono qui per aiutarti a trovare le migliori offerte e prenotazioni nella tua zona. Come posso aiutarti oggi?",
      isUser: false,
      timestamp: new Date(),
    }
  ]);
  const [newMessage, setNewMessage] = useState("");
  const [isWaiting, setIsWaiting] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const { userLocation } = useLocationManagement();

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunksRef.current.push(e.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(chunksRef.current, { type: "audio/webm" });
        const reader = new FileReader();
        reader.readAsDataURL(audioBlob);
        reader.onloadend = async () => {
          const base64Audio = reader.result as string;
          const base64Only = base64Audio.split(",")[1];
          await handleSendMessage(base64Only, true);
        };

        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (e) {
      console.error(e);
      alert("Per favore, concedi l'accesso al microfono");
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const handleRecord = async () => {
    if (isRecording) {
      stopRecording();
    } else {
      await startRecording();
    }
  };

  const handleSendMessage = async (content: string, isVoice = false) => {
    if ((!content.trim() && !isVoice)) return;

    const messageText = content.trim();
    setNewMessage("");

    // Add user message
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      content: messageText,
      isUser: true,
      timestamp: new Date(),
      isVoice,
    };

    setMessages(prev => [...prev, userMessage]);
    setIsWaiting(true);

    // Simulate AI response (you can replace this with actual API call)
    try {
      // TODO: Replace with actual CatchUp agent API call
      setTimeout(() => {
        const responses = [
          "Perfetto! Ti sto cercando le migliori offerte nella tua zona. Un momento...",
          "Ho trovato alcune fantastiche opportunità per te! Cosa ti interessa di più: ristoranti, benessere o servizi?",
          "Ottima scelta! Lasciami controllare la disponibilità per te.",
          "Ti consiglio di dare un'occhiata a queste offerte che ho selezionato appositamente per te!",
        ];
        
        const agentMessage: ChatMessage = {
          id: `agent-${Date.now()}`,
          content: responses[Math.floor(Math.random() * responses.length)],
          isUser: false,
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, agentMessage]);
        setIsWaiting(false);
      }, 2000);
    } catch (error) {
      console.error("Error calling CatchUp agent:", error);
      setIsWaiting(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(newMessage);
    }
  };

  const isBase64Audio = (content: string) => {
    try {
      if (content.trim().length === 0 || /^[\p{L}\p{N}\p{P}\p{Z}]+$/u.test(content)) {
        return false;
      }
      if (!/^[A-Za-z0-9+/=]+$/.test(content)) {
        return false;
      }
      if (content.length < 100) {
        return false;
      }
      const decodedString = atob(content);
      return decodedString.length > 0;
    } catch {
      return false;
    }
  };

  const AudioMessage = ({ content }: { content: string }) => {
    const [isPlaying, setIsPlaying] = useState(false);
    const audioRef = useRef<HTMLAudioElement>(null);

    const togglePlay = () => {
      if (audioRef.current) {
        if (isPlaying) {
          audioRef.current.pause();
        } else {
          audioRef.current.play();
        }
      }
    };

    useEffect(() => {
      const audio = audioRef.current;
      if (audio) {
        audio.onplay = () => setIsPlaying(true);
        audio.onpause = () => setIsPlaying(false);
        audio.onended = () => setIsPlaying(false);
      }
    }, []);

    return (
      <div className="flex items-center gap-2">
        <button
          onClick={togglePlay}
          className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
        >
          {isPlaying ? (
            <Pause className="h-4 w-4" />
          ) : (
            <Play className="h-4 w-4" />
          )}
        </button>
        <audio ref={audioRef} src={`data:audio/webm;base64,${content}`} />
        <span className="text-sm">Messaggio vocale</span>
      </div>
    );
  };

  const AgentThinkingRender = () => {
    return (
      <div className="flex justify-start">
        <div className="flex items-end gap-2">
          <Avatar className="h-8 w-8 bg-gradient-to-r from-purple-500 to-pink-500 text-white">
            <AvatarFallback>
              <Bot className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
          <div className="bg-white border rounded-lg p-3 shadow-sm">
            <div className="flex space-x-2">
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "0ms" }}
              ></div>
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "200ms" }}
              ></div>
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "400ms" }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <header className="fixed top-0 left-0 right-0 bg-white z-50 px-4 py-3 flex items-center justify-between border-b">
        <div className="flex items-center">
          <button onClick={() => navigate(-1)} className="p-2">
            <ArrowLeft className="h-5 w-5 text-gray-700" />
          </button>
          <div className="flex items-center gap-2 ml-2">
            <Avatar className="h-8 w-8 bg-gradient-to-r from-purple-500 to-pink-500">
              <AvatarFallback>
                <Bot className="h-4 w-4 text-white" />
              </AvatarFallback>
            </Avatar>
            <h1 className="text-lg font-semibold">Assistente CatchUp</h1>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-xs text-gray-500">Online</span>
        </div>
      </header>

      <main className="flex-1 pt-16 pb-20 px-4 overflow-y-auto">
        <div className="space-y-4">
          {messages.map((message) => (
            <div key={message.id} className={`flex ${message.isUser ? "justify-end" : "justify-start"}`}>
              <div className={`flex items-end gap-2 max-w-[80%] ${message.isUser ? "flex-row-reverse" : "flex-row"}`}>
                <Avatar className={`h-8 w-8 ${message.isUser ? "bg-brand-primary text-white" : "bg-gradient-to-r from-purple-500 to-pink-500 text-white"}`}>
                  <AvatarFallback>
                    {message.isUser ? userDetails?.first_name?.charAt(0) || "U" : <Bot className="h-4 w-4" />}
                  </AvatarFallback>
                </Avatar>

                <div className={`rounded-lg p-3 shadow-sm ${
                  message.isUser 
                    ? "bg-brand-primary text-white rounded-tr-none" 
                    : "bg-white border rounded-tl-none"
                }`}>
                  <div className={`text-xs mb-1 font-medium ${message.isUser ? "text-white/80" : "text-gray-500"}`}>
                    {message.isUser ? "Tu" : "CatchUp"}
                  </div>

                  {message.isVoice && isBase64Audio(message.content) ? (
                    <AudioMessage content={message.content} />
                  ) : (
                    <div className="text-sm prose prose-sm dark:prose-invert max-w-none">
                      <ReactMarkdown>{message.content}</ReactMarkdown>
                    </div>
                  )}

                  <div className={`text-xs mt-1 ${message.isUser ? "text-white/70" : "text-gray-500"}`}>
                    {format(message.timestamp, "HH:mm", { locale: it })}
                  </div>
                </div>
              </div>
            </div>
          ))}

          {isWaiting && <AgentThinkingRender />}
          <div ref={messagesEndRef} />
        </div>
      </main>

      <VoiceRecordingFeedback isRecording={isRecording} />

      <footer className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
        <div className="flex items-center gap-2">
          <textarea
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Scrivi un messaggio..."
            className="flex-1 resize-none border rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-brand-primary"
            rows={1}
          />
          <Button
            onClick={handleRecord}
            variant="outline"
            size="icon"
            className={`rounded-full ${
              isRecording ? "bg-red-500 text-white hover:bg-red-600" : ""
            }`}
          >
            {isRecording ? (
              <Square className="h-5 w-5" />
            ) : (
              <Mic className="h-5 w-5" />
            )}
          </Button>
          <Button
            onClick={() => handleSendMessage(newMessage)}
            disabled={!newMessage.trim() || isWaiting}
            variant="default"
            size="icon"
            className="rounded-full"
          >
            <Send className="h-5 w-5" />
          </Button>
        </div>
      </footer>
    </div>
  );
};

export default CatchUpAgentChat;

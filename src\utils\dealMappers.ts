
import { Deal, DealStatus } from "@/types/deals";
import { DealAvailability } from "@/types/search";

/**
 * Converte i risultati della ricerca di offerte vicine nel formato accettato da DealCarousel
 */
export const mapNearbyDealsToDealFormat = (nearbyDeals: DealAvailability[] = []): Deal[] => {
  if (!nearbyDeals.length) return [];
console.log(nearbyDeals);
  return nearbyDeals.map(deal => ({
    id: deal.id,
    title: deal.name,
    description: deal.description || "",
    business_id: deal.business?.id || "",
    discounted_price: deal.discounted_price,
    original_price: deal.original_price,
    discount_percentage: deal.discount_percentage,
    start_date: deal.start_date,
    end_date: deal.end_date,
    time_slots: deal.time_slots,
    // Assicuriamoci che status sia uno dei valori ammessi dal tipo DealStatus
    status: validateDealStatus(deal.status),
    created_at: deal.created_at || new Date().toISOString(),
    updated_at: deal.updated_at || new Date().toISOString(),
    images: deal.images || [],
    auto_confirm: deal.auto_confirm !== false,
    category_id: deal.category_id || deal.business?.category_id || "",
    fake: false,
    // Aggiungiamo il campo terms_conditions con un valore predefinito se non è presente
    terms_conditions: deal.terms_conditions || "Informazioni importanti -La prenotazione non è rimborsabile -Presentati 5 minuti prima dell'orario prenotato -Porta con te un documento d'identità",
    // Campi aggiuntivi per compatibilità
    price: deal.discounted_price,
    image_url: deal.images && deal.images.length > 0 ? deal.images[0] : "",
    business_name: deal.business?.name || "",
    capacity: 10, // Valore predefinito
    businesses: {
      name: deal.business?.name || "",
      address: deal.business?.address || "",
      city: deal.business?.city || "",
      state: deal.business?.state || "",
      zip_code: deal.business?.zip_code || "",
      country: deal.business?.country || "",
      phone: deal.business?.phone || "",
      email: deal.business?.email || "",
      website: deal.business?.website || "",
    }
  }));
};

/**
 * Funzione di supporto per validare lo stato dell'offerta
 * Assicura che lo stato sia uno dei valori ammessi dal tipo DealStatus
 */
function validateDealStatus(status: string | undefined): DealStatus {
  if (status === 'published' || status === 'draft' || status === 'expired') {
    return status;
  }
  // Valore predefinito se lo stato non è valido
  return 'published';
}

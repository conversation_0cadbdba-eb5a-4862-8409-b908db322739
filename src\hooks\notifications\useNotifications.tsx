
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface Notification {
  id: string;
  user_id: string;
  entity: string;
  entity_id: string;
  created_at: string;
  updated_at: string;
}

interface CreateNotificationData {
  entity: string;
  entity_id: string;
}

interface UpdateNotificationData {
  entity?: string;
  entity_id?: string;
}

// Hook per leggere le notifiche dell'utente corrente
export const useNotifications = () => {
  return useQuery({
    queryKey: ['notifications'],
    queryFn: async (): Promise<Notification[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('Utente non autenticato');
      }

      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Errore nel recupero delle notifiche:', error);
        throw error;
      }

      return data || [];
    }
  });
};

// Hook per creare una nuova notifica
export const useCreateNotification = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (notificationData: CreateNotificationData) => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('Utente non autenticato');
      }

      const { data, error } = await supabase
        .from('notifications')
        .insert({
          user_id: user.id,
          entity: notificationData.entity,
          entity_id: notificationData.entity_id
        })
        .select()
        .single();

      if (error) {
        console.error('Errore nella creazione della notifica:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast.success('Notifica creata con successo');
    },
    onError: (error) => {
      console.error('Errore nella creazione della notifica:', error);
      toast.error('Errore nella creazione della notifica');
    }
  });
};

// Hook per aggiornare una notifica
export const useUpdateNotification = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, ...updateData }: UpdateNotificationData & { id: string }) => {
      const { data, error } = await supabase
        .from('notifications')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Errore nell\'aggiornamento della notifica:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast.success('Notifica aggiornata con successo');
    },
    onError: (error) => {
      console.error('Errore nell\'aggiornamento della notifica:', error);
      toast.error('Errore nell\'aggiornamento della notifica');
    }
  });
};

// Hook per eliminare una notifica
export const useDeleteNotification = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (notificationId: string) => {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) {
        console.error('Errore nell\'eliminazione della notifica:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast.success('Notifica eliminata con successo');
    },
    onError: (error) => {
      console.error('Errore nell\'eliminazione della notifica:', error);
      toast.error('Errore nell\'eliminazione della notifica');
    }
  });
};

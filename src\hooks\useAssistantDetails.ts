
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";


export interface AssistantDetails {
  id: string;
  voice_id: string | null;
  name: string;
  image_url?: string;
}

export const useAssistantDetails = (isOpen: boolean, selectedAssistantId: string | undefined) => {
  const [assistantDetails, setAssistantDetails] = useState<AssistantDetails | null>(null);

  useEffect(() => {
    const fetchAssistantDetails = async () => {
      if (!selectedAssistantId) return;

      try {
        const { data, error } = await supabase
          .from('ai_assistant_profile')
          .select('id, voice_id, name, image_url')
          .eq('id', selectedAssistantId)
          .single();

        if (error) throw error;
        
        if (data) {
          setAssistantDetails(data);
        }
      } catch (error) {
        console.error('Errore nel recupero dei dettagli assistente:', error);
        toast.error("Errore nel recupero dei dettagli assistente");
      }
    };

    if (isOpen) {
      fetchAssistantDetails();
    }
  }, [isOpen, selectedAssistantId]);

  return assistantDetails;
};

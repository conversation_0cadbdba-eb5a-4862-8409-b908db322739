
 // City coordinate ranges for realistic address generation
 type CityCoordinates = {
    name: string;
    latitude: { min: number; max: number };
    longitude: { min: number; max: number };
  };
  
 
 // List of Italian cities with approximate coordinate ranges
  export const cities: CityCoordinates[] = [
    {
      name: "Milano",
      latitude: { min: 45.4, max: 45.5 },
      longitude: { min: 9.1, max: 9.2 },
    },
    {
      name: "Roma",
      latitude: { min: 41.8, max: 41.95 },
      longitude: { min: 12.4, max: 12.6 },
    },
    {
      name: "Napoli",
      latitude: { min: 40.8, max: 40.9 },
      longitude: { min: 14.2, max: 14.3 },
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      latitude: { min: 43.7, max: 43.8 },
      longitude: { min: 11.2, max: 11.3 },
    },
    {
      name: "Torino",
      latitude: { min: 45.0, max: 45.1 },
      longitude: { min: 7.6, max: 7.7 },
    },
    {
      name: "Bologna",
      latitude: { min: 44.4, max: 44.5 },
      longitude: { min: 11.3, max: 11.4 },
    },
    {
      name: "Venezia",
      latitude: { min: 45.4, max: 45.5 },
      longitude: { min: 12.3, max: 12.4 },
    },
  ];

  // Dati italiani per generazione più realistica
  export const italianData = {
    // Organizziamo i nomi delle aziende per categoria
    companyNamesByCategory: {
      // Bar, caffetterie, pub
      bar: [
        "Bar Sport",
        "Caffè Italia",
        "Bar Centrale",
        "Caffetteria Milano",
        "Bar Roma",
        "American Bar",
        "Irish Pub",
        "Cocktail Bar",
        "Wine Bar",
        "Lounge Bar",
        "Caffè Letterario",
        "Pasticceria & Caffè",
        "Bar del Corso",
        "Bar Moderno",
        "Bar di Moda",
        "Caffè Sognante",
        "Bar Urbano",
        "Caffè d'Autore",
        "Bar Classico",
        "Caffè del Duomo",
        "Bar Elegante",
        "Caffè Nero",
        "Bar Roma Nuova",
        "Caffè Milano",
        "Bar del Sole",
        "Caffetteria Centrale",
        "Bar Bella Vita",
        "Caffè Antico",
        "Bar & Bistro",
        "Bar Mediterraneo",
        "Caffè Vero",
        "Bar Armonia",
        "Caffè Moderno",
        "Bar Amore",
        "Caffè del Centro",
        "Bar Originale",
        "Caffè Paradiso",
        "Bar Lounge",
        "Caffè Culturale",
        "Bar del Porto",
        "Caffè del Mare",
        "Bar Vivace",
        "Caffè Rustico",
        "Bar Notturno",
        "Caffè del Giardino",
        "Bar Incontro",
        "Caffè Vivo",
        "Bar Urban",
        "Caffè Classico",
        "Bar Iconico",
      ],

      // Ristoranti, pizzerie, trattorie
      ristorante: [
        "Ristorante Da Luigi",
        "Pizzeria Napoletana",
        "Trattoria Il Gusto",
        "Osteria La Cucina",
        "Ristorante Bella Napoli",
        "Pizzeria Margherita",
        "Ristorante Al Duomo",
        "Trattoria da Mario",
        "Ristorante La Pergola",
        "Pizzeria Il Forno",
        "Taverna del Corso",
        "Ristorante La Terrazza",
        "Osteria del Borgo",
        "Trattoria del Sole",
        "Ristorante Il Sapore",
        "Pizzeria del Centro",
        "Ristorante Gourmet",
        "Trattoria Tradizionale",
        "Ristorante Il Gusto Italiano",
        "Pizzeria Vesuvio",
        "Ristorante Al Fresco",
        "Trattoria Rustica",
        "Ristorante Il Palato",
        "Pizzeria Romana",
        "Ristorante Da Antonio",
        "Trattoria del Nonno",
        "Ristorante Il Sogno",
        "Pizzeria Bella Italia",
        "Ristorante La Dolce Vita",
        "Trattoria della Nonna",
        "Ristorante Al Mare",
        "Pizzeria Il Cuore",
        "Ristorante Sapori d'Italia",
        "Trattoria Rusticana",
        "Ristorante Delizie",
        "Pizzeria La Regina",
        "Ristorante Tradizione",
        "Trattoria dei Sapori",
        "Ristorante Il Fornaio",
        "Pizzeria La Vera",
        "Ristorante Eleganza",
        "Trattoria del Gusto",
        "Ristorante Saporito",
        "Pizzeria Classica",
        "Ristorante del Borgo",
        "Trattoria Mediterranea",
        "Ristorante Incanto",
        "Pizzeria del Vesuvio",
        "Ristorante La Pergola",
        "Trattoria La Famiglia",
      ],

      // Alimentari, supermercati, negozi di cibo
      alimentari: [
        "Alimentari Da Mario",
        "Macelleria Rossi",
        "Panificio Il Forno",
        "Gastronomia Italiana",
        "Pastificio Artigianale",
        "Pescheria Del Mare",
        "Salumeria Tradizionale",
        "Fruttivendolo Il Raccolto",
        "Enoteca Vini & Sapori",
        "Negozio del Gusto",
        "Mercato di Via Roma",
        "Emporio Alimentare",
        "Delizie d'Italia",
        "Bottega del Cibo",
        "Alimentari La Bontà",
        "Spesa & Sapori",
        "Mercato Fresco",
        "Il Salumiere",
        "La Dispensa Italiana",
        "Cibo & Sapori",
        "Alimentari Il Sapore",
        "Bottega Gourmet",
        "Emporio del Gusto",
        "Negozio di Delizie",
        "La Casa del Cibo",
        "Mercato della Bontà",
        "Sapori & Sapienza",
        "Delizie del Mercato",
        "Cibo Fresco",
        "Il Gourmet Alimentari",
        "Emporio del Sapore",
        "Bottega dei Sapori",
        "Negozio Saporito",
        "Il Mercato dei Sapori",
        "Cucina e Mercato",
        "Sapori d'Orto",
        "Gastronomia del Centro",
        "Alimentari La Tradizione",
        "Il Bazar del Cibo",
        "Mercato delle Specialità",
        "Sapori e Tradizioni",
        "Bottega del Gusto",
        "Emporio Fresco",
        "La Dispensa Moderna",
        "Negozio delle Prelibatezze",
        "Il Cestino del Gusto",
        "Bottega di Sapori",
        "Mercato d'Italia",
        "Emporio della Tradizione",
        "Alimentari La Freschezza",
      ],

      // Parrucchieri, barbieri, saloni di bellezza
      bellezza: [
        "Parrucchiere Trend",
        "Salone Bellezza",
        "Centro Estetico Aurora",
        "Barbiere Classico",
        "Hair Stylist Milano",
        "Beauty Center",
        "Parrucchiere Donna & Uomo",
        "Barber Shop",
        "Estetica Eleganza",
        "Salone di Bellezza Moderna",
        "Centro di Bellezza",
        "Parrucchiere Glamour",
        "Salone di Stile",
        "Barbiere d'Arte",
        "Beauty Lounge",
        "Centro Estetico Bella Vita",
        "Parrucchieri d'Elite",
        "Salone Luminoso",
        "Estetica Avanguardia",
        "Hair Design Studio",
        "Beauty & Style",
        "Centro Bellezza Iconico",
        "Parrucchiere d'Eccellenza",
        "Salone di Tendenza",
        "Barbiere Urbano",
        "Beauty Lab",
        "Estetica e Benessere",
        "Parrucchiere Modaiolo",
        "Salone Armonia",
        "Barbiere del Centro",
        "Beauty Boutique",
        "Centro Estetico Moderna",
        "Parrucchiere Chic",
        "Salone di Lusso",
        "Estetica Incanto",
        "Hair & Beauty Studio",
        "Beauty Concept",
        "Centro Bellezza Sublime",
        "Parrucchiere Iconico",
        "Salone Raffinato",
        "Estetica Classica",
        "Hair Masters",
        "Beauty Vision",
        "Centro Estetico Royale",
        "Parrucchiere Elite",
        "Salone di Bellezza Unico",
        "Estetica Moderna",
        "Beauty Experience",
        "Centro Bellezza Perfetto",
        "Parrucchiere Innovazione",
      ],

      // Abbigliamento e moda
      abbigliamento: [
        "Boutique Eleganza",
        "Sartoria Italiana",
        "Atelier Moda",
        "Fashion Store",
        "Abbigliamento Uomo",
        "Negozio Donna",
        "Calzature Europa",
        "Accessori & Borse",
        "Moda d'Autore",
        "Boutique Classica",
        "Stile e Moda",
        "Abbigliamento di Lusso",
        "Fashion Boutique",
        "Eleganza e Stile",
        "Abiti su Misura",
        "Atelier di Tendenza",
        "Moda Moderna",
        "Boutique Chic",
        "Sartoria Moderna",
        "Vetrina di Moda",
        "Stile Unico",
        "Couture Italiana",
        "Moda Esclusiva",
        "Abbigliamento Iconico",
        "Fashion Lab",
        "Boutique d'Arte",
        "Moda Premium",
        "Stile Urbano",
        "Atelier Raffinato",
        "Moda Innovativa",
        "Abiti d'Eccellenza",
        "Fashion Emporio",
        "Boutique Squisita",
        "Stile Classico",
        "Abbigliamento Contemporaneo",
        "Atelier Elegante",
        "Moda e Charme",
        "Fashion Express",
        "Boutique dei Sogni",
        "Stile & Tendenza",
        "Abiti di Charme",
        "Moda Perfetta",
        "Fashion Avenue",
        "Boutique Incanto",
        "Stile Iconico",
        "Abbigliamento Refinato",
        "Atelier Unico",
        "Moda d'Epoca",
        "Fashion Sensation",
        "Boutique Classy",
      ],

      // Palestra, centri fitness
      palestra: [
        "Palestra Energia",
        "Centro Fitness Italia",
        "Palestra Vitalità",
        "Gym Evolution",
        "Palestra Forma",
        "Elite Fitness Club",
        "Palestra Dinamica",
        "Atelier Benessere",
        "Palestra Performance",
        "Fitness Revolution",
        "Gym Pro",
        "Palestra Xtreme",
        "Centro Fitness Avanzato",
        "Palestra Alpha",
        "Gym Mastery",
        "Palestra Attiva",
        "Fitness Studio",
        "Palestra Power",
        "Gym Italia",
        "Palestra Equilibrio",
        "Fitness Zone",
        "Palestra Infinity",
        "Gym Impact",
        "Centro Fitness Max",
        "Palestra Suprema",
        "Gym Progress",
        "Palestra Futuro",
        "Fitness Hub",
        "Palestra Elite",
        "Gym Performance",
        "Palestra Advance",
        "Fitness Factory",
        "Gym Dynamic",
        "Palestra Zen",
        "Fitness Core",
        "Palestra Fusion",
        "Gym Evolution Pro",
        "Centro Fitness Prime",
        "Palestra Impact",
        "Gym Spirit",
        "Palestra Vigor",
        "Fitness Force",
        "Palestra Supreme",
        "Gym Powerhouse",
        "Palestra Infinity Pro",
        "Fitness Arena",
        "Palestra Active",
        "Gym Revolution",
        "Palestra Titan",
        "Fitness Empire",
      ],

      // Spa, centri benessere, centri estetici
      spa: [
        "SPA Serenità",
        "Oasi del Relax",
        "Centro Benessere",
        "SPA Armonia",
        "Rifugio del Relax",
        "Spa Incanto",
        "Oasi Spa",
        "Benessere & Bellezza",
        "SPA Eterna",
        "Paradiso Spa",
        "Trattamento di Lusso",
        "Resort della Pace",
        "L'Oasi del Sogno",
        "SPA Splendore",
        "Equilibrio Spa",
        "SPA Nirvana",
        "Oasi dell'Anima",
        "Centro SPA Rilassante",
        "Spa del Cuore",
        "Oasi di Benessere",
        "SPA Relax",
        "Oasi di Serenità",
        "Centro Spa Vitalità",
        "Spa dell'Incanto",
        "Rifugio Spa",
        "SPA Paradiso",
        "Oasi dell'Armonia",
        "Spa di Lusso",
        "Centro Benessere Royale",
        "SPA Armoniosa",
        "Oasi di Relax Profondo",
        "Spa del Sorriso",
        "Rifugio di Serenità",
        "SPA Sublime",
        "Oasi Zen",
        "Centro SPA Incantevole",
        "Spa di Equilibrio",
        "Rifugio del Benessere",
        "SPA Tranquillità",
        "Oasi Spa Delicata",
        "Centro di Relax",
        "Spa di Eccellenza",
        "SPA Celestiale",
        "Oasi del Sorriso",
        "Centro Spa Divino",
        "Spa Incantevole",
        "Rifugio della Pace",
        "SPA Armonia Perfetta",
        "Oasi di Rilassamento",
        "Centro Benessere Supremo",
      ],

      // Default per altre categorie
      default: [
        "Attività Italiana",
        "Negozio Centro",
        "Emporio Milano",
        "Azienda Rossi",
        "Ditta Bianchi",
        "Servizi Generali",
        "Centro Specializzato",
        "Agenzia Moderna",
        "Soluzioni Innovative",
        "Consulenza Professionale",
        "Offerta Speciale",
        "Pacchetto Base",
        "Prodotto Signature",
        "Servizio Premium",
        "Kit Completo",
        "Soluzione Personalizzata",
        "Esperienza Unica",
        "Qualità Garantita",
        "Servizi Esclusivi",
        "Centro di Eccellenza",
        "Innovazione Italiana",
        "Centro Servizi",
        "Offerta Imperdibile",
        "Emporio di Qualità",
        "Azienda Innovativa",
        "Servizi Moderni",
        "Consulenza Specializzata",
        "Pacchetto Avanzato",
        "Soluzioni su Misura",
        "Servizio Unico",
        "Prodotto Esclusivo",
        "Esperienza Premium",
        "Emporio Speciale",
        "Centro Soluzioni",
        "Offerta Unica",
        "Servizio Professionale",
        "Pacchetto Completo",
        "Prodotto di Eccellenza",
        "Agenzia Specializzata",
        "Emporio Moderno",
        "Centro Innovativo",
        "Soluzione Smart",
        "Consulenza Innovativa",
        "Pacchetto Esclusivo",
        "Prodotto Premium",
        "Servizio Esclusivo",
        "Soluzioni Integrate",
        "Centro Avanzato",
        "Emporio Specializzato",
        "Offerta Innovativa",
      ],
    },
    companySuffixes: [
      "S.r.l.",
      "S.p.A.",
      "& Figli",
      "di Mario Rossi",
      "Group",
      "S.n.c.",
      "e Figli",
      "Holding",
      "International",
      "Industries",
      "S.a.s.",
      "e Associati",
      "Partners",
      "Di Famiglia",
      "e C.",
      "Consortium",
      "Società Unipersonale",
      "S.r.l.s.",
      "Inc.",
      "Global",
    ],
    productNames: {
      // Prodotti per bar/caffetterie
      bar: [
        "Caffè Espresso",
        "Cappuccino",
        "Caffè Macchiato",
        "Brioche",
        "Cornetto alla Crema",
        "Aperitivo Spritz",
        "Panino Gourmet",
        "Cocktail Signature",
        "Croissant Artigianale",
        "Tramezzino Classico",
        "Caffè Freddo",
        "Latte Macchiato",
        "Espresso Doppio",
        "Caffè Viennese",
        "Caffè Affogato",
        "Caffè Mocha",
        "Flat White",
        "Moka Speciale",
        "Ciambella al Caffè",
        "Biscotti Artigianali",
        "Torta al Caffè",
        "Macchiato Caramellato",
        "Caffè Lungo",
        "Espresso Ristretto",
        "Frappuccino",
        "Caffè Granita",
        "Tè Freddo",
        "Infuso al Miele",
        "Smoothie di Frutta",
        "Acqua Frizzante",
        "Succo di Arancia",
        "Succo di Mela",
        "Panino con Salame",
        "Panino Vegetariano",
        "Insalata Mista",
        "Torta di Mele",
        "Muffin al Cioccolato",
        "Crostata di Frutta",
        "Pasticcino al Pistacchio",
        "Croissant al Cioccolato",
        "Brownie al Caffè",
        "Biscotti al Burro",
        "Cioccolata Calda",
        "Caffè con Panna",
        "Granita al Limone",
        "Sorbetto di Frutta",
        "Frappé al Caramello",
        "Yogurt con Miele",
        "Pane Integrale",
        "Focaccia al Rosmarino",
      ],

      // Prodotti per ristoranti/pizzerie
      ristorante: [
        "Pizza Margherita",
        "Spaghetti alla Carbonara",
        "Bistecca Fiorentina",
        "Risotto ai Funghi",
        "Lasagne al Forno",
        "Tagliata di Manzo",
        "Penne all'Arrabbiata",
        "Pizza Quattro Stagioni",
        "Tiramisù",
        "Ossobuco con Risotto",
        "Pizza Capricciosa",
        "Spaghetti alle Vongole",
        "Ravioli di Ricotta",
        "Gnocchi al Pesto",
        "Fettuccine Alfredo",
        "Saltimbocca alla Romana",
        "Zuppa di Pesce",
        "Calamari Fritti",
        "Insalata Caprese",
        "Minestrone Tradizionale",
        "Risotto alla Milanese",
        "Pizza Diavola",
        "Spaghetti Aglio e Olio",
        "Polenta e Funghi",
        "Pasta al Pomodoro",
        "Lasagne Verdi",
        "Involtini di Melanzane",
        "Carpaccio di Manzo",
        "Risotto al Radicchio",
        "Pizza Prosciutto e Funghi",
        "Spaghetti al Pesto",
        "Tagliolini al Limone",
        "Pollo alla Cacciatora",
        "Fritto Misto di Mare",
        "Pasta con Sugo di Pomodoro",
        "Risotto ai Frutti di Mare",
        "Pizza Quattro Formaggi",
        "Tortellini in Brodo",
        "Ravioli al Ragù",
        "Cotolette alla Milanese",
        "Pasta alla Norma",
        "Spaghetti Bolognese",
        "Ragu di Anatra",
        "Pizza Vegetariana",
        "Zuppa Toscana",
        "Risotto ai Porcini",
        "Pappardelle al Cinghiale",
        "Frittura di Pesce",
        "Pizza Rustica",
        "Cannoli Siciliani",
      ],

      // Prodotti per negozi alimentari
      alimentari: [
        "Prosciutto di Parma",
        "Parmigiano Reggiano",
        "Pasta Fresca",
        "Olio Extra Vergine",
        "Vino Rosso Toscano",
        "Salame Artigianale",
        "Mozzarella di Bufala",
        "Pane Casereccio",
        "Conserve Artigianali",
        "Aceto Balsamico Tradizionale",
        "Mortadella di Bologna",
        "Gorgonzola Dolce",
        "Ricotta Fresca",
        "Lardo di Colonnata",
        "Pesto Genovese",
        "Pomodori Secchi",
        "Arancini Siciliani",
        "Farina di Grano Duro",
        "Riso Carnaroli",
        "Legumi Selezionati",
        "Olio d'Oliva Extra",
        "Confettura Artigianale",
        "Grissini Croccanti",
        "Biscotti Secchi",
        "Taralli Pugliesi",
        "Formaggio Pecorino",
        "Salmone Affumicato",
        "Capperi di Pantelleria",
        "Olive Nere",
        "Funghi Porcini Secchi",
        "Castagne Arrostite",
        "Miele Millefiori",
        "Acqua Minerale Naturale",
        "Pasta di Gragnano",
        "Sugo all'Arrabbiata",
        "Semi di Finocchio",
        "Spezie Mediterranee",
        "Latte Fresco",
        "Yogurt Greco",
        "Frutta Secca Mista",
        "Tartufo Nero",
        "Zafferano di Sardegna",
        "Lenticchie Secche",
        "Fagioli Borlotti",
        "Pomodorini Pachino",
        "Melanzane Grigliate",
        "Peperoni Arrostiti",
        "Basilico Fresco",
        "Rucola Selvaggia",
        "Cavolfiore Bianco",
      ],

      // Servizi per saloni di bellezza
      bellezza: [
        "Taglio & Piega",
        "Colorazione Professionale",
        "Trattamento Viso",
        "Manicure Deluxe",
        "Pacchetto Benessere",
        "Massaggio Relax",
        "Cura della Barba",
        "Extension Ciglia",
        "Acconciatura Sposa",
        "Pedicure Spa",
        "Trattamento Anti-Age",
        "Make-Up Professionale",
        "Trattamento Capelli",
        "Styling Moderno",
        "Servizio Permanente",
        "Trattamento Nutriente",
        "Maschera Viso Rinfrescante",
        "Terapia Laser",
        "Trattamento Corpo Snellente",
        "Pulizia del Viso Profonda",
        "Servizio Barba & Baffi",
        "Extension Capelli",
        "Trattamento Ristrutturante",
        "Sessione di Trucco",
        "Servizio Sopracciglia",
        "Massaggio Rilassante",
        "Trattamento Rassodante",
        "Detox per Viso",
        "Trattamento Idratante",
        "Cura Mani e Piedi",
        "Rimozione Ciglia",
        "Servizio Styling Personalizzato",
        "Trattamento Anticellulite",
        "Servizio Spa Express",
        "Trattamento Illuminante",
        "Trattamento Detox",
        "Massaggio Aromaterapico",
        "Trucco Sera",
        "Trucco Giorno",
        "Cura Cutanea Avanzata",
        "Servizio Sopracciglia e Ciglia",
        "Trattamento Ringiovanente",
        "Consulenza di Bellezza",
        "Servizio di Trucco Artistico",
        "Manicure Artistica",
        "Pedicure di Lusso",
        "Trattamento Nutriente Capelli",
        "Servizio di Estetica Avanzata",
        "Trattamento Corpo Rilassante",
        "Trattamento Viso Illuminante",
      ],

      // Prodotti per abbigliamento
      abbigliamento: [
        "Abito Sartoriale",
        "Camicia su Misura",
        "Collezione Estate",
        "Accessori Fashion",
        "Borsa in Pelle",
        "Scarpe Artigianali",
        "Cappotto Elegante",
        "Jeans Premium",
        "Abito da Sera",
        "T-shirt Personalizzata",
        "Gonna di Lusso",
        "Pantaloni Classici",
        "Blazer Moderno",
        "Maglione in Cashmere",
        "Giacca in Pelle",
        "Abbigliamento Sportivo",
        "Costume da Bagno",
        "Camicia Casual",
        "Pantaloncini Estivi",
        "Tuta Elegante",
        "Abito da Cocktail",
        "Scarpe da Ginnastica",
        "Borsa a Tracolla",
        "Orologio di Moda",
        "Occhiali da Sole Trendy",
        "Sciarpa in Seta",
        "Cappello Fashion",
        "Guanti in Pelle",
        "Cravatta Classica",
        "Cardigan Morbido",
        "Blusa Elegante",
        "Pantaloni Slim",
        "Vestito a Fiori",
        "Jeans Slim Fit",
        "Giacca Formale",
        "Top Estivo",
        "Leggings Confortevoli",
        "Polo Classica",
        "Maglia a Righe",
        "Abito Elegante",
        "Sneakers di Design",
        "Stivali in Pelle",
        "Gilet da Uomo",
        "Giacca Sportiva",
        "Camicia a Maniche Lunghe",
        "Pantaloni Chino",
        "Abito da Cerimonia",
        "Camicetta Fashion",
        "Accessori di Tendenza",
        "Collezione Autunno/Inverno",
      ],

      // Default per altre categorie
      default: [
        "Servizio Premium",
        "Pacchetto Base",
        "Offerta Speciale",
        "Prodotto Signature",
        "Consulenza Specializzata",
        "Kit Completo",
        "Servizio Professionale",
        "Soluzione Personalizzata",
        "Esperienza Esclusiva",
        "Pacchetto Avanzato",
        "Prodotto Innovativo",
        "Offerta Imperdibile",
        "Servizio Unico",
        "Soluzione Integrata",
        "Pacchetto Deluxe",
        "Prodotto di Eccellenza",
        "Consulenza Premium",
        "Kit Essenziale",
        "Servizio Speciale",
        "Soluzione Smart",
        "Pacchetto Innovativo",
        "Prodotto Unico",
        "Offerta Top",
        "Servizio Avanzato",
        "Soluzione Dinamica",
        "Pacchetto Innovazione",
        "Prodotto Esclusivo",
        "Consulenza d'Eccellenza",
        "Kit Avanzato",
        "Servizio Specializzato",
        "Soluzione Moderna",
        "Pacchetto Completo",
        "Prodotto di Qualità",
        "Offerta Esclusiva",
        "Servizio su Misura",
        "Soluzione Premium",
        "Pacchetto Personalizzato",
        "Prodotto Speciale",
        "Consulenza Innovativa",
        "Kit Professionale",
        "Servizio Integrato",
        "Soluzione d'Avanguardia",
        "Pacchetto d'Eccellenza",
        "Prodotto Personalizzato",
        "Offerta Personalizzata",
        "Servizio Elite",
        "Soluzione Flessibile",
        "Pacchetto Fino",
        "Prodotto d'Elite",
        "Consulenza Speciale",
      ],
    },
    streetNames: [
      "Via Roma",
      "Via Dante",
      "Via Garibaldi",
      "Via Mazzini",
      "Via Verdi",
      "Corso Italia",
      "Corso Europa",
      "Piazza Duomo",
      "Viale dei Giardini",
      "Via Leonardo da Vinci",
      "Via Montebello",
      "Via delle Rose",
      "Via Marconi",
      "Piazza San Marco",
      "Viale Vittorio Emanuele",
      "Corso Vittorio Emanuele II",
      "Via Andrea Solari",
      "Via Montenapoleone",
      "Via Torino",
      "Via Brera",
      "Via Paolo Sarpi",
      "Via Borgogna",
      "Corso Como",
      "Via della Spiga",
    ],
    firstNames: [
      "Andrea",
      "Marco",
      "Giuseppe",
      "Antonio",
      "Giovanni",
      "Mario",
      "Luigi",
      "Francesco",
      "Roberto",
      "Paolo",
      "Alessio",
      "Stefano",
      "Davide",
      "Michele",
      "Carlo",
      "Alessandro",
      "Leonardo",
      "Massimo",
      "Vittorio",
      "Riccardo",
    ],
    lastNames: [
      "Rossi",
      "Bianchi",
      "Ferrari",
      "Esposito",
      "Romano",
      "Colombo",
      "Ricci",
      "Marino",
      "Greco",
      "Bruno",
      "Gallo",
      "Conti",
      "De Luca",
      "Costa",
      "Giordano",
      "Mancini",
      "Rizzo",
      "Lombardi",
      "Moretti",
      "Barbieri",
      "Fontana",
      "Santoro",
    ],
    zipCodes: {
      Milano: [
        "20121",
        "20122",
        "20123",
        "20124",
        "20125",
        "20144",
        "20146",
        "20154",
      ],
      Roma: [
        "00100",
        "00118",
        "00121",
        "00122",
        "00123",
        "00125",
        "00126",
        "00132",
      ],
      Napoli: [
        "80121",
        "80122",
        "80123",
        "80124",
        "80125",
        "80126",
        "80127",
        "80128",
      ],
      Firenze: [
        "50121",
        "50122",
        "50123",
        "50124",
        "50125",
        "50126",
        "50127",
        "50128",
      ],
      Torino: [
        "10121",
        "10122",
        "10123",
        "10124",
        "10125",
        "10126",
        "10127",
        "10128",
      ],
      Bologna: [
        "40121",
        "40122",
        "40123",
        "40124",
        "40125",
        "40126",
        "40127",
        "40128",
      ],
      Venezia: [
        "30121",
        "30122",
        "30123",
        "30124",
        "30125",
        "30126",
        "30127",
        "30128",
      ],
    },
    provinces: {
      Milano: "MI",
      Roma: "RM",
      Napoli: "NA",
      Firenze: "FI",
      Torino: "TO",
      Bologna: "BO",
      Venezia: "VE",
    },
    domains: ["it", "com", "net", "org"],
    phoneFormats: [
      "+39 ### ### ####",
      "+39 ## ## ## ##",
      "### ### ####",
      "## ## ## ##",
    ],
    catchPhrases: [
      "La qualità che merita",
      "Tradizione e innovazione",
      "Il gusto autentico italiano",
      "Dal 1985 al vostro servizio",
      "I sapori della tradizione",
      "L'eccellenza del made in Italy",
      "Artigianalità e passione",
      "Il meglio per i nostri clienti",
    ],
  };

  //
import { Clock } from "lucide-react";
import FlexibleDealCarousel from "./FlexibleDealCarousel";
import { useFlexibleDeals } from "@/hooks/useFlexibleDeals";

interface EndingSoonCarouselProps {
  className?: string;
}

const EndingSoonCarousel = ({ className = "" }: EndingSoonCarouselProps) => {
  const { sections, isLoading } = useFlexibleDeals('ending_soon', {
    maxSections: 1,
    dealsPerSection: 10,
    userPreferences: false
  });

  return (
    <FlexibleDealCarousel
      title="Scadono Presto"
      titleIcon={Clock}
      sections={sections}
      isLoading={isLoading}
      className={className}
    />
  );
};

export default EndingSoonCarousel;
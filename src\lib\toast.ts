import toast from "react-hot-toast";

type ToastPosition = 
  | "top-left" 
  | "top-center" 
  | "top-right" 
  | "bottom-left" 
  | "bottom-center" 
  | "bottom-right";

interface ToastOptions {
  duration?: number;
  position?: ToastPosition;
  style?: React.CSSProperties;
  className?: string;
  icon?: string | React.ReactElement;
  id?: string;
}

// Create a toast utility that matches sonner's API but uses react-hot-toast
export const toastNotification = {
  // Basic toast (equivalent to sonner's toast())
  message: (message: string, options: ToastOptions = {}) => {
    return toast(message, {
      duration: options.duration,
      position: options.position,
      style: options.style,
      className: options.className,
      icon: options.icon,
      id: options.id,
    });
  },

  // Success toast
  success: (message: string, options: ToastOptions = {}) => {
    return toast.success(message, {
      duration: options.duration || 3000,
      position: options.position,
      style: options.style,
      className: options.className,
      icon: options.icon,
      id: options.id,
    });
  },

  // Error toast
  error: (message: string, options: ToastOptions = {}) => {
    return toast.error(message, {
      duration: options.duration || 5000,
      position: options.position,
      style: options.style,
      className: options.className,
      icon: options.icon,
      id: options.id,
    });
  },

  // Info toast (using custom styling for info appearance)
  info: (message: string, options: ToastOptions = {}) => {
    return toast(message, {
      duration: options.duration || 4000,
      position: options.position,
      style: {
        background: "#3b82f6",
        color: "#ffffff",
        ...options.style,
      },
      className: options.className,
      icon: options.icon || "ℹ️",
      id: options.id,
    });
  },

  // Warning toast (using custom styling for warning appearance)
  warning: (message: string, options: ToastOptions = {}) => {
    return toast(message, {
      duration: options.duration || 4000,
      position: options.position,
      style: {
        background: "#f59e0b",
        color: "#ffffff",
        ...options.style,
      },
      className: options.className,
      icon: options.icon || "⚠️",
      id: options.id,
    });
  },

  // Loading toast
  loading: (message: string, options: ToastOptions = {}) => {
    return toast.loading(message, {
      duration: options.duration,
      position: options.position,
      style: options.style,
      className: options.className,
      id: options.id,
    });
  },

  // Promise toast (equivalent to sonner's toast.promise)
  promise: <T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    },
    options: ToastOptions = {}
  ) => {
    return toast.promise(promise, messages, {
      position: options.position,
      style: options.style,
      className: options.className,
      id: options.id,
      success: {
        duration: options.duration || 3000,
      },
      error: {
        duration: options.duration || 5000,
      },
    });
  },

  // Dismiss toast
  dismiss: (toastId?: string) => {
    if (toastId) {
      toast.dismiss(toastId);
    } else {
      toast.dismiss();
    }
  },

  // Remove toast (instant removal)
  remove: (toastId?: string) => {
    if (toastId) {
      toast.remove(toastId);
    } else {
      toast.remove();
    }
  },

  // Custom toast with JSX content
  custom: (content: React.ReactElement, options: ToastOptions = {}) => {
    return toast.custom(content, {
      duration: options.duration,
      position: options.position,
      id: options.id,
    });
  },
};

// Export default for backward compatibility
export default toastNotification;

// Also export individual methods for convenience
export const {
  message,
  success,
  error,
  info,
  warning,
  loading,
  promise,
  dismiss,
  remove,
  custom,
} = toastNotification; 
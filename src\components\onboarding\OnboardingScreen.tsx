
import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

export interface OnboardingScreenProps {
  image: string;
  title: string;
  description: string;
  currentStep: number;
  totalSteps: number;
  onNext: () => void;
  onPrevious: () => void;
  onSkip: () => void;
  onComplete: () => void;
}

const OnboardingScreen: React.FC<OnboardingScreenProps> = ({
  image,
  title,
  description,
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  onSkip,
  onComplete,
}) => {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="flex flex-col h-full"
    >
      <div className="flex justify-between items-center p-4">
        <div className="flex space-x-1">
          {Array.from({ length: totalSteps }).map((_, index) => (
            <div
              key={index}
              className={`h-1 w-8 rounded-full ${
                index <= currentStep ? 'bg-brand-primary' : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
        <Button
          variant="ghost"
          className="text-gray-600"
          onClick={onSkip}
        >
          Salta
        </Button>
      </div>

      <div className="flex-1 flex flex-col items-center justify-center p-6 text-center">
        <div className="mb-8 w-full max-w-xs">
          <img
            src={image}
            alt={title}
            className="w-full h-auto object-contain mx-auto"
          />
        </div>
        <h1 className="text-2xl font-bold mb-3 text-gray-800">{title}</h1>
        <p className="text-gray-600 mb-8 max-w-xs">{description}</p>
      </div>

      <div className="p-6 flex justify-between items-center">
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={isFirstStep}
          className={`${isFirstStep ? 'invisible' : ''}`}
        >
          <ChevronLeft className="mr-1 h-4 w-4" />
          Indietro
        </Button>
        
        <Button
          onClick={isLastStep ? onComplete : onNext}
          className="bg-brand-primary hover:bg-brand-secondary text-white"
        >
          {isLastStep ? 'Inizia' : 'Continua'}
          {!isLastStep && <ChevronRight className="ml-1 h-4 w-4" />}
        </Button>
      </div>
    </motion.div>
  );
};

export default OnboardingScreen;

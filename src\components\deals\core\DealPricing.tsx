import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface DealPricingProps {
  originalPrice: string;
  discountPercentage: string;
  discountedPrice: string;
  onOriginalPriceChange: (value: string) => void;
  onDiscountPercentageChange: (value: string) => void;
  onDiscountedPriceChange: (value: string) => void;
}

const DealPricing = ({
  originalPrice,
  discountPercentage,
  discountedPrice,
  onOriginalPriceChange,
  onDiscountPercentageChange,
  onDiscountedPriceChange,
}: DealPricingProps) => {
  const [inputMode, setInputMode] = useState<"percentage" | "price">("percentage");

  // Validation states
  const isOriginalPriceValid = originalPrice && parseFloat(originalPrice) > 0;
  const isDiscountPercentageValid = discountPercentage && parseFloat(discountPercentage) > 0 && parseFloat(discountPercentage) <= 100;
  const isDiscountedPriceValid = discountedPrice && parseFloat(discountedPrice) > 0 && 
    parseFloat(discountedPrice) < parseFloat(originalPrice || "0") && 
    parseFloat(discountedPrice) !== parseFloat(originalPrice || "0");

  // Handle discount percentage change - calculates discounted price
  const handleDiscountPercentageChange = (value: string) => {
    onDiscountPercentageChange(value);
    if (originalPrice && value) {
      const original = parseFloat(originalPrice);
      const discount = parseFloat(value);
      if (!isNaN(original) && !isNaN(discount) && original > 0) {
        const calculated = (original - (original * discount) / 100).toFixed(2);
        onDiscountedPriceChange(calculated);
      }
    }
  };

  // Handle discounted price change - calculates discount percentage
  const handleDiscountedPriceChange = (value: string) => {
    onDiscountedPriceChange(value);
    if (originalPrice && value) {
      const original = parseFloat(originalPrice);
      const discounted = parseFloat(value);
      if (!isNaN(original) && !isNaN(discounted) && original > 0 && discounted < original) {
        const calculated = (((original - discounted) / original) * 100).toFixed(2);
        onDiscountPercentageChange(calculated);
      }
    }
  };

  // Get input border class based on validation
  const getInputBorderClass = (isValid: boolean, hasValue: boolean) => {
    if (!hasValue) return "border-border";
    return isValid ? "border-green-500" : "border-red-500";
  };

  const calculatedSavings = originalPrice && discountedPrice 
    ? (parseFloat(originalPrice) - parseFloat(discountedPrice)).toFixed(2)
    : "0.00";

  return (
    <div className="space-y-6">
      {/* Original Price - Always visible */}
      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          Prezzo Originale *
        </label>
        <input
          type="number"
          step="0.01"
          min="0"
          required
          value={originalPrice}
          onChange={(e) => onOriginalPriceChange(e.target.value)}
          className={cn(
            "w-full px-4 py-3 rounded-lg border focus:ring-2 focus:ring-primary focus:border-primary transition-colors",
            getInputBorderClass(isOriginalPriceValid, !!originalPrice)
          )}
          placeholder="0.00"
        />
      </div>

      {/* Toggle between percentage and price input */}
      <div className="w-full">
        <label className="block text-sm font-medium text-foreground mb-2">
          Metodo di Sconto
        </label>
        <ToggleGroup
          className="w-full grid grid-cols-2"
          variant="outline"
          type="single"
          value={inputMode}
          onValueChange={(value) => value && setInputMode(value as "percentage" | "price")}
        >
          <ToggleGroupItem
            value="percentage"
            aria-label="Inserisci percentuale di sconto"
            className="data-[state=on]:bg-primary data-[state=on]:text-primary-foreground"
          >
            <span>Percentuale (%)</span>
          </ToggleGroupItem>
          <ToggleGroupItem
            value="price"
            aria-label="Inserisci prezzo scontato"
            className="data-[state=on]:bg-primary data-[state=on]:text-primary-foreground"
          >
            <span>Prezzo Scontato</span>
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      {/* Input field based on selected mode */}
      {inputMode === "percentage" ? (
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Percentuale di Sconto *
          </label>
          <div className="relative">
            <input
              type="number"
              step="0.01"
              min="0"
              max="100"
              required
              value={discountPercentage}
              onChange={(e) => handleDiscountPercentageChange(e.target.value)}
              className={cn(
                "w-full px-4 py-3 pr-12 rounded-lg border focus:ring-2 focus:ring-primary focus:border-primary transition-colors",
                getInputBorderClass(isDiscountPercentageValid, !!discountPercentage)
              )}
              placeholder="0"
              disabled={!isOriginalPriceValid}
            />
            <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-muted-foreground">
              %
            </span>
          </div>
          {discountedPrice && (
            <p className="text-sm text-muted-foreground mt-1">
              Prezzo scontato: €{discountedPrice}
            </p>
          )}
        </div>
      ) : (
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Prezzo Scontato *
          </label>
          <input
            type="number"
            step="0.01"
            min="0"
            max={originalPrice || undefined}
            required
            value={discountedPrice}
            onChange={(e) => handleDiscountedPriceChange(e.target.value)}
            className={cn(
              "w-full px-4 py-3 rounded-lg border focus:ring-2 focus:ring-primary focus:border-primary transition-colors",
              getInputBorderClass(isDiscountedPriceValid, !!discountedPrice)
            )}
            placeholder="0.00"
            disabled={!isOriginalPriceValid}
          />
          {discountPercentage && (
            <p className="text-sm text-muted-foreground mt-1">
              Sconto: {discountPercentage}%
            </p>
          )}
        </div>
      )}

      {/* Summary */}
      {isOriginalPriceValid && (discountPercentage || discountedPrice) && (
        <div className="p-4 bg-muted/50 rounded-lg border border-border">
          <h4 className="text-sm font-semibold text-foreground mb-2">
            Riepilogo Offerta
          </h4>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Prezzo originale:</span>
              <span className="text-sm font-medium">€{originalPrice}</span>
            </div>
            {discountedPrice && (
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Prezzo scontato:</span>
                <span className="text-sm font-medium text-primary">€{discountedPrice}</span>
              </div>
            )}
            {discountPercentage && (
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Sconto:</span>
                <span className="text-sm font-medium text-green-600">{discountPercentage}%</span>
              </div>
            )}
            <div className="flex justify-between border-t pt-1">
              <span className="text-sm font-medium text-foreground">Risparmio:</span>
              <span className="text-sm font-bold text-green-600">€{calculatedSavings}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DealPricing;
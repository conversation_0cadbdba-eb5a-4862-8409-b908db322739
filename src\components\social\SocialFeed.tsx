import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Heart, MessageSquare, Share2, Clock, MapPin, Users, ExternalLink, User, Trash2 } from 'lucide-react';
import { useSocialFeed } from '@/hooks/social/useSocialFeed';
import { useDeleteShare } from '@/hooks/social/useDeleteShare';
import { useAuth } from '@/hooks/auth/useAuth';
import { formatDistanceToNow } from 'date-fns';
import { it } from 'date-fns/locale';
import { useNavigate } from 'react-router-dom';
import { formatCurrency } from '@/lib/format-currency';

export function SocialFeed() {
  const { data: activities, isLoading, error } = useSocialFeed();
  const { user } = useAuth();
  const navigate = useNavigate();
  const deleteShare = useDeleteShare();

  const handleDeleteShare = (activity: any) => {
    deleteShare.mutate({
      id: activity.id,
      type: activity.type,
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-muted rounded-full" />
                <div className="space-y-2 flex-1">
                  <div className="h-4 bg-muted rounded w-1/4" />
                  <div className="h-3 bg-muted rounded w-1/3" />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="h-4 bg-muted rounded w-3/4" />
                <div className="h-20 bg-muted rounded" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">Errore nel caricamento del feed</p>
        </CardContent>
      </Card>
    );
  }

  if (!activities || activities.length === 0) {
    return (
      <div className="text-center space-y-8">
        {/* Main Empty State Card */}
        <Card className="bg-gradient-to-br from-muted/30 to-muted/10 border-dashed border-2 border-muted-foreground/20 overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 opacity-50" />
          <CardContent className="p-12 relative">
            <div className="space-y-6">
              {/* Icon with animation */}
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full mx-auto flex items-center justify-center">
                  <Users className="h-10 w-10 text-muted-foreground" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center animate-pulse">
                  <Heart className="h-4 w-4 text-primary-foreground" />
                </div>
              </div>
              
              <div className="space-y-3">
                <h3 className="text-2xl font-bold text-foreground">Il tuo feed è vuoto</h3>
                <p className="text-muted-foreground text-lg max-w-md mx-auto">
                  Unisciti ai gruppi per scoprire offerte incredibili condivise dai tuoi amici!
                </p>
              </div>

              {/* Action buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
                <Button 
                  onClick={() => navigate('/groups')} 
                  className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300"
                  size="lg"
                >
                  <Users className="h-5 w-5 mr-2" />
                  Esplora i gruppi
                </Button>
                <Button 
                  variant="outline" 
                  className="border-primary/30 text-primary hover:bg-primary/5"
                  size="lg"
                >
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Invita amici
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Feature highlights */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
            <CardContent className="p-6 text-center">
              <Share2 className="h-8 w-8 text-primary mx-auto mb-3" />
              <h4 className="font-semibold text-foreground mb-2">Condividi offerte</h4>
              <p className="text-sm text-muted-foreground">Aiuta i tuoi amici a risparmiare</p>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-br from-secondary/5 to-secondary/10 border-secondary/20">
            <CardContent className="p-6 text-center">
              <Clock className="h-8 w-8 text-secondary mx-auto mb-3" />
              <h4 className="font-semibold text-foreground mb-2">Aggiornamenti real-time</h4>
              <p className="text-sm text-muted-foreground">Non perdere mai un'offerta</p>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-br from-accent/5 to-accent/10 border-accent/20">
            <CardContent className="p-6 text-center">
              <MapPin className="h-8 w-8 text-accent mx-auto mb-3" />
              <h4 className="font-semibold text-foreground mb-2">Vicino a te</h4>
              <p className="text-sm text-muted-foreground">Scopri offerte nella tua zona</p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {activities.map((activity) => {
        const isOwnShare = activity.shared_by === user?.id;
        
        return (
        <Card key={activity.id} className={`overflow-hidden hover:shadow-lg transition-all duration-300 border-border/50 ${
          isOwnShare 
            ? 'bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20' 
            : 'bg-gradient-to-br from-background to-muted/20'
        }`}>
          <CardHeader className={`pb-3 ${isOwnShare ? 'bg-gradient-to-r from-primary/20 to-primary/5' : 'bg-gradient-to-r from-muted/30 to-transparent'}`}>
             <div className="flex items-center space-x-3">
                <div className="relative">
                  <Avatar className={`h-12 w-12 ring-2 ${isOwnShare ? 'ring-primary/40' : 'ring-primary/20'}`}>
                    <AvatarImage src={activity.user_avatar} />
                    <AvatarFallback className="bg-gradient-to-br from-primary/20 to-secondary/20 text-foreground font-semibold">
                      {activity.user_name?.charAt(0)?.toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full flex items-center justify-center border-2 border-background ${
                    isOwnShare 
                      ? 'bg-gradient-to-br from-green-500 to-green-600' 
                      : 'bg-gradient-to-br from-primary to-primary/80'
                  }`}>
                    {isOwnShare ? (
                      <User className="h-3 w-3 text-white" />
                    ) : (
                      <Share2 className="h-3 w-3 text-primary-foreground" />
                    )}
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 flex-wrap">
                    <p className="font-semibold text-foreground truncate">
                      {isOwnShare ? 'Tu' : activity.user_name}
                    </p>
                    {isOwnShare && (
                      <Badge variant="outline" className="text-xs bg-gradient-to-r from-green-50 to-green-100 text-green-700 border-green-200">
                        Le tue condivisioni
                      </Badge>
                    )}
                    <span className="text-muted-foreground">•</span>
                    <Badge variant="secondary" className="text-xs bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20">
                      {activity.group_name}
                    </Badge>
                  </div>
                 <div className="flex items-center gap-1 text-sm text-muted-foreground mt-1">
                   <Clock className="h-3 w-3" />
                   {formatDistanceToNow(new Date(activity.created_at), { 
                     addSuffix: true, 
                     locale: it 
                   })}
                 </div>
               </div>
               {isOwnShare && (
                 <Button
                   variant="ghost"
                   size="sm"
                   onClick={(e) => {
                     e.stopPropagation();
                     handleDeleteShare(activity);
                   }}
                   disabled={deleteShare.isPending}
                   className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive hover:bg-destructive/10 transition-colors"
                 >
                   <Trash2 className="h-4 w-4" />
                 </Button>
               )}
             </div>
            
            {activity.message && (
              <div className="mt-3 p-3 bg-gradient-to-r from-muted/30 to-muted/10 rounded-lg border border-border/30">
                <p className="text-foreground italic">"{activity.message}"</p>
              </div>
            )}
          </CardHeader>

          <CardContent className="pt-0">
            {activity.type === 'deal_share' && activity.deal && (
              <div 
                className="border border-border/30 rounded-xl p-4 bg-gradient-to-br from-background to-muted/10 cursor-pointer hover:bg-gradient-to-br hover:from-muted/20 hover:to-muted/30 transition-all duration-300 hover:shadow-md group"
                onClick={() => navigate(`/deals/${activity.deal.id}`)}
              >
                <div className="flex gap-4">
                  {activity.deal.images && activity.deal.images[0] && (
                    <div className="relative overflow-hidden rounded-lg">
                      <img
                        src={activity.deal.images[0]}
                        alt={activity.deal.title}
                        className="w-20 h-20 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-tr from-transparent to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-foreground truncate group-hover:text-primary transition-colors">
                      {activity.deal.title}
                    </h4>
                    <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                      {activity.deal.description}
                    </p>
                    <div className="flex items-center gap-4 mt-3">
                      <div className="flex items-center gap-2">
                        <span className="text-xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                          {formatCurrency(activity.deal.discounted_price)}
                        </span>
                        {activity.deal.original_price > activity.deal.discounted_price && (
                          <span className="text-sm text-muted-foreground line-through">
                            {formatCurrency(activity.deal.original_price)}
                          </span>
                        )}
                        <Badge variant="outline" className="text-xs bg-gradient-to-r from-green-50 to-green-100 text-green-700 border-green-200">
                          -{Math.round(((activity.deal.original_price - activity.deal.discounted_price) / activity.deal.original_price) * 100)}%
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground mt-2">
                      <MapPin className="h-3 w-3" />
                      {activity.deal.business_name}
                    </div>
                  </div>
                  <div className="flex flex-col items-center justify-center text-muted-foreground group-hover:text-primary transition-colors">
                    <ExternalLink className="h-5 w-5" />
                    <span className="text-xs mt-1">Vedi</span>
                  </div>
                </div>
              </div>
            )}

            {activity.type === 'booking_share' && activity.booking && (
              <div 
                className="border border-border rounded-lg p-4 bg-muted/30 cursor-pointer hover:bg-muted/50 transition-colors"
                onClick={() => navigate(`/bookings/${activity.booking.id}`)}
              >
                <div className="flex gap-4">
                  {activity.booking.deal_images && activity.booking.deal_images[0] && (
                    <img
                      src={activity.booking.deal_images[0]}
                      alt={activity.booking.deal_title}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                  )}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="outline" className="text-xs">
                        Prenotazione
                      </Badge>
                      <Badge 
                        variant={
                          activity.booking.status === 'confirmed' ? 'default' :
                          activity.booking.status === 'pending' ? 'secondary' : 'destructive'
                        }
                        className="text-xs"
                      >
                        {activity.booking.status === 'confirmed' ? 'Confermata' :
                         activity.booking.status === 'pending' ? 'In attesa' : 'Annullata'}
                      </Badge>
                    </div>
                    <h4 className="font-semibold text-foreground truncate">
                      {activity.booking.deal_title}
                    </h4>
                    <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {new Date(activity.booking.booking_date).toLocaleDateString('it-IT')} alle {activity.booking.booking_time}
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {activity.booking.business_name}
                      </div>
                    </div>
                  </div>
                  <ExternalLink className="h-4 w-4 text-muted-foreground shrink-0" />
                </div>
              </div>
            )}

          </CardContent>
        </Card>
        );
      })}
    </div>
  );
}
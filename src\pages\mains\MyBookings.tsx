
import { useState } from "react";
import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";

import BookingsTabs from "@/components/bookings/BookingsTabs";
import { useBookings } from "@/hooks/booking/useBookings";
import { useBusinessMode } from "@/hooks/useBusinessMode";
import { motion } from "framer-motion";
import { Search, Filter } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ShareBookingDialog } from "@/components/sharing/ShareBookingDialog";
import type { Booking } from "@/types/booking";
import UnifiedHeader from "@/components/toolbars/UnifiedHeader";

const MyBookings = () => {
  const [activeTab, setActiveTab] = useState<'upcoming' | 'past' | 'cancelled'>('upcoming');
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedBookingForShare, setSelectedBookingForShare] = useState<Booking | null>(null);
  const { isLoading, upcomingBookings, pastBookings, cancelledBookings } = useBookings();
  const { isBusinessMode } = useBusinessMode();

  const handleShareClick = (booking: Booking) => {
    setSelectedBookingForShare(booking);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
        <UnifiedHeader title="Le Mie Prenotazioni" isBusiness={isBusinessMode}/>
        <main className="pt-20 pb-20 px-4">
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-card rounded-xl p-4 animate-pulse">
                <div className="flex gap-4">
                  <div className="w-16 h-16 bg-muted rounded-lg" />
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-muted rounded w-3/4" />
                    <div className="h-3 bg-muted rounded w-1/2" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </main>
        <BottomNavigationBar isBusiness={isBusinessMode} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <UnifiedHeader title="Le Mie Prenotazioni" isBusiness={isBusinessMode}/>

      <main className="pt-20 pb-20">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="px-4 mb-6"
        >
          {/* Search and Filter Bar */}
          <div className="flex gap-3 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input 
                placeholder="Cerca prenotazioni..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-card border-border/50 focus:border-primary/50"
              />
            </div>
            <Button 
              variant="outline" 
              size="icon" 
              className="bg-card border-border/50 hover:bg-accent"
            >
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </motion.div>

        <div className="px-4">
          <BookingsTabs 
            activeTab={activeTab} 
            onTabChange={value => setActiveTab(value as 'upcoming' | 'past' | 'cancelled')} 
            upcomingBookings={upcomingBookings} 
            pastBookings={pastBookings}
            cancelledBookings={cancelledBookings}
            searchQuery={searchQuery}
            onShareClick={handleShareClick}
          />
        </div>
      </main>

      <ShareBookingDialog
        open={!!selectedBookingForShare}
        onOpenChange={(open) => !open && setSelectedBookingForShare(null)}
        bookingId={selectedBookingForShare?.id || ''}
        dealTitle={selectedBookingForShare?.deals?.title || 'Prenotazione'}
      />

      <BottomNavigationBar isBusiness={isBusinessMode} />
    </div>
  );
};

export default MyBookings;

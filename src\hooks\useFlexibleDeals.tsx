import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/auth/useAuth";
import { useCategoryQuery } from "@/queries/useCategoryQuery";

export type DealCollectionType = 
  | 'categories'
  | 'weekend'
  | 'featured'
  | 'nearby'
  | 'trending'
  | 'ending_soon'
  | 'high_discount';

interface DealItem {
  id: string;
  title: string;
  discounted_price: number;
  original_price: number;
  discount_percentage: number;
  images: string[];
  businesses: {
    name: string;
    city?: string;
  };
}

interface DealSection {
  id: string;
  name: string;
  icon?: string;
  iconType?: 'iconify' | 'lucide';
  deals: DealItem[];
  viewAllUrl?: string;
}

export const useFlexibleDeals = (
  collectionType: DealCollectionType,
  options: {
    maxSections?: number;
    dealsPerSection?: number;
    userPreferences?: boolean;
  } = {}
) => {
  const { user } = useAuth();
  const [sections, setSections] = useState<DealSection[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { data: allCategories, isLoading: isLoadingCategories } = useCategoryQuery();

  const {
    maxSections = 6,
    dealsPerSection = 10,
    userPreferences = true
  } = options;

  useEffect(() => {
    const fetchDeals = async () => {
      if (collectionType === 'categories' && (isLoadingCategories || !allCategories)) {
        return;
      }

      setIsLoading(true);
      
      try {
        let sectionsData: DealSection[] = [];

        switch (collectionType) {
          case 'categories':
            sectionsData = await fetchCategorySections();
            break;
          case 'weekend':
            sectionsData = await fetchWeekendDeals();
            break;
          case 'featured':
            sectionsData = await fetchFeaturedDeals();
            break;
          case 'ending_soon':
            sectionsData = await fetchEndingSoonDeals();
            break;
          case 'high_discount':
            sectionsData = await fetchHighDiscountDeals();
            break;
          default:
            sectionsData = [];
        }

        setSections(sectionsData.filter(section => section.deals.length > 0));
      } catch (error) {
        console.error(`Error fetching ${collectionType} deals:`, error);
        setSections([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDeals();
  }, [collectionType, user, isLoadingCategories, allCategories, userPreferences]);

  const fetchCategorySections = async (): Promise<DealSection[]> => {
    if (!allCategories) return [];

    let categories = [...allCategories];
    
    // Apply user preferences if enabled
    if (userPreferences && user) {
      const { data: userPrefs } = await supabase
        .from("user_preferences")
        .select("*")
        .eq("user_id", user.id)
        .maybeSingle();
        
      if (userPrefs && userPrefs.categories) {
        const preferredCategories = userPrefs.categories
          .map((id: string) => allCategories.find(cat => cat.id === id))
          .filter(Boolean);
        
        if (preferredCategories.length > 0) {
          categories = preferredCategories;
        }
      }
    }

    const randomizedCategories = categories
      .sort(() => 0.5 - Math.random())
      .slice(0, maxSections);

    const sectionsData = await Promise.all(
      randomizedCategories.map(async (category) => {
        const { data: deals } = await supabase
          .from("deals")
          .select(`
            id,
            title,
            discounted_price,
            original_price,
            discount_percentage,
            images,
            businesses!inner(name, city)
          `)
          .eq("status", "published")
          .eq("businesses.category_id", category.id)
          .gte("end_date", new Date().toISOString())
          .order("created_at", { ascending: false })
          .limit(dealsPerSection);

        return {
          id: category.id,
          name: category.name || "",
          icon: category.icon || "",
          iconType: 'iconify' as const,
          deals: deals || [],
          viewAllUrl: `/deals?category=${category.id}`
        };
      })
    );

    return sectionsData;
  };

  const fetchWeekendDeals = async (): Promise<DealSection[]> => {
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 = Sunday, 6 = Saturday
    
    // Only show weekend deals on Friday, Saturday, and Sunday
    if (dayOfWeek < 5 && dayOfWeek > 0) {
      return [];
    }

    const { data: deals } = await supabase
      .from("deals")
      .select(`
        id,
        title,
        discounted_price,
        original_price,
        discount_percentage,
        images,
        businesses!inner(name, city)
      `)
      .eq("status", "published")
      .gte("end_date", new Date().toISOString())
      .gte("discount_percentage", 20) // Weekend deals should have good discounts
      .order("discount_percentage", { ascending: false })
      .limit(dealsPerSection);

    return [{
      id: 'weekend',
      name: 'Offerte Weekend',
      icon: 'mdi:calendar-weekend',
      iconType: 'iconify',
      deals: deals || [],
      viewAllUrl: '/deals?filter=weekend'
    }];
  };

  const fetchFeaturedDeals = async (): Promise<DealSection[]> => {

    // const { data: deals } = await supabase
    //   .from("deals")
    //   .select(`
    //     id,
    //     title,
    //     discounted_price,
    //     original_price,
    //     discount_percentage,
    //     images,
    //     businesses!inner(name, city, score)
    //   `)
    //   .eq("status", "published")
    //   .gte("end_date", new Date().toISOString())
    //   .gte("businesses.score", 4.0) // High-rated businesses
    //   .order("businesses.score", { ascending: false })
    //   .limit(dealsPerSection);

// TODO we can use deals_with_info to get the deals
// const { data: deals } = await supabase
// .from("deals_with_info")
// .select(`
//   id,
//   title,
//   discounted_price,
//   original_price,
//   discount_percentage,
//   images,

//   business_name,

//   business_city,
//   business_score
// `)
// .eq("status", "published")
// .gte("end_date", new Date().toISOString())
// .gte("business_score", 4.0) // High-rated businesses
// .order("business_score", { ascending: false })
// .limit(dealsPerSection);

// // TODO we can use deals_with_info to get the deals


// const returnDeals = deals?.map((deal: any) => ({
// ...deal,
// businesses: {
// name: deal.business_name,
// city: deal.business_city
// }
// }));

    const { data: businesses } = await supabase
      .from("businesses")
      .select(`
        id,
        name,
        city,
        score,
        deals!inner(
          id,
          title,
          discounted_price,
          original_price,
          discount_percentage,
          images
        )
      `)
      .eq("deals.status", "published")
      .gte("deals.end_date", new Date().toISOString())
      .gte("score", 4.0) // High-rated businesses
      .order("score", { ascending: false })
      .limit(dealsPerSection);

    // Transform the data to match the expected format
    const deals = businesses?.flatMap(business => 
      business.deals?.map(deal => ({
        ...deal,
        businesses: {
          name: business.name,
          city: business.city
        }
      })) || []
    ) || [];

    return [{
      id: 'featured',
      name: 'Offerte in Evidenza',
      icon: 'mdi:star',
      iconType: 'iconify',
      deals: deals,
      viewAllUrl: '/deals?filter=featured'
    }];
  };

  const fetchEndingSoonDeals = async (): Promise<DealSection[]> => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 2);

    const { data: deals } = await supabase
      .from("deals")
      .select(`
        id,
        title,
        discounted_price,
        original_price,
        discount_percentage,
        images,
        businesses!inner(name, city)
      `)
      .eq("status", "published")
      .gte("end_date", new Date().toISOString())
      .lte("end_date", tomorrow.toISOString())
      .order("end_date", { ascending: true })
      .limit(dealsPerSection);

    return [{
      id: 'ending_soon',
      name: 'Scadono Presto',
      icon: 'mdi:clock-alert',
      iconType: 'iconify',
      deals: deals || [],
      viewAllUrl: '/deals?filter=ending_soon'
    }];
  };

  const fetchHighDiscountDeals = async (): Promise<DealSection[]> => {
    const { data: deals } = await supabase
      .from("deals")
      .select(`
        id,
        title,
        discounted_price,
        original_price,
        discount_percentage,
        images,
        businesses!inner(name, city)
      `)
      .eq("status", "published")
      .gte("end_date", new Date().toISOString())
      .gte("discount_percentage", 40) // High discount threshold
      .order("discount_percentage", { ascending: false })
      .limit(dealsPerSection);

    return [{
      id: 'high_discount',
      name: 'Super Sconti',
      icon: 'mdi:percent',
      iconType: 'iconify',
      deals: deals || [],
      viewAllUrl: '/deals?filter=high_discount'
    }];
  };

  return {
    sections,
    isLoading,
    refetch: () => {
      setIsLoading(true);
      // Trigger re-fetch by updating dependencies
    }
  };
};
import { Star } from "lucide-react";
import FlexibleDealCarousel from "./FlexibleDealCarousel";
import { useFlexibleDeals } from "@/hooks/useFlexibleDeals";

interface FeaturedDealsCarouselProps {
  className?: string;
}

const FeaturedDealsCarousel = ({ className = "" }: FeaturedDealsCarouselProps) => {
  const { sections, isLoading } = useFlexibleDeals('featured', {
    maxSections: 1,
    dealsPerSection: 12,
    userPreferences: false
  });

  return (
    <FlexibleDealCarousel
      title="Offerte in Evidenza"
      titleIcon={Star}
      sections={sections}
      isLoading={isLoading}
      className={className}
    />
  );
};

export default FeaturedDealsCarousel;
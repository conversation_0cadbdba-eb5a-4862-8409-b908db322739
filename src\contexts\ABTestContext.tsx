import React, { createContext, useContext, useEffect, useState } from 'react';

// Define the color palette variants
export type ColorPalette = 'default' | 'alternative';

// Define the test variants
export interface ABTestVariants {
  colorPalette: ColorPalette;
}

// Define the context value type
interface ABTestContextValue {
  variants: ABTestVariants;
  trackEvent: (eventName: string, properties?: Record<string, any>) => void;
  trackConversion: (conversionType: string, value?: number) => void;
  setColorPalette: (palette: ColorPalette) => void;
}

// Create the context with a default value
const ABTestContext = createContext<ABTestContextValue | undefined>(undefined);

// Define props for the provider component
interface ABTestProviderProps {
  children: React.ReactNode;
}

// Create a provider component
export const ABTestProvider: React.FC<ABTestProviderProps> = ({ children }) => {
  // State to store the assigned variants
  const [variants, setVariants] = useState<ABTestVariants>({
    colorPalette: 'default',
  });

  // Initialize the A/B test on component mount
  useEffect(() => {
    // Check if the user already has an assigned variant stored
    const storedVariant = localStorage.getItem('ab_test_color_palette');

    if (storedVariant && (storedVariant === 'default' || storedVariant === 'alternative')) {
      // Use the stored variant if it exists
      setVariants({ colorPalette: storedVariant as ColorPalette });
    } else {
      // Randomly assign a variant (50/50 split)
      const newVariant: ColorPalette = Math.random() < 0.5 ? 'default' : 'alternative';
      localStorage.setItem('ab_test_color_palette', newVariant);
      setVariants({ colorPalette: newVariant });

      // Track the assignment event
      trackEvent('ab_test_assignment', {
        test: 'color_palette',
        variant: newVariant
      });
    }
  }, []);

  // Function to track events
  const trackEvent = (eventName: string, properties?: Record<string, any>) => {
    // Add the current variant to the properties
    const eventProperties = {
      ...properties,
      colorPalette: variants.colorPalette,
    };

    // Log the event (for development)
    console.log(`[ABTest] Event: ${eventName}`, eventProperties);

    // Here you would integrate with your analytics service
    // For example, with Google Analytics 4:
    if (typeof window !== 'undefined' && 'gtag' in window) {
      (window as any).gtag('event', eventName, eventProperties);
    }
  };

  // Function to track conversions
  const trackConversion = (conversionType: string, value?: number) => {
    trackEvent('conversion', {
      type: conversionType,
      value: value
    });
  };

  // Function to manually set the color palette
  const setColorPalette = (palette: ColorPalette) => {
    // Update the state
    setVariants({ colorPalette: palette });

    // Save to localStorage
    localStorage.setItem('ab_test_color_palette', palette);

    // Track the manual change event
    trackEvent('ab_test_manual_change', {
      test: 'color_palette',
      variant: palette
    });
  };

  // Provide the context value to children
  return (
    <ABTestContext.Provider value={{ variants, trackEvent, trackConversion, setColorPalette }}>
      {children}
    </ABTestContext.Provider>
  );
};

// Create a hook for using the context
export const useABTest = () => {
  const context = useContext(ABTestContext);
  if (context === undefined) {
    throw new Error('useABTest must be used within an ABTestProvider');
  }
  return context;
};

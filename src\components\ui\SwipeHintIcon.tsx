
import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SwipeHintIconProps {
  show: boolean;
  className?: string;
}

const SwipeHintIcon: React.FC<SwipeHintIconProps> = ({ show, className }) => {
  if (!show) return null;

  return (
    <div className={cn(
      "absolute inset-0 flex items-center justify-center pointer-events-none z-10",
      "animate-fade-in-out",
      className
    )}>
      <div className="flex items-center bg-black/20 backdrop-blur-sm rounded-full px-3 py-2 gap-1">
        <ChevronLeft className="h-4 w-4 text-white animate-pulse" />
        <span className="text-white text-xs font-medium">Scorri</span>
        <ChevronRight className="h-4 w-4 text-white animate-pulse" />
      </div>
    </div>
  );
};

export default SwipeHintIcon;

import { useState, useCallback, useEffect } from "react";
import { 
  useFilters, 
  useFilterActions, 
  useHasActiveFilters, 
  useActiveFiltersCount,
  type FilterSettings 
} from "@/stores/useFilterStore";

export const useSearchPanel = (initialFilters?: Partial<FilterSettings>) => {
  // Use Zustand store for persistent filter state
  const filters = useFilters();
  const { updateFilters: updateStoreFilters, resetFilters: resetStoreFilters } = useFilterActions();
  const hasActiveFilters = useHasActiveFilters();
  const activeFiltersCount = useActiveFiltersCount();

  // Keep panel open/close state local (shouldn't persist across navigation)
  const [isOpen, setIsOpen] = useState(false);

  // Initialize filters with provided initial values (only on first render if store is empty)
  useEffect(() => {
    if (initialFilters && !hasActiveFilters) {
      updateStoreFilters(initialFilters);
    }
  }, []);

  const openSearchPanel = useCallback(() => {
    setIsOpen(true);
  }, []);

  const closeSearchPanel = useCallback(() => {
    setIsOpen(false);
  }, []);

  const toggleSearchPanel = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  // Wrapper function to match the expected interface
  const updateFilters = useCallback((newFilters: FilterSettings) => {
    updateStoreFilters(newFilters);
    console.log('🖱️ Manual filter change detected - updating Zustand store');
  }, [updateStoreFilters]);

  // Wrapper function to match the expected interface
  const resetFilters = useCallback(() => {
    resetStoreFilters();
  }, [resetStoreFilters]);

  return {
    isOpen,
    filters,
    openSearchPanel,
    closeSearchPanel,
    toggleSearchPanel,
    updateFilters,
    resetFilters,
    hasActiveFilters,
    activeFiltersCount,
  };
}; 
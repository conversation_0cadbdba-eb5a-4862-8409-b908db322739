
@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0.5;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-left {
  from {
    transform: translateX(-100%);
    opacity: 0.5;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fade-in-out {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.animate-slide-in-right {
  animation: slide-in-right 1s ease-in-out infinite;
}

.animate-slide-in-left {
  animation: slide-in-left 1s ease-in-out infinite;
}

.animate-fade-in-out {
  animation: fade-in-out 2s ease-in-out infinite;
}

/* Touch optimization */
.touch-pan-y {
  touch-action: pan-y;
}

/* Scroll snap for mobile */
.snap-mandatory {
  scroll-snap-type: y mandatory;
}

.snap-start {
  scroll-snap-align: start;
}

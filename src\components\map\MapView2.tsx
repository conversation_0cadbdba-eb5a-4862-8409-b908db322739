import { useEffect, useRef, useState, useCallback, useMemo } from "react";
import { APIProvider, Map } from "@vis.gl/react-google-maps";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { debounce } from "lodash";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";
import { MapPin } from "lucide-react";
import "./MapView2.css";

// Importiamo i componenti estratti
import { UserLocationMarker } from "./UserLocationMarker";
import { DirectionsRenderer } from "./DirectionsRenderer";
import { MarkerClustererComponent } from "./MarkerClustererComponent";
import { MapReference } from "./MapReference";
import { NavigationControls } from "./NavigationControls";
import { NavigationDirections } from "./NavigationDirections";
import { BusinessInfoSlide } from "./BusinessInfoSlide";
import { LoadingIndicator } from "./LoadingIndicator";
import { useMapNavigation } from "./useMapNavigation";
import { getTransportIcon } from "./transportIcons";

// Importiamo i tipi estratti
import { Business, MapViewProps, mapOptions, DEFAULT_ZOOM } from "./types";

/**
 * MapView2 component displaying businesses on a map with navigation features
 */
export const MapView2 = ({
  businesses,
  onBusinessClick,
  onDealClick,
  locationEnabled = true,
}: MapViewProps) => {
  const mapRef = useRef<google.maps.Map>();
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(
    null
  );
  const [isGoogleLoaded, setIsGoogleLoaded] = useState(false);
  const [center, setCenter] = useState<{ lat: number; lng: number }>({
    lat: 45.4671,
    lng: 9.1526,
  });
  const [apiKey, setApiKey] = useState<string>("");
  const [isSlideVisible, setIsSlideVisible] = useState(false);
  const [currentBounds, setCurrentBounds] =
    useState<google.maps.LatLngBounds | null>(null);
  const [isLoadingMarkers, setIsLoadingMarkers] = useState(false);
  
  const { userLocation, demoEnabled } = useLocationManagement();

  // Utilizziamo l'hook creato per la gestione della navigazione
  const {
    navigationState,
    setNavigationState,
    showTransportMenu,
    setShowTransportMenu,
    calculateRoute,
    clearNavigation,
    formatDuration,
    formatDistance
  } = useMapNavigation(userLocation, isGoogleLoaded);

  useEffect(() => {
    if (userLocation && locationEnabled) {
      setCenter(userLocation);
    }
  }, [userLocation, locationEnabled]);

  useEffect(() => {
    const getGoogleMapsKey = async () => {
      const {
        data: { GOOGLE_MAPS_API_KEY },
        error,
      } = await supabase.functions.invoke("get-google-maps-key");
      if (error) {
        console.error("Errore nel recupero della chiave API:", error);
        return;
      }
      setApiKey(GOOGLE_MAPS_API_KEY);
    };

    getGoogleMapsKey();
  }, []);

  const handleMarkerClick = async (business: Business) => {
    if (selectedBusiness?.id !== business.id) {
      clearNavigation();
    }

    if (!business.deals || business.deals.length === 0) {
      const { data, error } = await supabase
        .from("deals")
        .select('*, businesses(*)')
        .eq("business_id", business.id)
        .eq("status", "published");

      console.log("dealsData", data);
     

      if (error) {
        console.error("Errore nel recupero delle offerte:", error);
        toast.error("Errore nel recupero delle offerte");
      } else {
        business.deals = data || [];
      }
    }

    setSelectedBusiness({ ...business });
    setIsSlideVisible(true);
    if (onBusinessClick) {
      onBusinessClick(business);
    }
  };

  const handleBoundsChanged = useCallback(
    debounce((map: google.maps.Map) => {
      if (map && isGoogleLoaded) {
        setIsLoadingMarkers(true);
        const bounds = map.getBounds();
        if (bounds) {
          setCurrentBounds(bounds);
        }
        setTimeout(() => {
          setIsLoadingMarkers(false);
        }, 500);
      }
    }, 300),
    [isGoogleLoaded]
  );

  const visibleBusinesses = useMemo(() => {
    if (!currentBounds || !isGoogleLoaded) return [];

    return businesses.filter((business) => {
      if (!business.latitude || !business.longitude) return false;

      return currentBounds.contains(
        new google.maps.LatLng(business.latitude, business.longitude)
      );
    });
  }, [businesses, currentBounds, isGoogleLoaded]);

  const handleMapLoad = useCallback((map: google.maps.Map) => {
    mapRef.current = map;
    if (map.getBounds()) {
      setCurrentBounds(map.getBounds());
    }
    setIsGoogleLoaded(true);
    
    map.addListener('bounds_changed', () => {
      if (map && isGoogleLoaded) {
        setIsLoadingMarkers(true);
        const bounds = map.getBounds();
        if (bounds) {
          setCurrentBounds(bounds);
        }
        setTimeout(() => {
          setIsLoadingMarkers(false);
        }, 1500);
      }
    });
  }, [isGoogleLoaded]);

  const handleDealClick = (dealId: string) => {
    if (onDealClick) {
      onDealClick(dealId);
    }
  };

  if (!apiKey) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center h-[calc(100vh-120px)]">
          <div className="animate-spin">
            <MapPin className="h-8 w-8 text-brand-primary" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      <APIProvider apiKey={apiKey} onLoad={() => setIsGoogleLoaded(true)}>
        <Map
          mapId="google-map"
          defaultZoom={DEFAULT_ZOOM}
          defaultCenter={center}
          mapTypeId="roadmap"
          gestureHandling="greedy"
          disableDefaultUI={true}
          styles={mapOptions.styles}
          onBoundsChanged={(e) => {
            const bounds = e.detail.bounds;
            if (bounds) {
              setCurrentBounds(new google.maps.LatLngBounds(
                new google.maps.LatLng(bounds.south, bounds.west),
                new google.maps.LatLng(bounds.north, bounds.east)
              ));
              setIsLoadingMarkers(true);
              setTimeout(() => {
                setIsLoadingMarkers(false);
              }, 1500);
            }
          }}
        >
          {/* Reference the map to save it once mounted */}
          <MapReference onLoad={handleMapLoad} />

          {!navigationState.isActive && isGoogleLoaded && (
            <MarkerClustererComponent 
              businesses={visibleBusinesses} 
              onMarkerClick={(handleMarkerClick)}
              isGoogleLoaded={isGoogleLoaded}
              onDealClick={handleDealClick}
            />
          )}

          {userLocation && isGoogleLoaded && (
            <UserLocationMarker position={userLocation} />
          )}

          {navigationState.directions && (
            <DirectionsRenderer 
              directions={navigationState.directions} 
            />
          )}
        </Map>
      </APIProvider>

      <LoadingIndicator isLoading={isLoadingMarkers} />

      <NavigationControls 
        selectedBusiness={selectedBusiness}
        navigationState={navigationState}
        showTransportMenu={showTransportMenu}
        setShowTransportMenu={setShowTransportMenu}
        calculateRoute={calculateRoute}
        getTransportIcon={getTransportIcon}
        formatDuration={formatDuration}
      />

      <NavigationDirections 
        navigationState={navigationState}
        setNavigationState={setNavigationState}
        getTransportIcon={getTransportIcon}
        formatDuration={formatDuration}
        formatDistance={formatDistance}
      />

      <BusinessInfoSlide
        selectedBusiness={selectedBusiness}
        isSlideVisible={isSlideVisible}
        setIsSlideVisible={setIsSlideVisible}
        clearNavigation={clearNavigation}
        handleDealClick={handleDealClick}
      />
    </div>
  );
};


import React, { useEffect, useRef } from "react";
import { useMap, useMapsLibrary } from "@vis.gl/react-google-maps";

/**
 * Directions component for showing routes
 */
export const DirectionsRenderer = ({ 
  directions, 
  onDirectionsRender
}: { 
  directions: google.maps.DirectionsResult | null,
  onDirectionsRender?: () => void
}) => {
  const map = useMap();
  const routesLib = useMapsLibrary("routes");
  const rendererRef = useRef<google.maps.DirectionsRenderer | null>(null);

  useEffect(() => {
    if (!routesLib || !map || !directions) return;

    if (!rendererRef.current) {
      if (window.google && window.google.maps) {
        rendererRef.current = new window.google.maps.DirectionsRenderer({
          suppressMarkers: false,
          polylineOptions: {
            strokeColor: "#4F46E5",
            strokeWeight: 6,
            strokeOpacity: 0.8,
          },
          markerOptions: {
            zIndex: 2000,
          },
          preserveViewport: false,
        });
      }
    }

    if (rendererRef.current) {
      rendererRef.current.setMap(map);
      rendererRef.current.setDirections(directions);
      
      if (onDirectionsRender) {
        onDirectionsRender();
      }
    }

    return () => {
      if (rendererRef.current) {
        rendererRef.current.setMap(null);
      }
    };
  }, [routesLib, map, directions, onDirectionsRender]);

  return null;
};

import Hint from "@/components/Hint";
import { Deal } from "@/types/deals";
import { Store } from "lucide-react";

interface DealVisitBusinessButtonProps {
  deal:     Deal;
  navigate: (path: string) => void;
}



export function DealVisitBusinessButton({deal, navigate}: DealVisitBusinessButtonProps) {
    return (
      <Hint text={`Visita ${deal.businesses.name}`}>
      <button
        onClick={(e) => {
          e.stopPropagation();
          navigate(`/business-view/${deal.business_id}`);
        }}
        className="absolute bottom-2 right-2 p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow"
        aria-label={`Visita la pagina di ${deal.businesses.name}`}
      >
        <Store className="h-4 w-4 text-gray-500" />
      </button>
    </Hint>
  );
}
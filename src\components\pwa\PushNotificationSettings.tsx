import React from 'react';
import { usePushNotifications } from '@/hooks/pwa/usePushNotifications';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Bell, BellOff, TestTube } from 'lucide-react';

export const PushNotificationSettings: React.FC = () => {
  const {
    permission,
    isSupported,
    subscription,
    requestPermission,
    subscribe,
    unsubscribe,
    sendTestNotification,
  } = usePushNotifications();

  if (!isSupported) {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BellOff className="w-5 h-5" />
            Push Notifications
          </CardTitle>
          <CardDescription>
            Push notifications are not supported in this browser
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const getStatusText = () => {
    if (permission === 'granted' && subscription) {
      return 'Enabled - You\'ll receive notifications';
    }
    if (permission === 'granted' && !subscription) {
      return 'Permission granted - Click subscribe to enable';
    }
    if (permission === 'denied') {
      return 'Blocked - Please enable in browser settings';
    }
    return 'Click to enable push notifications';
  };

  const getStatusColor = () => {
    if (permission === 'granted' && subscription) return 'text-green-600';
    if (permission === 'denied') return 'text-red-600';
    return 'text-gray-600';
  };

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="w-5 h-5" />
          Push Notifications
        </CardTitle>
        <CardDescription>
          Get notified about bookings, deals, and messages
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Status:</span>
          <span className={`text-sm ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>

        <div className="flex gap-2">
          {permission === 'default' && (
            <Button
              onClick={requestPermission}
              className="flex-1"
            >
              Enable Notifications
            </Button>
          )}

          {permission === 'granted' && !subscription && (
            <Button
              onClick={subscribe}
              className="flex-1"
            >
              Subscribe
            </Button>
          )}

          {permission === 'granted' && subscription && (
            <Button
              onClick={unsubscribe}
              variant="outline"
              className="flex-1"
            >
              Unsubscribe
            </Button>
          )}
        </div>

        {permission === 'granted' && (
          <Button
            onClick={sendTestNotification}
            variant="secondary"
            className="w-full gap-2"
          >
            <TestTube className="w-4 h-4" />
            Send Test Notification
          </Button>
        )}

        <div className="text-xs text-gray-500">
          <p>💡 Notifications include:</p>
          <ul className="mt-1 space-y-1">
            <li>• Booking reminders</li>
            <li>• New deals near you</li>
            <li>• Chat messages</li>
            <li>• Special offers</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}; 
import { availabilityService } from "@/services/availabilityService";
import { WeeklySchedule } from "@/types/deals";
import { useEffect, useRef, useState } from "react";

/**
 * DealTimeSelector Component
 *
 * Questo componente mostra le fasce orarie disponibili per una data selezionata
 * Evidenzia la fascia oraria selezionata e mostra la disponibilità di posti
 */
interface DealTimeSelectorProps {
  /** Weekly schedule containing available time slots for each day */
  timeSlots: WeeklySchedule;
  /** Selected date in ISO format (YYYY-MM-DD) */
  selectedDate: string | null;
  /** Currently selected time slot */
  selectedTime: string | null;
  /** Callback function triggered when a time slot is selected */
  onTimeSelect: (time: string) => void;
  /** Controls whether the component is visible */
  visible: boolean;
  /** Deal ID for fetching bookings */
  dealId: string;
}

const DealTimeSelector = ({
  timeSlots,
  selectedDate,
  selectedTime,
  onTimeSelect,
  visible,
  dealId,
}: DealTimeSelectorProps) => {
  const timeSlotsRef = useRef<HTMLDivElement>(null);
  const [bookings, setBookings] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(false);

  // Fetch bookings for the selected date
  useEffect(() => {
    if (visible && selectedDate && dealId) {
      setLoading(true);
      const fetchBookings = async () => {
        const bookingsMap = await availabilityService.getTimeSlotBookings(
          dealId,
          selectedDate
        );
        setBookings(bookingsMap);
        setLoading(false);
      };

      fetchBookings();
    }
  }, [dealId, selectedDate, visible]);

  useEffect(() => {
    if (visible && timeSlotsRef.current) {
      timeSlotsRef.current.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }
  }, [visible]);

  if (!visible || !selectedDate) 
    return (
  <>
  
       <h3 className="text-sm font-semibold mb-2">Nessuna data selezionata</h3>
           <div className="flex gap-2 overflow-x-auto pb-2">
          {[1, 2].map((i) => (
            <div
              key={i}
              className="min-w-28 h-16 px-4 py-2 border rounded-xl animate-pulse bg-gray-100"
            ></div>
          ))}
          {/* <h2 className="text-sm font-semibold mb-2">Seleziona una data</h2> */}
        </div>
  </>
      );

  // Trova il giorno della settimana (1-7) dalla data selezionata
  const selectedDay = new Date(selectedDate).getDay() || 7; // Converte 0 (domenica) in 7 per compatibilità

  // Trova gli slot orari per il giorno selezionato
  const daySchedule = timeSlots.schedule.find((day) => day.day === selectedDay);
  const availableTimeSlots = daySchedule?.time_slots || [];

  if (availableTimeSlots.length === 0) {
    return (
      <div className="text-center text-gray-500 py-2">
        Nessun orario disponibile per questa data
      </div>
    );
  }

  // Helper function to safely get the slot details
  const getSlotDetails = (
    slot: any
  ): {
    startTime: string;
    endTime: string;
    available_seats: number;
  } => {
    if ("start_time" in slot) {
      return {
        startTime: slot.start_time as string,
        endTime: slot.end_time as string,
        available_seats: slot.available_seats as number,
      };
    }

    return {
      startTime: "",
      endTime: "",
      available_seats: 0,
    };
  };

  // Funzione per calcolare la percentuale di occupazione
  const calculateOccupancyPercentage = (available: number, booked: number) => {
    const total = available + booked;
    if (total === 0) return 100;
    return Math.round((booked / total) * 100);
  };

  return (
    <div ref={timeSlotsRef}>
      <h3 className="text-sm font-semibold mb-2">Orari Disponibili</h3>
      {loading ? (
        <div className="flex gap-2 overflow-x-auto pb-2">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="min-w-28 px-4 py-2 border rounded-xl animate-pulse bg-gray-100"
            ></div>
          ))}
        </div>
      ) : (
        <div className="flex gap-2 overflow-x-auto pb-2">
          {availableTimeSlots.map((slot: any) => {
            const { startTime, endTime, available_seats } =
              getSlotDetails(slot);

            // Get booked seats for this time slot from the bookings map
            // Add ":00" to match the database time format which includes seconds
            const bookedSeats = bookings[startTime + ":00"] || 0;

            // Calculate remaining seats using the service method
            const remainingSeats = availabilityService.calculateRemainingSeats(
              available_seats,
              bookedSeats
            );

            const fullyBooked = remainingSeats <= 0;
            const lowAvailability = remainingSeats <= 3 && remainingSeats > 0;
            const occupancyPercentage = calculateOccupancyPercentage(
              remainingSeats,
              bookedSeats
            );

            return (
              <button
                key={startTime}
                disabled={fullyBooked}
                onClick={() => onTimeSelect(startTime)}
                className={`min-w-28 px-4 py-2 border rounded-xl text-sm whitespace-nowrap transition-all ${
                  selectedTime === startTime
                    ? "bg-brand-primary text-white border-brand-primary"
                    : fullyBooked
                    ? "bg-gray-100 text-gray-500 border-gray-200"
                    : lowAvailability
                    ? "bg-amber-50 text-amber-700 border-amber-200"
                    : "bg-white text-gray-700 border-gray-200 hover:bg-gray-50"
                }`}
              >
                <div className="flex flex-col items-center">
                  <span className="font-medium">
                    {startTime} - {endTime}
                  </span>
                  {!fullyBooked ? (
                    <>
                      <span
                        className={`text-xs mt-1 ${
                          lowAvailability ? "text-amber-600 font-bold" : ""
                        }`}
                      >
                        {remainingSeats === 1
                          ? "Ultimo posto"
                          : `${remainingSeats} post${
                              remainingSeats > 1 ? "i" : "o"
                            }`}
                      </span>
                      {/* Indicatore grafico di disponibilità */}
                      <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                        <div
                          className={`h-1 rounded-full ${
                            lowAvailability ? "bg-amber-500" : "bg-green-500"
                          }`}
                          style={{ width: `${100 - occupancyPercentage}%` }}
                        ></div>
                      </div>
                    </>
                  ) : (
                    <span className="text-xs mt-1 text-red-500">Esaurito</span>
                  )}
                </div>
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default DealTimeSelector;

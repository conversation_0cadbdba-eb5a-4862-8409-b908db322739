import { useState, useEffect } from "react";
import { MapPin, Navigation, Save } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AddressAutocomplete, AddressComponents } from "@/components/business/AddressAutocomplete";
import { toast } from "sonner";

interface DemoCoordinatesEditorProps {
  demoLatitude: number | null;
  demoLongitude: number | null;
  onUpdate: (lat: number, lng: number) => Promise<boolean>;
}

export const DemoCoordinatesEditor = ({ 
  demoLatitude, 
  demoLongitude, 
  onUpdate 
}: DemoCoordinatesEditorProps) => {
  console.log('DemoCoordinatesEditor: Received props:', { demoLatitude, demoLongitude });
  
  const [lat, setLat] = useState(demoLatitude?.toString() || "45.4666");
  const [lng, setLng] = useState(demoLongitude?.toString() || "9.1832");
  const [address, setAddress] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  
  // Update local state when props change
  useEffect(() => {
    console.log('DemoCoordinatesEditor: Props changed, updating state:', { demoLatitude, demoLongitude });
    if (demoLatitude !== null) {
      setLat(demoLatitude.toString());
    }
    if (demoLongitude !== null) {
      setLng(demoLongitude.toString());
    }
  }, [demoLatitude, demoLongitude]);

  const validateCoordinates = (latitude: string, longitude: string): boolean => {
    const latNum = parseFloat(latitude);
    const lngNum = parseFloat(longitude);
    
    if (isNaN(latNum) || isNaN(lngNum)) {
      toast.error("Inserisci coordinate numeriche valide");
      return false;
    }
    
    if (latNum < -90 || latNum > 90) {
      toast.error("La latitudine deve essere tra -90 e 90");
      return false;
    }
    
    if (lngNum < -180 || lngNum > 180) {
      toast.error("La longitudine deve essere tra -180 e 180");
      return false;
    }
    
    return true;
  };

  const handleSave = async () => {
    if (!validateCoordinates(lat, lng)) return;
    
    setIsLoading(true);
    try {
      console.log("DemoCoordinatesEditor: Saving coordinates", { lat: parseFloat(lat), lng: parseFloat(lng) });
      const success = await onUpdate(parseFloat(lat), parseFloat(lng));
      if (success) {
        toast.success("Coordinate demo aggiornate con successo");
        console.log("DemoCoordinatesEditor: Coordinates saved successfully");
      }
    } catch (error) {
      console.error("Error updating demo coordinates:", error);
      toast.error("Errore nell'aggiornamento delle coordinate");
    } finally {
      setIsLoading(false);
    }
  };

  const handleUseCurrentLocation = () => {
    if (!navigator.geolocation) {
      toast.error("Geolocalizzazione non supportata dal browser");
      return;
    }
    
    setIsLoading(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const newLat = position.coords.latitude.toFixed(6);
        const newLng = position.coords.longitude.toFixed(6);
        setLat(newLat);
        setLng(newLng);
        setIsLoading(false);
        toast.success("Coordinate attuali acquisite");
      },
      (error) => {
        console.error("Geolocation error:", error);
        toast.error("Impossibile ottenere la posizione attuale");
        setIsLoading(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }
    );
  };

  const handleAddressChange = (sddressComponent?: AddressComponents) => {
    setAddress(sddressComponent.formatted_address);
    if (lat !== undefined && lng !== undefined) {
      setLat(sddressComponent.latitude.toFixed(6));
      setLng(sddressComponent.longitude.toFixed(6));
      toast.success("Coordinate acquisite dall'indirizzo");
    }
  };

  return (
    <div className="space-y-4 p-4 border border-border rounded-lg bg-card">
      <div className="flex items-center space-x-2">
        <MapPin className="h-5 w-5 text-brand-primary" />
        <h3 className="font-medium text-foreground">Coordinate Demo</h3>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="address" className="text-sm text-muted-foreground">
          Cerca per Indirizzo
        </Label>
        <AddressAutocomplete
          value={address}
          onChange={handleAddressChange}
        />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="latitude" className="text-sm text-muted-foreground">
            Latitudine
          </Label>
          <Input
            id="latitude"
            type="number"
            step="any"
            placeholder="45.4666"
            value={lat}
            onChange={(e) => setLat(e.target.value)}
            className="text-sm"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="longitude" className="text-sm text-muted-foreground">
            Longitudine
          </Label>
          <Input
            id="longitude"
            type="number"
            step="any"
            placeholder="9.1832"
            value={lng}
            onChange={(e) => setLng(e.target.value)}
            className="text-sm"
          />
        </div>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleUseCurrentLocation}
          disabled={isLoading}
          className="flex items-center gap-2"
        >
          <Navigation className="h-4 w-4" />
          Usa Posizione Attuale
        </Button>
        
        <Button
          variant="default"
          size="sm"
          onClick={handleSave}
          disabled={isLoading}
          className="flex items-center gap-2"
        >
          <Save className="h-4 w-4" />
          Salva Coordinate
        </Button>
      </div>
      
      <p className="text-xs text-muted-foreground">
        Le coordinate demo vengono utilizzate quando la modalità demo mappa è attiva.
        Milano di default: 45.4666, 9.1832
      </p>
    </div>
  );
};
# CatchUp Booking Workflow Documentation

## Overview

The CatchUp booking system is a comprehensive workflow that enables customers to discover, select, and book time-sensitive deals from local businesses. The system provides real-time availability management, streamlined booking processes, and robust business management tools.

## 🎯 Key Features

- **Multi-step booking process** with clear progress indicators
- **Real-time availability tracking** using Supabase subscriptions
- **Multi-day deal availability** with flexible time slot management
- **Auto-confirmation** or manual approval workflows
- **QR code generation** for booking verification
- **Business dashboard** for booking management
- **Customer booking history** and management

## 📊 Database Schema & Entities

### Core Tables

#### 1. `bookings` Table
Primary booking records table containing all booking information.

```sql
bookings {
  id: string (UUID, Primary Key)
  user_id: string (UUID, Foreign Key → users)
  deal_id: string (UUID, Foreign Key → deals)
  booking_date: string (DATE)
  booking_time: string
  booking_end_time: string
  status: string ('pending' | 'confirmed' | 'cancelled')
  discount_percentage: number
  discounted_price: number
  original_price: number
  cancellation_note: string | null
  qr_data: Json (QR code data)
  fake: boolean (for testing)
  created_at: timestamp
  updated_at: timestamp
}
```

#### 2. `time_slot_bookings` Table
Detailed time slot tracking for capacity management.

```sql
time_slot_bookings {
  id: string (UUID, Primary Key)
  booking_id: string (UUID, Foreign Key → bookings)
  deal_id: string (UUID, Foreign Key → deals)
  booking_date: string | null (DATE)
  start_time: string
  end_time: string
  day_of_week: number
  booked_seats: number
  fake: boolean (for testing)
  created_at: timestamp
}
```

#### 3. `deals` Table
Deal information with time slot configuration.

```sql
deals {
  id: string (UUID, Primary Key)
  business_id: string (UUID, Foreign Key → businesses)
  title: string
  description: string | null
  discounted_price: number
  original_price: number
  discount_percentage: number | null
  start_date: string (DATE)
  end_date: string (DATE)
  time_slots: Json | null (flexible multi-day availability)
  auto_confirm: boolean (automatic vs manual approval)
  status: enum ('draft' | 'published' | 'expired')
  terms_conditions: string
  images: string[] | null
  category_id: string | null
  fake: boolean (for testing)
  created_at: timestamp
  updated_at: timestamp
}
```

### TypeScript Type Definitions

#### Core Booking Types

```typescript
// Basic booking with deal information
export type Booking = Database['public']['Tables']['bookings']['Row'] & {
  deals: {
    title: string;
    images: string[] | null;
    businesses: {
      name: string;
      address: string;
    } | null;
  } | null;
};

// Extended booking with user details (for business management)
export type BookingWithDetails = Omit<Database['public']['Tables']['bookings']['Row'], 'qr_data'> & {
  deals?: {
    title: string;
    images: string[] | null;
    description: string | null;
    discounted_price: number;
    businesses?: {
      name: string;
      address: string | null;
    } | null;
  } | null;
  user_details?: {
    first_name: string | null;
    last_name: string | null;
    avatar_url: string | null;
  } | null;
  qr_data: QRData;
};

// QR code data structure
export type QRData = {
  bookingId: string;
  dealTitle: string;
  businessName: string;
  date: string;
  time: string;
  status: string;
};

// Booking form data
export interface BookingDetails {
  date: string;
  day: number;
  start_time: string;
  end_time: string;
}
```

## 🔄 Booking Process Flow

### 1. Customer Journey

```mermaid
flowchart TD
    A[Browse Deals] --> B[View Deal Details]
    B --> C[Check Availability]
    C --> D{Available Slots?}
    
    D -->|Yes| E[Select Date & Time]
    D -->|Limited| F[Show Limited Availability]
    D -->|Sold Out| G[Show Waitlist/Alternative]
    
    E --> H[Choose Quantity]
    F --> H
    G --> Z[End - No Booking]
    
    H --> I{User Authenticated?}
    I -->|No| J[Login/Register Required]
    I -->|Yes| K[Review Booking Details]
    
    J --> O[Authentication Flow]
    O --> K
    
    K --> L[Confirm Terms & Conditions]
    L --> M[Process Payment]
    M --> N{Payment Success?}
    
    N -->|Success| P[Create Booking Record]
    N -->|Failed| Q[Payment Error - Retry]
    
    P --> R{Auto-Confirm Deal?}
    R -->|Yes| S[Status: Confirmed]
    R -->|No| T[Status: Pending]
    
    S --> U[Generate QR Code]
    T --> V[Notify Business]
    V --> U
    
    U --> W[Send Confirmation]
    W --> X[Booking Complete]
    
    Q --> M
```

### 2. Business Management Flow

```mermaid
flowchart TD
    A[Booking Created] --> B{Auto-Confirm?}
    B -->|Yes| C[Automatically Confirmed]
    B -->|No| D[Pending Approval]
    
    D --> E[Business Receives Notification]
    E --> F{Business Decision}
    F -->|Approve| G[Update Status to Confirmed]
    F -->|Reject| H[Update Status to Cancelled]
    
    G --> I[Send Customer Confirmation]
    H --> J[Send Cancellation Notice]
    
    I --> K[Generate QR Code]
    J --> L[Update Availability]
    
    C --> K
    K --> M[Booking Active]
    L --> N[End]
    M --> O[Ready for Check-in]
```

## 🧩 Component Architecture

### Customer-Facing Components

#### 1. **BookingPage** (`src/pages/bookings/BookingPage.tsx`)
Main booking interface with multi-step process:
- **Step 1**: Date selection with availability display
- **Step 2**: Booking details review
- **Step 3**: Payment processing (demo mode)

```typescript
const BookingPage = () => {
  const [step, setStep] = useState(1);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [availability, setAvailability] = useState<BookingAvailability | null>(null);
  
  // Multi-step navigation and validation
  const handleDateSelection = (date: string, avail: BookingAvailability) => {
    setSelectedDate(date);
    setAvailability(avail);
    if (quantity > avail.available) {
      setQuantity(Math.max(1, avail.available));
    }
  };
};
```

#### 2. **DateAvailabilitySelector** (`src/components/DateAvailabilitySelector.tsx`)
Sophisticated date picker with real-time availability:
- Fetches available dates from deal configuration
- Shows capacity indicators (Available/Limited/Sold Out)
- Visual progress bars for booking status
- Automatic availability updates

```typescript
const DateAvailabilitySelector = ({ dealId, onSelectDate, initialDate }) => {
  const [availableDates, setAvailableDates] = useState<string[]>([]);
  const [availability, setAvailability] = useState<AvailabilityInfo | null>(null);
  
  // Real-time availability fetching
  useEffect(() => {
    async function fetchAvailability() {
      const avail = await availabilityService.getAvailabilityForDate(dealId, selectedDate);
      setAvailability(avail);
      if (onSelectDate) onSelectDate(selectedDate, avail);
    }
    fetchAvailability();
  }, [dealId, selectedDate]);
};
```

#### 3. **ConfirmBooking** (`src/pages/bookings/ConfirmBooking.tsx`)
Final confirmation page with booking summary and action buttons.

### Business Management Components

#### 1. **BusinessBookingList** (`src/components/booking/BusinessBookingList.tsx`)
Comprehensive booking management interface:
- Real-time booking status updates
- Approve/cancel functionality
- Customer information display
- Quick action buttons

```typescript
const BusinessBookingList = ({
  bookings,
  onApprove,
  onCancel,
  selectedBookingId,
  setSelectedBookingId
}) => {
  return (
    <div className="space-y-2">
      {bookings.map((booking) => (
        <div key={booking.id} className="bg-white rounded-lg shadow-sm p-2">
          <BookingInfo booking={booking} />
          <BookingStatus status={booking.status} />
          <div className="flex gap-2">
            <button onClick={() => onApprove(booking.id)}>Approva</button>
            <button onClick={() => setSelectedBookingId(booking.id)}>Cancella</button>
          </div>
        </div>
      ))}
    </div>
  );
};
```

#### 2. **BusinessBookings** (`src/pages/bookings/BusinessBookings.tsx`)
Main business dashboard for booking management.

#### 3. **BusinessBookingDetails** (`src/pages/bookings/BusinessBookingDetails.tsx`)
Detailed view of individual bookings with full customer information.

### Supporting Components

#### 1. **BookingStatus** (`src/components/booking/BookingStatus.tsx`)
Visual status indicators with color-coded badges:
- ✅ Confirmed (green)
- ⏱️ Pending (yellow)
- ❌ Cancelled (red)

#### 2. **BookingInfo** (`src/components/booking/BookingInfo.tsx`)
Formatted display of booking details.

#### 3. **CancellationForm** (`src/components/booking/CancellationForm.tsx`)
Form for cancelling bookings with required notes.

## 🔧 Service Layer

### AvailabilityService (`src/services/availabilityService.ts`)

Centralized service for all availability-related operations:

```typescript
export const availabilityService = {
  // Get all available dates for a deal
  async getAvailableDates(dealId: string): Promise<string[]>
  
  // Get availability for specific date
  async getAvailabilityForDate(dealId: string, date: string): Promise<{
    capacity: number;
    booked: number;
    available: number;
  }>
  
  // Get time slot bookings
  async getTimeSlotBookings(dealId: string, date: string): Promise<Record<string, number>>
  
  // Calculate remaining seats
  calculateRemainingSeats(totalSeats: number, bookedSeats: number): number
  
  // Get availability summary across all dates
  async getAvailabilitySummary(dealId: string): Promise<AvailabilitySummary>
};
```

### Key Capabilities:
- **Real-time calculations**: Dynamic availability based on current bookings
- **Multi-day support**: Handles complex time slot configurations
- **Capacity management**: Tracks and enforces seat limits
- **Status determination**: Automatically categorizes availability (Available/Limited/Sold Out)

## 🔗 Supabase Integration

### Real-time Features

#### 1. **Live Availability Updates**
```typescript
const useRealTimeAvailability = (dealId: string) => {
  useEffect(() => {
    const subscription = supabase
      .channel(`deal-${dealId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'time_slot_bookings',
        filter: `deal_id=eq.${dealId}`
      }, () => {
        queryClient.invalidateQueries(['availability', dealId]);
      })
      .subscribe();
      
    return () => subscription.unsubscribe();
  }, [dealId]);
};
```

#### 2. **Row Level Security (RLS)**
```sql
-- Users can only view their own bookings
CREATE POLICY "Users can view own bookings" ON bookings
  FOR SELECT USING (auth.uid() = user_id);

-- Business owners can view bookings for their deals
CREATE POLICY "Business owners can view their bookings" ON bookings
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM deals d 
      JOIN businesses b ON d.business_id = b.id 
      WHERE d.id = bookings.deal_id AND b.owner_id = auth.uid()
    )
  );
```

### Data Operations

#### 1. **Booking Creation**
```typescript
const createBooking = async (bookingData: BookingInsert) => {
  const { data, error } = await supabase
    .from('bookings')
    .insert({
      user_id: userId,
      deal_id: dealId,
      booking_date: selectedDate,
      booking_time: selectedTime,
      status: deal.auto_confirm ? 'confirmed' : 'pending',
      discounted_price: deal.discounted_price,
      original_price: deal.original_price,
      qr_data: generateQRData(bookingInfo)
    })
    .select()
    .single();
    
  if (error) throw error;
  return data;
};
```

#### 2. **Availability Queries**
```typescript
// Complex query for availability calculation
const getAvailability = async (dealId: string, date: string) => {
  const { data: bookings } = await supabase
    .from('time_slot_bookings')
    .select('booked_seats')
    .eq('deal_id', dealId)
    .eq('booking_date', date);
    
  const totalBooked = bookings?.reduce((sum, booking) => 
    sum + (booking.booked_seats || 1), 0) || 0;
    
  return {
    capacity: DEFAULT_CAPACITY,
    booked: totalBooked,
    available: Math.max(0, DEFAULT_CAPACITY - totalBooked)
  };
};
```

## 🚀 Process Streamlining Features

### 1. **Intelligent Date Selection**
- **Smart filtering**: Only shows dates with availability
- **Visual indicators**: Color-coded availability status
- **Capacity warnings**: Alerts for limited availability
- **Auto-selection**: Remembers user preferences

### 2. **Seamless User Experience**
- **Progressive disclosure**: Multi-step process with clear progress
- **Auto-save**: Form data persisted across steps
- **Error recovery**: Graceful handling of booking conflicts
- **Mobile optimization**: Touch-friendly interface design

### 3. **Business Efficiency**
- **Auto-confirmation**: Configurable automatic approval
- **Bulk actions**: Multiple booking management
- **Real-time notifications**: Instant booking alerts
- **Quick filters**: Fast booking status filtering

### 4. **Performance Optimizations**
- **Smart caching**: React Query for server state management
- **Lazy loading**: Components loaded on demand
- **Optimistic updates**: Immediate UI feedback
- **Background sync**: Real-time data synchronization

### 5. **Error Prevention**
- **Availability checks**: Prevents double-booking
- **Form validation**: Client and server-side validation
- **Conflict resolution**: Handles concurrent booking attempts
- **Data integrity**: Transaction-based booking creation

## 🔄 State Management

### Customer Booking State
```typescript
// React Query for server state
const useBookings = () => {
  return useQuery({
    queryKey: ['bookings', userId],
    queryFn: () => fetchUserBookings(userId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Local state for booking flow
const [bookingState, setBookingState] = useState({
  step: 1,
  selectedDate: null,
  quantity: 1,
  availability: null
});
```

### Business Dashboard State
```typescript
// Business bookings with real-time updates
const useBusinessBookings = (businessId: string) => {
  const [bookings, setBookings] = useState<BookingWithDetails[]>([]);
  const [selectedBookingId, setSelectedBookingId] = useState<string | null>(null);
  
  // Fetch and manage business bookings
  const handleApprove = async (bookingId: string) => {
    await supabase
      .from('bookings')
      .update({ status: 'confirmed' })
      .eq('id', bookingId);
    await fetchBookings();
  };
};
```

## 📱 Mobile Responsiveness

### Key Mobile Features
- **Touch-optimized controls**: Large tap targets for booking actions
- **Swipe gestures**: Natural navigation between booking steps
- **Responsive layouts**: Optimized for all screen sizes
- **Offline support**: PWA capabilities for basic functionality

### Performance Considerations
- **Lazy loading**: Images and components loaded as needed
- **Bundle optimization**: Code splitting for faster initial load
- **Service workers**: Caching strategies for offline access
- **Core Web Vitals**: Meeting performance benchmarks

## 🔮 Future Enhancements

### Planned Features
1. **Payment Integration**: Real payment processing (Stripe/PayPal)
2. **Advanced Notifications**: Push notifications for booking updates
3. **Calendar Integration**: Sync with user's calendar apps
4. **Review System**: Post-booking review and rating functionality
5. **AI Recommendations**: Smart booking suggestions based on user behavior

### Technical Improvements
1. **WebSocket Integration**: Real-time booking updates
2. **Advanced Caching**: Redis for high-performance caching
3. **Microservices**: Separate booking service for scalability
4. **Analytics**: Comprehensive booking analytics and insights

---

*This documentation covers the complete CatchUp booking workflow as of the current implementation. The system demonstrates enterprise-level architecture with real-time capabilities, robust state management, and user-centric design principles.* 
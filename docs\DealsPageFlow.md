# Deals Page Flow

This document describes the flow and user interactions handled by `src/pages/deals/Deals.tsx`.

The page loads all published deals from Supabase, allows users to filter them, and shows the results either in a list or on an interactive map.

## UML Flow Diagram

```mermaid
flowchart TD
    A[Page Mount] --> B[fetchCategories]
    A --> C[fetchBusinessesAndDeals]
    B --> D[setCategories]
    C --> E[setBusinesses & setDeals]
    E --> F{viewMode}
    F -->|list| G[List View]
    F -->|map| H[Map View]

    G --> I[SearchBar]
    G --> J[Categories]
    G --> K[NearestBusinesses]
    I --> L[Filter Deals]
    J --> L
    L --> M[Render DealCard List]
    M --> N[Navigate to Deal Detail]

    H --> O[Search Button]
    O --> P[Search Panel]
    P --> Q[SearchBar + Categories]
    Q --> R[Filter Deals & Businesses]
    H --> S[MapView with Markers]
    S --> T[Click Marker]
    T --> N
```

## Available Use Cases

- Browse all deals in list view.
- Search deals by name or business address.
- Filter deals by category.
- View nearby businesses (when in list view).
- Switch between list and map views.
- Open a search panel while in map view.
- View deals on a map with interactive markers.
- Navigate to the detail page of a selected deal.

import { useState } from "react";
import { Calendar, Clock, Euro, MapPin, Users, Heart, Coffee, Dumbbell, ShoppingBag } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import type { Experience } from "@/pages/mains/Experience";

interface ExperienceTemplatesProps {
  onSelectTemplate: (template: Experience) => void;
}

const ExperienceTemplates = ({ onSelectTemplate }: ExperienceTemplatesProps) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { id: 'all', label: '<PERSON><PERSON>', icon: MapPin },
    { id: 'wellness', label: 'Benessere', icon: Heart },
    { id: 'food', label: 'Enogastronomia', icon: Coffee },
    { id: 'fitness', label: 'Sport', icon: Du<PERSON><PERSON> },
    { id: 'shopping', label: 'Shopping', icon: ShoppingBag },
    { id: 'couples', label: 'Co<PERSON><PERSON>', icon: Users },
  ];

  const templates: Experience[] = [
    {
      id: 'template-wellness-1',
      name: 'Giornata Spa & Relax',
      description: 'Una giornata dedicata al benessere e al relax totale',
      date: new Date().toISOString().split('T')[0],
      stops: [
        {
          id: 'stop-1',
          dealId: 'template-deal-1',
          businessId: 'template-business-1',
          dealTitle: 'Massaggio Rilassante 90min',
          businessName: 'Spa Luxury Center',
          address: 'Via del Benessere 10, Milano',
          latitude: 45.4685,
          longitude: 9.1824,
          startTime: '10:00',
          endTime: '11:30',
          estimatedDuration: 90,
          price: 120,
          category: 'Benessere',
          travelTimeToNext: 20,
          order: 1
        },
        {
          id: 'stop-2',
          dealId: 'template-deal-2',
          businessId: 'template-business-2',
          dealTitle: 'Pranzo Detox Menu',
          businessName: 'Green Bistrot',
          address: 'Via Salute 5, Milano',
          latitude: 45.4758,
          longitude: 9.2025,
          startTime: '13:00',
          endTime: '14:00',
          estimatedDuration: 60,
          price: 35,
          category: 'Ristorazione',
          travelTimeToNext: 15,
          order: 2
        },
        {
          id: 'stop-3',
          dealId: 'template-deal-3',
          businessId: 'template-business-3',
          dealTitle: 'Hammam e Sauna',
          businessName: 'Terme Urbane',
          address: 'Corso Relax 20, Milano',
          latitude: 45.4722,
          longitude: 9.1881,
          startTime: '15:30',
          endTime: '17:00',
          estimatedDuration: 90,
          price: 45,
          category: 'Benessere',
          order: 3
        }
      ],
      totalDuration: 240,
      totalPrice: 200,
      totalTravelTime: 35,
      status: 'draft',
      isTemplate: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'template-food-1',
      name: 'Tour Gastronomico',
      description: 'Scopri i sapori autentici della città',
      date: new Date().toISOString().split('T')[0],
      stops: [
        {
          id: 'stop-1',
          dealId: 'template-deal-4',
          businessId: 'template-business-4',
          dealTitle: 'Degustazione Vini e Formaggi',
          businessName: 'Enoteca del Centro',
          address: 'Via Gourmet 15, Milano',
          latitude: 45.4685,
          longitude: 9.1824,
          startTime: '11:00',
          endTime: '12:30',
          estimatedDuration: 90,
          price: 40,
          category: 'Enogastronomia',
          travelTimeToNext: 10,
          order: 1
        },
        {
          id: 'stop-2',
          dealId: 'template-deal-5',
          businessId: 'template-business-5',
          dealTitle: 'Pranzo Tradizionale',
          businessName: 'Osteria della Nonna',
          address: 'Via Sapori 8, Milano',
          latitude: 45.4758,
          longitude: 9.2025,
          startTime: '13:00',
          endTime: '14:30',
          estimatedDuration: 90,
          price: 55,
          category: 'Ristorazione',
          travelTimeToNext: 15,
          order: 2
        },
        {
          id: 'stop-3',
          dealId: 'template-deal-6',
          businessId: 'template-business-6',
          dealTitle: 'Corso di Cucina Italiana',
          businessName: 'Scuola di Cucina Milano',
          address: 'Via Chef 12, Milano',
          latitude: 45.4722,
          longitude: 9.1881,
          startTime: '16:00',
          endTime: '18:00',
          estimatedDuration: 120,
          price: 75,
          category: 'Enogastronomia',
          order: 3
        }
      ],
      totalDuration: 300,
      totalPrice: 170,
      totalTravelTime: 25,
      status: 'draft',
      isTemplate: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'template-fitness-1',
      name: 'Giornata Fitness & Sport',
      description: 'Un programma completo per mantenersi in forma',
      date: new Date().toISOString().split('T')[0],
      stops: [
        {
          id: 'stop-1',
          dealId: 'template-deal-7',
          businessId: 'template-business-7',
          dealTitle: 'Allenamento Personal Trainer',
          businessName: 'FitClub Premium',
          address: 'Via Sport 25, Milano',
          latitude: 45.4685,
          longitude: 9.1824,
          startTime: '09:00',
          endTime: '10:00',
          estimatedDuration: 60,
          price: 50,
          category: 'Fitness',
          travelTimeToNext: 20,
          order: 1
        },
        {
          id: 'stop-2',
          dealId: 'template-deal-8',
          businessId: 'template-business-8',
          dealTitle: 'Smoothie Proteico',
          businessName: 'Juice Bar Energy',
          address: 'Via Energia 3, Milano',
          latitude: 45.4758,
          longitude: 9.2025,
          startTime: '10:30',
          endTime: '11:00',
          estimatedDuration: 30,
          price: 12,
          category: 'Ristorazione',
          travelTimeToNext: 25,
          order: 2
        },
        {
          id: 'stop-3',
          dealId: 'template-deal-9',
          businessId: 'template-business-9',
          dealTitle: 'Lezione di Yoga',
          businessName: 'Yoga Studio Zen',
          address: 'Via Equilibrio 18, Milano',
          latitude: 45.4722,
          longitude: 9.1881,
          startTime: '12:00',
          endTime: '13:00',
          estimatedDuration: 60,
          price: 25,
          category: 'Fitness',
          order: 3
        }
      ],
      totalDuration: 150,
      totalPrice: 87,
      totalTravelTime: 45,
      status: 'draft',
      isTemplate: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'template-couples-1',
      name: 'Serata Romantica',
      description: 'Una serata perfetta per le coppie',
      date: new Date().toISOString().split('T')[0],
      stops: [
        {
          id: 'stop-1',
          dealId: 'template-deal-10',
          businessId: 'template-business-10',
          dealTitle: 'Aperitivo al Tramonto',
          businessName: 'Sky Lounge',
          address: 'Via Panorama 30, Milano',
          latitude: 45.4685,
          longitude: 9.1824,
          startTime: '18:00',
          endTime: '19:30',
          estimatedDuration: 90,
          price: 45,
          category: 'Ristorazione',
          travelTimeToNext: 15,
          order: 1
        },
        {
          id: 'stop-2',
          dealId: 'template-deal-11',
          businessId: 'template-business-11',
          dealTitle: 'Cena Romantica per Due',
          businessName: 'Ristorante Amore',
          address: 'Via Romantica 7, Milano',
          latitude: 45.4758,
          longitude: 9.2025,
          startTime: '20:00',
          endTime: '22:00',
          estimatedDuration: 120,
          price: 120,
          category: 'Ristorazione',
          travelTimeToNext: 10,
          order: 2
        },
        {
          id: 'stop-3',
          dealId: 'template-deal-12',
          businessId: 'template-business-12',
          dealTitle: 'Cocktail e Musica Live',
          businessName: 'Jazz Club Mood',
          address: 'Via Musica 14, Milano',
          latitude: 45.4722,
          longitude: 9.1881,
          startTime: '22:30',
          endTime: '00:00',
          estimatedDuration: 90,
          price: 35,
          category: 'Intrattenimento',
          order: 3
        }
      ],
      totalDuration: 300,
      totalPrice: 200,
      totalTravelTime: 25,
      status: 'draft',
      isTemplate: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ];

  const filteredTemplates = selectedCategory === 'all' 
    ? templates 
    : templates.filter(template => {
        // Filter based on the predominant category in the template
        const categoryMap = {
          'wellness': ['Benessere'],
          'food': ['Enogastronomia', 'Ristorazione'],
          'fitness': ['Fitness'],
          'shopping': ['Shopping'],
          'couples': ['Intrattenimento']
        };
        
        const relevantCategories = categoryMap[selectedCategory as keyof typeof categoryMap] || [];
        return template.stops.some(stop => relevantCategories.includes(stop.category));
      });

  const getCategoryIcon = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    if (!category) return MapPin;
    return category.icon;
  };

  const getTemplateCategoryColor = (template: Experience) => {
    const categories = template.stops.map(stop => stop.category);
    if (categories.includes('Benessere')) return 'bg-green-100 text-green-800';
    if (categories.includes('Enogastronomia') || categories.includes('Ristorazione')) return 'bg-orange-100 text-orange-800';
    if (categories.includes('Fitness')) return 'bg-blue-100 text-blue-800';
    if (categories.includes('Intrattenimento')) return 'bg-purple-100 text-purple-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold mb-4">Template Esperienze</h2>
        <p className="text-muted-foreground mb-6">
          Scegli tra i nostri template predefiniti per iniziare rapidamente
        </p>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => {
          const Icon = category.icon;
          return (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className="flex items-center space-x-2"
            >
              <Icon className="w-4 h-4" />
              <span>{category.label}</span>
            </Button>
          );
        })}
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    {template.description}
                  </p>
                </div>
                <Badge className={getTemplateCategoryColor(template)}>
                  {template.stops.length} attività
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Template Stats */}
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                  <span>{Math.round(template.totalDuration / 60)}h</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Euro className="w-4 h-4 text-muted-foreground" />
                  <span>{template.totalPrice}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <MapPin className="w-4 h-4 text-muted-foreground" />
                  <span>{template.stops.length} tappe</span>
                </div>
              </div>

              {/* Template Preview */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Anteprima Attività:</h4>
                <div className="space-y-1">
                  {template.stops.slice(0, 3).map((stop, index) => (
                    <div key={stop.id} className="flex items-center justify-between text-xs bg-muted p-2 rounded">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs px-1">
                          {index + 1}
                        </Badge>
                        <span className="font-medium">{stop.dealTitle}</span>
                      </div>
                      <div className="flex items-center space-x-2 text-muted-foreground">
                        <span>{stop.startTime}</span>
                        <span>€{stop.price}</span>
                      </div>
                    </div>
                  ))}
                  {template.stops.length > 3 && (
                    <div className="text-xs text-muted-foreground text-center py-1">
                      +{template.stops.length - 3} altre attività
                    </div>
                  )}
                </div>
              </div>

              <Button 
                onClick={() => onSelectTemplate(template)}
                className="w-full"
              >
                Usa questo Template
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Nessun template trovato</h3>
            <p className="text-muted-foreground">
              Prova a selezionare una categoria diversa
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ExperienceTemplates;
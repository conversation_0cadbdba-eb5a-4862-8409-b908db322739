
import { Tag, CalendarCheck, Euro, Users } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";

interface BusinessStatsProps {
  dealsCount: number;
  draftDealsCount?: number;
  businessId?: string;
}

export const BusinessStats = ({ dealsCount, draftDealsCount = 0, businessId }: BusinessStatsProps) => {
  const navigate = useNavigate();
  const [totalBookings, setTotalBookings] = useState(0);
  const [pendingBookings, setPendingBookings] = useState(0);

  useEffect(() => {
    const fetchBookings = async () => {
      if (!businessId) return;

      try {
        // Prima otteniamo tutti i deal_id per questa attività
        const { data: deals } = await supabase
          .from('deals')
          .select('id')
          .eq('business_id', businessId);

        if (!deals || deals.length === 0) {
          setTotalBookings(0);
          setPendingBookings(0);
          return;
        }

        const dealIds = deals.map(deal => deal.id);

        // Contiamo tutte le prenotazioni
        const { count: totalCount } = await supabase
          .from('bookings')
          .select('*', { count: 'exact', head: true })
          .in('deal_id', dealIds);

        // Contiamo le prenotazioni in attesa
        const { count: pendingCount } = await supabase
          .from('bookings')
          .select('*', { count: 'exact', head: true })
          .in('deal_id', dealIds)
          .eq('status', 'pending');

        setTotalBookings(totalCount || 0);
        setPendingBookings(pendingCount || 0);
      } catch (error) {
        console.error('Error fetching bookings:', error);
      }
    };

    fetchBookings();
  }, [businessId]);

  return (
    <section className="mt-6">
      <h2 className="text-lg font-semibold mb-4">Panoramica</h2>
      <div className="grid grid-cols-2 gap-4">
        <div
          className="bg-gradient-to-br from-pink-500 to-rose-500 p-4 rounded-xl text-white cursor-pointer"
          onClick={() => businessId && navigate(`/business/${businessId}/deals`)}
        >
          <Tag className="h-6 w-6 mb-2" />
          <h3 className="text-sm opacity-90">Offerte Attive</h3>
          <div className="flex items-center gap-2">
            <p className="text-2xl font-bold">{dealsCount}</p>
            {draftDealsCount > 0 && (
              <span className="bg-gray-400/20 text-white text-xs px-2 py-1 rounded-full">
                +{draftDealsCount} bozze
              </span>
            )}
          </div>
        </div>
        <div
          className="bg-gradient-to-br from-purple-500 to-indigo-500 p-4 rounded-xl text-white cursor-pointer"
          onClick={() => businessId && navigate(`/business/${businessId}/bookings`)}
        >
          <CalendarCheck className="h-6 w-6 mb-2" />
          <h3 className="text-sm opacity-90">Prenotazioni</h3>
          <div className="flex items-center gap-2">
            <p className="text-2xl font-bold">{totalBookings}</p>
            {pendingBookings > 0 && (
              <span className="bg-yellow-400/20 text-yellow-100 text-xs px-2 py-1 rounded-full">
                {pendingBookings} in attesa
              </span>
            )}
          </div>
        </div>
        <div className="bg-gradient-to-br from-blue-500 to-cyan-500 p-4 rounded-xl text-white">
          <Euro className="h-6 w-6 mb-2" />
          <h3 className="text-sm opacity-90">Ricavi</h3>
          <p className="text-2xl font-bold">€2.4K</p>
        </div>
        <div className="bg-gradient-to-br from-teal-500 to-emerald-500 p-4 rounded-xl text-white">
          <Users className="h-6 w-6 mb-2" />
          <h3 className="text-sm opacity-90">Clienti</h3>
          <p className="text-2xl font-bold">156</p>
        </div>
      </div>
    </section>
  );
};

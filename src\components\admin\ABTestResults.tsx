import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { getPalette } from '@/styles/colorPalettes';

// Mock data for demonstration - in a real app, this would come from your analytics service
const mockData = {
  pageViews: [
    { name: 'Home', default: 1200, alternative: 1250, unit: 'views' },
    { name: 'Map', default: 850, alternative: 920, unit: 'views' },
    { name: 'Deals', default: 650, alternative: 720, unit: 'views' },
    { name: 'Profile', default: 450, alternative: 430, unit: 'views' },
  ],
  engagement: [
    { name: 'Avg. Session Duration', default: 185, alternative: 210, unit: 'seconds' },
    { name: 'Avg. Pages per Session', default: 3.2, alternative: 3.6, unit: 'pages' },
    { name: 'Bounce Rate', default: 42, alternative: 38, unit: 'percent' },
    { name: 'Avg. Scroll Depth', default: 65, alternative: 72, unit: 'percent' },
  ],
  conversions: [
    { name: 'Booking Completion', default: 5.2, alternative: 6.1, unit: 'percent' },
    { name: 'Deal Views', default: 28, alternative: 32, unit: 'percent' },
    { name: 'Business Contact', default: 3.8, alternative: 4.2, unit: 'percent' },
    { name: 'App Shares', default: 1.2, alternative: 1.4, unit: 'percent' },
  ],
};

const ABTestResults: React.FC = () => {
  const [activeTab, setActiveTab] = useState('pageViews');
  const [data, setData] = useState(mockData.pageViews);
  const [isLoading, setIsLoading] = useState(false);
  const [testActive, setTestActive] = useState(true);

  // Get color palettes for visualization
  const defaultPalette = getPalette('default');
  const alternativePalette = getPalette('alternative');

  // Update data when tab changes
  useEffect(() => {
    setData(mockData[activeTab as keyof typeof mockData]);
  }, [activeTab]);

  // In a real app, you would fetch actual data from your analytics service
  const fetchRealData = async () => {
    setIsLoading(true);
    try {
      // Example API call to fetch data
      // const response = await fetch('/api/analytics/ab-test-results');
      // const data = await response.json();
      // setData(data[activeTab]);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setData(mockData[activeTab as keyof typeof mockData]);
    } catch (error) {
      console.error('Error fetching A/B test data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Format data for the chart
  const chartData = data.map(item => ({
    name: item.name,
    'Default Theme': item.default,
    'Alternative Theme': item.alternative,
    unit: item.unit,
  }));

  // Calculate improvement percentages
  const calculateImprovement = (metric: string) => {
    const item = data.find(d => d.name === metric);
    if (!item) return 0;

    const improvement = ((item.alternative - item.default) / item.default) * 100;
    return improvement.toFixed(1);
  };

  // Handle ending the test and selecting a winner
  const handleEndTest = (winner: 'default' | 'alternative') => {
    setTestActive(false);
    // In a real app, you would make an API call to end the test and set the winner
    alert(`Test ended. ${winner === 'default' ? 'Default' : 'Alternative'} theme selected as the winner.`);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Color Palette A/B Test Results</CardTitle>
        <CardDescription>
          Comparing performance metrics between default (pink) and alternative (orange) color schemes
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="pageViews" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4 w-full flex flex-wrap">
            <TabsTrigger value="pageViews" className="flex-1">Page Views</TabsTrigger>
            <TabsTrigger value="engagement" className="flex-1">User Engagement</TabsTrigger>
            <TabsTrigger value="conversions" className="flex-1">Conversions</TabsTrigger>
          </TabsList>

          <TabsContent value="pageViews" className="space-y-4">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value, name) => [`${value} ${data[0]?.unit || ''}`, name]} />
                  <Legend />
                  <Bar dataKey="Default Theme" fill={defaultPalette.brandPrimary} />
                  <Bar dataKey="Alternative Theme" fill={alternativePalette.brandPrimary} />
                </BarChart>
              </ResponsiveContainer>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-4">
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm">Home Page</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`text-lg font-bold ${Number(calculateImprovement('Home')) > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {calculateImprovement('Home')}% {Number(calculateImprovement('Home')) > 0 ? '↑' : '↓'}
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm">Map Page</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`text-lg font-bold ${Number(calculateImprovement('Map')) > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {calculateImprovement('Map')}% {Number(calculateImprovement('Map')) > 0 ? '↑' : '↓'}
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Similar content for other tabs */}
          <TabsContent value="engagement" className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value, name) => [`${value} ${data[0]?.unit || ''}`, name]} />
                <Legend />
                <Bar dataKey="Default Theme" fill={defaultPalette.brandPrimary} />
                <Bar dataKey="Alternative Theme" fill={alternativePalette.brandPrimary} />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="conversions" className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value, name) => [`${value} ${data[0]?.unit || ''}`, name]} />
                <Legend />
                <Bar dataKey="Default Theme" fill={defaultPalette.brandPrimary} />
                <Bar dataKey="Alternative Theme" fill={alternativePalette.brandPrimary} />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>

        {testActive && (
          <div className="flex flex-col sm:flex-row gap-3 sm:justify-between mt-6">
            <Button variant="outline" onClick={() => handleEndTest('default')} className="w-full sm:w-auto">
              Select Default Theme as Winner
            </Button>
            <Button variant="outline" onClick={() => handleEndTest('alternative')} className="w-full sm:w-auto">
              Select Alternative Theme as Winner
            </Button>
          </div>
        )}

        <Button
          className="mt-4 w-full"
          onClick={fetchRealData}
          disabled={isLoading}
        >
          {isLoading ? 'Loading...' : 'Refresh Data'}
        </Button>
      </CardContent>
    </Card>
  );
};

export default ABTestResults;

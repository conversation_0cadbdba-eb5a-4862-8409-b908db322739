
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import type { Conversation, Message, MessageWithRole } from "@/types/conversations";



export const useMessages = (conversationId: string) => {
  const [messages, setMessages] = useState<MessageWithRole[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [conversation, setConversation] = useState<Conversation | null>(null);

  const fetchMessages = async () => {
    console.log('Fetching messages for conversation:', conversationId);
    try {
      // Recupera i dettagli della conversazione
      const { data: conversationData, error: conversationError } = await supabase
        .from('conversations_with_details')
        .select('*')
        .eq('id', conversationId)
        .single();

      // console.log('Conversation data:', conversationData);
      // console.log('Conversation error:', conversationError);
    
      if (conversationError) throw conversationError;
      setConversation(conversationData as Conversation);

      // Recupera i messaggi con il ruolo usando la nuova vista
      const { data, error } = await supabase
        .from('message_with_role')
        .select('*')
        .eq('conversation_id', conversationId)
       // .order('created_at', { ascending: true });

      // console.log('Messages data:', data);
      // console.log('Messages error:', error);

      if (error) throw error;

      setMessages(data || []);
    } catch (error) {
      console.error('Error fetching messages:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Imposta il listener per gli aggiornamenti in tempo reale
  useEffect(() => {
    console.log('Setting up real-time listener for conversation:', conversationId);
    const channel = supabase
      .channel(`chat-${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`
        },
        async (payload) => {
          console.log('New message received:', payload);
          
          // Recupera il ruolo dell'utente che ha inviato il messaggio
          const { data: roleData } = await supabase
            .from('conversation_participants')
            .select('role')
            .eq('conversation_id', conversationId)
            .eq('user_id', (payload.new as Message).user_id)
            .single();
            
          // Aggiungi il messaggio con il ruolo
          const messageWithRole: MessageWithRole = {
            ...(payload.new as Message),
            role: roleData?.role || 'participant'
          };
          
          setMessages(current => [...current, messageWithRole]);
        }
      )
      .subscribe();

    return () => {
      console.log('Cleaning up real-time listener for conversation:', conversationId);
      supabase.removeChannel(channel);
    };
  }, [conversationId]);

  // Carica i messaggi all'avvio
  useEffect(() => {
    console.log('Initializing messages for conversation:', conversationId);
    if (conversationId) {
      fetchMessages();
    }
  }, [conversationId]);

  return {
    messages,
    isLoading,
    conversation,
    refreshMessages: fetchMessages
  };
};

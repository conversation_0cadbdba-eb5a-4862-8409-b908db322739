import React from 'react';
import { usePWAInstall } from '@/hooks/pwa/usePWAInstall';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Download, Smartphone, X } from 'lucide-react';

interface PWAInstallPromptProps {
  className?: string;
  variant?: 'card' | 'button' | 'banner';
  showDismiss?: boolean;
}

export const PWAInstallPrompt: React.FC<PWAInstallPromptProps> = ({ 
  className = '', 
  variant = 'card',
  showDismiss = true 
}) => {
  const { canInstall, isInstalling, install, dismissInstall } = usePWAInstall();

  // Don't render if app can't be installed
  if (!canInstall) {
    return null;
  }

  const handleInstall = async () => {
    try {
            await install();
          } catch (error) {
            console.error('Failed to install PWA:', error);
            // Optionally show user-friendly error message
          }
  };

  const handleDismiss = () => {
    if (showDismiss) {
      dismissInstall();
    }
  };

  // Button variant - minimal install button
  if (variant === 'button') {
    return (
      <Button
        onClick={handleInstall}
        disabled={isInstalling}
        className={`gap-2 ${className}`}
        variant="outline"
      >
        <Download className="w-4 h-4" />
        {isInstalling ? 'Installing...' : 'Install App'}
      </Button>
    );
  }

  // Banner variant - top banner style
  if (variant === 'banner') {
    return (
      <div className={`bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200 ${className}`}>
        <div className="flex items-center justify-between px-4 py-3 max-w-7xl mx-auto">
          <div className="flex items-center gap-3">
            <Smartphone className="w-5 h-5 text-blue-600" />
            <div>
              <p className="text-sm font-medium text-blue-900">
                Install CatchUp for a better experience
              </p>
              <p className="text-xs text-blue-600">
                Get faster access and work offline
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={handleInstall}
              disabled={isInstalling}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isInstalling ? 'Installing...' : 'Install'}
            </Button>
            {showDismiss && (
              <Button
                onClick={handleDismiss}
                variant="ghost"
                size="sm"
                className="text-blue-600 hover:text-blue-700"
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Card variant - default card style
  return (
    <Card className={`max-w-md mx-auto ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Smartphone className="w-5 h-5" />
            Install CatchUp
          </CardTitle>
          {showDismiss && (
            <Button
              onClick={handleDismiss}
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
        <CardDescription>
          Install CatchUp on your device for faster access and offline functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Faster loading</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Offline access</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Native app feel</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Push notifications</span>
          </div>
        </div>
        <Button
          onClick={handleInstall}
          disabled={isInstalling}
          className="w-full gap-2"
        >
          <Download className="w-4 h-4" />
          {isInstalling ? 'Installing...' : 'Install CatchUp'}
        </Button>
      </CardContent>
    </Card>
  );
}; 
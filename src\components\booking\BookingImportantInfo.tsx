
const BookingImportantInfo = () => {
  return (
    <div className="mt-6 space-y-4 text-sm">
      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <h3 className="font-semibold text-yellow-800 mb-2">Informazioni importanti</h3>
        <ul className="list-disc list-inside text-yellow-700 space-y-1">
          <li>La prenotazione non è rimborsabile</li>
          <li>Presentati 5 minuti prima dell'orario prenotato</li>
          <li>Porta con te un documento d'identità</li>
        </ul>
      </div>
    </div>
  );
};

export default BookingImportantInfo;

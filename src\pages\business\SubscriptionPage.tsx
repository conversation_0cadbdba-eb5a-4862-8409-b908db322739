import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Check,
  ArrowLeft,
  Crown,
  Star,
  Zap,
  Users,
  CreditCard,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Switch } from "@/components/ui/switch";
import { useSubscription } from "@/hooks/useSubscription";
import {
  SubscriptionPlan,
  SubscriptionPlanDetails,
} from "@/types/subscription";
import { usePricingPlans } from "@/hooks/usePricingPlans";
import { toast } from "sonner";

const SubscriptionPage = () => {
  const navigate = useNavigate();
  const {
    createSubscription,
    cancelSubscription,
    isLoading,
    hasActiveSubscription,
    subscription,
  } = useSubscription();
  const {
    plans,
    isLoading: plansLoading,
    error: plansError,
  } = usePricingPlans();
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(
    null
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const [isYearly, setIsYearly] = useState(false);

  const getPriceSuffix = () => {
    return isYearly ? "/anno" : "/mese";
  };

  const getPlanIcon = (planId: SubscriptionPlan) => {
    switch (planId) {
      case "basic":
        return <Users className="w-6 h-6" />;
      case "professional":
        return <Star className="w-6 h-6" />;
      case "enterprise":
        return <Crown className="w-6 h-6" />;
      default:
        return <Users className="w-6 h-6" />;
    }
  };

  const getPlanTier = (planId: string) => {
    switch (planId) {
      case "basic":
        return 100;
      case "professional":
        return 200;
      case "enterprise":
        return 300;
      default:
        return 0;
    }
  };

  const isCurrentPlan = (planId: string) => {
    if (!hasActiveSubscription || !subscription) return false;
    // Handle the mapping between database plan types and UI plan types
    const currentPlanType = subscription.plan_type;
    const planMapping: Record<string, string> = {
      professional: "professional", // UI shows 'premium' as 'professional' in plans
      basic: "basic",
      enterprise: "enterprise",
    };
    return (
      planMapping[currentPlanType] === planId || currentPlanType === planId
    );
  };

  const isUpgrade = (planId: string) => {
    if (!hasActiveSubscription || !subscription) return false;
    const currentTier = getPlanTier(subscription.plan_type);
    const targetTier = getPlanTier(planId);
    return targetTier > currentTier;
  };

  const isDowngrade = (planId: string) => {
    if (!hasActiveSubscription || !subscription) return false;
    const currentTier = getPlanTier(subscription.plan_type);
    const targetTier = getPlanTier(planId);
    return targetTier < currentTier;
  };

  const getButtonLabel = (planId: string) => {
    if (isCurrentPlan(planId)) return "Annulla";
    if (isUpgrade(planId)) return "Upgrade";
    if (isDowngrade(planId)) return "Downgrade";
    return "Sottoscrivi";
  };

  const handleSelectPlan = async (planId: SubscriptionPlan) => {
    console.log("new planId", planId);
    setSelectedPlan(planId);
    setIsProcessing(true);

    try {
      if (isCurrentPlan(planId)) {
        // Cancel current subscription
        await cancelSubscription();
        toast.success("Sottoscrizione annullata con successo.");
        navigate("/profile");
      } else {
        // Determine the change type
        let changeType: "upgrade" | "downgrade" | undefined;
        if (hasActiveSubscription) {
          if (isUpgrade(planId)) {
            changeType = "upgrade";
          } else if (isDowngrade(planId)) {
            changeType = "downgrade";
          }
        }

        // Create new subscription (upgrade/downgrade/new)
        await createSubscription(
          planId,
          isYearly ? "yearly" : "monthly",
          changeType
        );

        const actionLabel =
          changeType === "upgrade"
            ? "Upgrade"
            : changeType === "downgrade"
            ? "Downgrade"
            : "Sottoscrizione";
        toast.success(
          `${actionLabel} completato! Ora puoi accedere alla Modalità Business.`
        );
        navigate("/profile");
      }
    } catch (error) {
      console.error("Error selecting plan:", error);
      const actionLabel = isCurrentPlan(planId)
        ? "annullare"
        : isUpgrade(planId)
        ? "aggiornare"
        : isDowngrade(planId)
        ? "declassare"
        : "attivare";
      toast.error(`Errore nell\'${actionLabel} la sottoscrizione. Riprova.`);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBack = () => {
    navigate("/profile");
  };

  if (isLoading || plansLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-pulse">Caricamento piani sottoscrizioni...</div>
      </div>
    );
  }

  if (plansError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-2">
            Errore nel caricamento dei piani
          </div>
          <button
            onClick={() => window.location.reload()}
            className="text-blue-600 hover:text-blue-800"
          >
            Riprova
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <button
              onClick={handleBack}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-xl font-semibold text-gray-900">
              Scegli il tuo piano
            </h1>
            <div className="w-16" /> {/* Spacer */}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Passa alla Modalità Business
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Scegli il piano che si adatta alle tue esigenze.
            </p>
          </motion.div>
        </div>

        {/* Current Subscription Card */}
        {hasActiveSubscription && subscription && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="max-w-2xl mx-auto mb-12"
          >
            <Card className="border-green-500 shadow-green-100 bg-gradient-to-r from-green-50 to-green-100">
              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4">
                  <div className="bg-green-500 text-white p-3 rounded-full">
                    {getPlanIcon(subscription.plan_type)}
                  </div>
                </div>
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Badge className="bg-green-500 text-white">
                    Piano Attivo
                  </Badge>
                  <Badge
                    variant="outline"
                    className="bg-white border-green-300 text-green-700"
                  >
                    {subscription.is_yearly ? "Annuale" : "Mensile"}
                  </Badge>
                </div>
                <CardTitle className="text-2xl font-bold text-green-800">
                  {plans[subscription.plan_type]?.name ||
                    subscription.plan_type}
                </CardTitle>
                <CardDescription className="text-green-700">
                  {plans[subscription.plan_type]?.description ||
                    "Il tuo piano attuale"}
                </CardDescription>
                <div className="mt-4">
                  <div className="flex items-end justify-center gap-2">
                    <span className="text-4xl font-bold text-green-800">
                      €
                      {subscription.is_yearly
                        ? plans[subscription.plan_type]?.price_yearly
                        : plans[subscription.plan_type]?.price_monthly}
                    </span>
                    <span className="text-green-600 text-lg">
                      {subscription.is_yearly ? "/anno" : "/mese"}
                    </span>
                  </div>
                  <div className="mt-2 text-sm text-green-600">
                    <span className="font-medium">Scade il:</span>{" "}
                    {new Date(subscription.end_date).toLocaleDateString(
                      "it-IT",
                      {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      }
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="text-center">
                <Button
                  onClick={() => handleSelectPlan(subscription.plan_type)}
                  disabled={isProcessing}
                  variant="outline"
                  className="border-green-500 text-green-700 hover:bg-green-50"
                >
                  {isProcessing && selectedPlan === subscription.plan_type ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-500"></div>
                      Elaborazione...
                    </div>
                  ) : (
                    "Annulla Sottoscrizione"
                  )}
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Billing Toggle */}
        <div className="flex justify-center mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex items-center gap-4 bg-white rounded-lg p-4 shadow-sm border"
          >
            <span
              className={`text-sm font-medium transition-colors ${
                !isYearly ? "text-gray-900" : "text-gray-500"
              }`}
            >
              Mensile
            </span>
            <Switch
              checked={isYearly}
              onCheckedChange={setIsYearly}
              className="data-[state=checked]:bg-blue-600"
            />
            <div className="flex items-center gap-2">
              <span
                className={`text-sm font-medium transition-colors ${
                  isYearly ? "text-gray-900" : "text-gray-500"
                }`}
              >
                Annuale
              </span>
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-700 text-xs px-2 py-1"
              >
                Risparmia 20%
              </Badge>
            </div>
          </motion.div>
        </div>

        {/* Plans Grid */}
        <div className="mb-12">
          {hasActiveSubscription && (
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                Cambia Piano
              </h3>
              <p className="text-gray-600">
                Scegli un piano diverso per aggiornare o declassare la tua
                sottoscrizione
              </p>
            </div>
          )}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {Object.entries(plans)
              .filter(
                ([planId]) => !hasActiveSubscription || !isCurrentPlan(planId)
              )
              .map(
                ([planId, plan]: [string, SubscriptionPlanDetails], index) => (
                  <motion.div
                    key={planId}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                  >
                    <Card
                     key={planId}
                      className={`relative h-full transition-all duration-200 hover:shadow-lg ${
                        planId === "professional"
                          ? "border-blue-500 shadow-blue-100"
                          : "border-gray-200"
                      }`}
                    >
                      {planId === "professional" && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <Badge className="bg-blue-500 text-white px-4 py-1">
                            Più Popolare
                          </Badge>
                        </div>
                      )}

                      <CardHeader className="text-center pb-4">
                        <div className="flex justify-center mb-4">
                          <div
                            className={`p-3 rounded-full ${
                              planId === "basic"
                                ? "bg-green-100 text-green-600"
                                : planId === "professional"
                                ? "bg-blue-100 text-blue-600"
                                : "bg-purple-100 text-purple-600"
                            }`}
                          >
                            {getPlanIcon(planId as SubscriptionPlan)}
                          </div>
                        </div>
                        <CardTitle className="text-2xl font-bold">
                          {plan.name}
                        </CardTitle>
                        <CardDescription className="text-gray-600">
                          {plan.description}
                        </CardDescription>
                        <div className="mt-4">
                          <div className="flex items-end gap-2">
                            <span className="text-4xl font-bold text-gray-900">
                              €
                              {isYearly
                                ? plan.price_yearly
                                : plan.price_monthly}
                            </span>
                            <span className="text-gray-500 text-lg">
                              {getPriceSuffix()}
                            </span>
                          </div>
                          {isYearly && (
                            <div className="mt-2 text-sm text-green-600">
                              Risparmi €{plan.price_yearly - plan.price_monthly}{" "}
                              all&apos;anno
                            </div>
                          )}
                        </div>
                      </CardHeader>

                      <CardContent className="space-y-6">
                        {/* Features */}
                        <div className="space-y-3">
                          {plan.features.map((feature, featureIndex) => (
                            <div
                              key={featureIndex}
                              className="flex items-start gap-3"
                            >
                              <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-700">{feature}</span>
                            </div>
                          ))}
                        </div>

                        {/* Key Stats */}
                        <div className="border-t pt-6">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <Zap className="w-4 h-4 text-gray-500" />
                              <span className="text-gray-600">
                                {plan.max_businesses === -1
                                  ? "Illimitato"
                                  : plan.max_businesses}{" "}
                                attività aziendali
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* CTA Button */}
                        <Button
                          onClick={() =>
                            handleSelectPlan(planId as SubscriptionPlan)
                          }
                          disabled={isProcessing}
                          className={`w-full ${
                            isUpgrade(planId)
                              ? "bg-green-600 hover:bg-green-700"
                              : isDowngrade(planId)
                              ? "bg-orange-600 hover:bg-orange-700"
                              : planId === "professional"
                              ? "bg-blue-600 hover:bg-blue-700"
                              : "bg-gray-900 hover:bg-gray-800"
                          }`}
                        >
                          {isProcessing && selectedPlan === planId ? (
                            <div className="flex items-center gap-2">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                              Elaborazione...
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <CreditCard className="w-4 h-4" />
                              {getButtonLabel(planId)}
                            </div>
                          )}
                        </Button>
                      </CardContent>
                    </Card>
                  </motion.div>
                )
              )}
          </div>
        </div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="max-w-3xl mx-auto"
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-center">Domande Frequenti</CardTitle>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="cancel-subscription">
                  <AccordionTrigger className="text-left">
                    Posso annullare la mia sottoscrizione in qualsiasi momento?
                  </AccordionTrigger>
                  <AccordionContent>
                    Sì, puoi annullare la tua sottoscrizione in qualsiasi
                    momento. Continuerai ad avere accesso fino alla fine del tuo
                    attuale periodo di fatturazione.
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="free-trial">
                  <AccordionTrigger className="text-left">
                    Cosa succede durante la prova gratuita?
                  </AccordionTrigger>
                  <AccordionContent>
                    Ottieni accesso completo a tutte le funzionalità del tuo
                    piano selezionato per 30 giorni. Nessun addebito fino alla
                    fine della prova.
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="plan-changes">
                  <AccordionTrigger className="text-left">
                    Posso aggiornare o declassare il mio piano?
                  </AccordionTrigger>
                  <AccordionContent>
                    Sì, puoi cambiare il tuo piano in qualsiasi momento. Le
                    modifiche hanno effetto immediato e la fatturazione è
                    proporzionale.
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="payment-methods">
                  <AccordionTrigger className="text-left">
                    Quali metodi di pagamento accettate?
                  </AccordionTrigger>
                  <AccordionContent>
                    Accettiamo tutte le principali carte di credito (Visa,
                    Mastercard, American Express), PayPal e bonifici bancari per
                    i piani annuali.
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="refund-policy">
                  <AccordionTrigger className="text-left">
                    Qual è la vostra politica di rimborso?
                  </AccordionTrigger>
                  <AccordionContent>
                    Offriamo una garanzia di rimborso di 30 giorni. Se non sei
                    soddisfatto entro i primi 30 giorni, forniremo un rimborso
                    completo.
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default SubscriptionPage;


import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { Booking } from "@/types/booking";
import { useAuth } from "@/hooks/auth/useAuth";

export const useBookings = () => {
  const { user } = useAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchBookings = async () => {
      if (!user) return;

      try {
        const { data, error } = await supabase
          .from('bookings')
          .select(`
            *,
            deals (
              title,
              images,
              business_id,
              businesses (
                name,
                address
              )
            )
          `)
          .eq('user_id', user.id)
          .order('booking_date', { ascending: false }).order('booking_time', { ascending: false });

          

        if (error) {
          toast.error("Errore nel caricamento delle prenotazioni");
          return;
        }
        console.log(data);
        setBookings(data);
      } catch (error) {
        console.error('Error fetching bookings:', error);
        toast.error("Si è verificato un errore");
      } finally {
        setIsLoading(false);
      }
    };

    fetchBookings();
  }, [user]);

  const filterBookings = (type: 'upcoming' | 'past' | 'cancelled') => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (type === 'cancelled') {
      return bookings.filter(booking => booking.status === 'cancelled');
    }

    return bookings.filter(booking => {
      const bookingDate = new Date(booking.booking_date);
      
      if (type === 'upcoming') {
        return bookingDate >= today && booking.status !== 'cancelled';
      } else {
        return bookingDate < today && booking.status !== 'cancelled';
      }
    });
  };

  return {
    bookings,
    isLoading,
    upcomingBookings: filterBookings('upcoming'),
    pastBookings: filterBookings('past'),
    cancelledBookings: filterBookings('cancelled')
  };
};

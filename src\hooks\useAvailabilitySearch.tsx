import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useLocationManagement } from "./location/useLocationManagement";


// export interface AvailabilityResponse {
//   date: string;
//   time?: string; 
//   businesses: BusinessAvailability[];
//   next_cursor: string | null;
// }
export interface BusinessAvailability {
  business_id: string;
  business_name: string;
  address: string;
  latitude: number;
  longitude: number;
  distance: number | null;
  photos: string[] | null;
  category_id: string;
  available_slots: AvailableSlots[];
}


// The available_slots actually contains:
export interface AvailableSlots {
  exceptions: any[];
  schedule: DaySchedule[];
}

export interface DaySchedule {
  day: number;           // 1
  day_name: string;      // "lunedì"
  time_slots: TimeSlot[];
}

export interface TimeSlot {
  available_seats: number;  // 3
  end_time: string;        // "17:00"
  start_time: string;      // "16:00"
}


export interface SearchLocation {
  lat: number;
  lng: number;
  radius?: number;
}

export interface TimeSlotQueryParams {
  date: string;
  time?: string;
  location?: SearchLocation;
  cityName?: string;
  category_id?: string;
  cursor?: string;
  limit?: number;
}


export interface SearchResults {
  businesses: BusinessAvailability[];
  next_cursor: string | null;
}

export interface SearchMetrics {
  queryTime: number;
  totalResults: number;
  isCached: boolean;
  pageNumber: number;
}

export function useAvailabilitySearch(
  initialParams?: Partial<TimeSlotQueryParams>
) {
  const [params, setParams] = useState<TimeSlotQueryParams>({
    date: initialParams?.date || new Date().toISOString().split("T")[0],
    time: initialParams?.time,
    location: initialParams?.location,
    cityName: initialParams?.cityName,
    category_id: initialParams?.category_id,
    limit: initialParams?.limit || 20,
  });

  const [results, setResults] = useState<BusinessAvailability[]>([]);
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<SearchMetrics>({
    queryTime: 0,
    totalResults: 0,
    isCached: false,
    pageNumber: 1,
  });

  const { userLocation } = useLocationManagement();

  const searchAvailability = useCallback(
    async (isLoadMore: boolean = false) => {
      if (!params.date) return;

      const searchLocation =
        params.location ||
        (userLocation
          ? { lat: userLocation.lat, lng: userLocation.lng }
          : undefined);

      if (!searchLocation && !params.cityName) {
        setError("Posizione non disponibile per la ricerca");
        return;
      }

      try {
        isLoadMore ? setIsLoadingMore(true) : setIsLoading(true);
        setError(null);

        const startTime = performance.now();
        console.log("startTime: ", startTime);
        const { data, error } = await supabase.functions.invoke(
          "get-availability-by-location",
          {
            body: {
              date: params.date,
              time: params.time,
              location: searchLocation,
              cityName: params.cityName,
              category_id: params.category_id,
              cursor: isLoadMore ? nextCursor : null,
              limit: params.limit,
            },
          }
        );
        console.log("availability: ", data);
        if (error) throw new Error(error.message);

        const queryTime = performance.now() - startTime;

        setMetrics((prev) => ({
          queryTime,
          totalResults: isLoadMore
            ? prev.totalResults + (data.businesses?.length || 0)
            : data.businesses?.length || 0,
          isCached: data.cached || false,
          pageNumber: isLoadMore ? prev.pageNumber + 1 : 1,
        }));

        setResults((prev) =>
          isLoadMore ? [...prev, ...data.businesses] : data.businesses || []
        );
        setNextCursor(data.next_cursor);
      } catch (err: any) {
        console.error("Errore nella ricerca disponibilità:", err);
        setError(err.message || "Errore durante la ricerca");
        toast.error("Impossibile completare la ricerca di disponibilità");
      } finally {
        isLoadMore ? setIsLoadingMore(false) : setIsLoading(false);
      }
    },
    [params, userLocation, nextCursor]
  );

  useEffect(() => {
    searchAvailability(false);
  }, [params, userLocation]);

  const updateSearchParams = (newParams: Partial<TimeSlotQueryParams>) => {
    setParams((prev) => ({ ...prev, ...newParams }));
    setNextCursor(null); // Reset pagination when params change
  };

  const loadMore = () => {
    if (nextCursor && !isLoadingMore) {
      searchAvailability(true);
    }
  };

  const refresh = () => {
    setNextCursor(null);
    searchAvailability(false);
  };

  return {
    results,
    isLoading,
    isLoadingMore,
    error,
    params,
    metrics,
    hasMore: !!nextCursor,
    updateSearchParams,
    loadMore,
    refresh,
  };
}

.business-marker-custom {
  cursor: pointer;
  position: relative;
  transform: translateY(-5px);
  transition: all 0.2s ease-in-out;
}

.business-marker-custom .tip {
  position: absolute;
  bottom: 0;
  width: 0;
  height: 0;
  border: 8px solid #1F2937;
  border-radius: 0;
  border-bottom-right-radius: 5px;
  z-index: -1;
  left: 50%;
  transform: translateY(22%) translateX(-50%) rotate(45deg);
  transition: all 0.2s ease-in-out;
}

.business-marker-custom .custom-pin {
  position: relative;
  height: 34px;
  width: fit-content;
  max-width: 34px;
  padding: 4px;
  background-color: #1F2937;
  border-radius: 6px;
  
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  
  transform-origin: bottom;
  transition:
    max-width 0.2s ease-in-out,
    height 0.2s ease-in-out,
    border-radius 0.2s ease-in-out;
  
  color: white;
  overflow: hidden;
}

.business-marker-custom .custom-pin .close-button {
  display: none;
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px;
  border: none;
  box-shadow: none;
  background: none;
  color: white;
  cursor: pointer;
  z-index: 10;
}

.business-marker-custom .custom-pin .business-info {
  width: 100%;
  height: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: opacity 0.2s ease-in-out;
}

.business-marker-custom .custom-pin .business-name {
  font-size: 12px;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 90%;
}

.business-marker-custom .custom-pin .deal-count {
  font-size: 11px;
  display: flex;
  align-items: center;
  color: crimson;
  margin-top: 4px;
}

.business-marker-custom .custom-pin .deal-count svg {
  margin-right: 4px;
}

/* Hover State */
.business-marker-custom.hovered {
  z-index: 2;
  transform: translateY(-9px);
}

.business-marker-custom.hovered .custom-pin {
  max-width: 160px;
  height: 60px;
  border-radius: 8px;
}

.business-marker-custom.hovered .tip {
  transform: translateY(23%) translateX(-50%) rotate(45deg) scale(1.2);
}

/* Clicked State */
.business-marker-custom.clicked {
  z-index: 3;
  transform: translateY(-9px);
}

.business-marker-custom.clicked .custom-pin {
  background-color: #1F2937;
  border-radius: 8px;
  width: fit-content;
  max-width: 300px;
  height: auto;
  min-height: 120px;
  padding: 12px;
}

.business-marker-custom.clicked .custom-pin .business-info {
  text-align: left;
  align-items: flex-start;
  margin-bottom: 8px;
  margin-top: 8px;
}

.business-marker-custom.clicked .custom-pin .business-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.business-marker-custom.clicked .custom-pin .close-button {
  display: flex;
}

.business-marker-custom.clicked .custom-pin .close-button span {
  font-size: 18px;
}

.business-marker-custom.clicked .custom-pin .deals-container {
  width: 100%;
  margin-top: 10px;
  animation: fadeIn 0.3s ease-in-out;
}

.business-marker-custom.clicked .custom-pin .deals-container h4 {
  font-size: 14px;
  margin: 0 0 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 4px;
}

.business-marker-custom.clicked .custom-pin .deals-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
}

.business-marker-custom.clicked .custom-pin .deals-list li {
  padding: 8px;
  margin-bottom: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.business-marker-custom.clicked .custom-pin .deals-list li:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.business-marker-custom.clicked .custom-pin .deal-title {
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 4px;
}

.business-marker-custom.clicked .custom-pin .deal-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.business-marker-custom.clicked .tip {
  transform: translateY(23%) translateX(-50%) rotate(45deg) scale(1.2);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.custom-pin {
  transition: all 0.3s ease-in-out;
}

.advanced-marker-example .real-estate-marker {
  cursor: pointer;
  position: relative;
  transform: translateY(-5px);
  transition: all 0.2s ease-in-out;
}

.advanced-marker-example .real-estate-marker .tip {
  position: absolute;
  bottom: 0;
  width: 0;
  height: 0;
  border: 8px solid var(--estate-green-dark);
  border-radius: 0;
  border-bottom-right-radius: 5px;
  z-index: -1;
  left: 50%;
  transform: translateY(22%) translateX(-50%) rotate(45deg);
  transition: all 0.2s ease-in-out;
}

.advanced-marker-example .custom-pin {
  position: relative;
  height: 34px;
  width: fit-content;
  max-width: 34px;
  padding: 4px;
  background-color: var(--estate-green-dark);
  border-radius: 50%;

  display: flex;
  justify-content: center;
  align-items: center;

  transform-origin: bottom;
  transition:
    max-width 0.2s ease-in-out,
    height 0.2s ease-in-out,
    border-radius 0.2s ease-in-out;
}

.advanced-marker-example .custom-pin .close-button {
  display: none;
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 8px;
  border: none;
  box-shadow: none;
  background: none;
  color: var(--estate-green-dark);
  cursor: pointer;
}

.advanced-marker-example .custom-pin .image-container {
  width: 100%;
  height: 100%;
  max-width: 285px;
  background-position: 50% 50%;
  background-size: cover;
  border-radius: inherit;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.2s ease-in-out;
}

.advanced-marker-example .custom-pin .image-container .icon {
  position: absolute;
  opacity: 1;
  transition: opacity 0.2s ease-in-out;
}

.advanced-marker-example .real-estate-marker.hovered {
  z-index: 2;

  transform: translateY(-9px);
}
.advanced-marker-example .real-estate-marker.hovered .custom-pin {
  max-width: 80px;
  height: 80px;
  border-radius: 50%;
}

.advanced-marker-example
  .real-estate-marker.hovered
  .custom-pin
  .image-container {
  opacity: 1;
  border-radius: inherit;
}

.advanced-marker-example
  .real-estate-marker.hovered
  .custom-pin
  .image-container
  .icon {
  opacity: 0;
}

.advanced-marker-example .real-estate-marker.hovered .tip {
  transform: translateY(23%) translateX(-50%) rotate(45deg) scale(1.4);
}

.advanced-marker-example .real-estate-marker.clicked {
  z-index: 3;

  transform: translateY(-9px);
}

.advanced-marker-example .real-estate-marker.clicked .custom-pin {
  background-color: var(--estate-green-dark);
  border-radius: 0;
  width: fit-content;
  max-width: 650px;
  height: 317px;
}

.advanced-marker-example
  .real-estate-marker.clicked
  .custom-pin
  .image-container {
  border-radius: inherit;
}

.advanced-marker-example
  .real-estate-marker.clicked
  .custom-pin
  .image-container
  .icon {
  opacity: 0;
  visibility: hidden;
}

.advanced-marker-example
  .real-estate-marker.clicked
  .custom-pin
  .details-container {
  max-width: 380px;
  opacity: 1;
  visibility: visible;

  animation: slideInFadeIn 0.7s ease-in-out;
}

.advanced-marker-example
  .real-estate-marker.clicked
  .custom-pin
  .details-container
  .close-button {
  display: flex;
}

.advanced-marker-example
  .real-estate-marker.clicked
  .custom-pin
  .details-container
  .close-button
  span {
  font-size: 24px;
}

.advanced-marker-example .real-estate-marker.clicked .tip {
  transform: translateY(23%) translateX(-50%) rotate(45deg) scale(1.4);
}

@keyframes slideInFadeIn {
  0% {
    max-width: 0;
    opacity: 0;
    visibility: hidden;
  }

  75% {
    max-width: 380px;
    opacity: 0;
    visibility: hidden;
  }

  100% {
    max-width: 380px;
    opacity: 1;
    visibility: visible;
  }
}

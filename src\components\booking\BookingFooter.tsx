
import { Users, AlertTriangle } from "lucide-react";

interface BookingFooterProps {
  onConfirm: () => void;
  isLoading: boolean;
  availableSeats?: number;
}

const BookingFooter = ({ onConfirm, isLoading, availableSeats }: BookingFooterProps) => {
  // Determina se la disponibilità è bassa (3 o meno posti)
  const isLowAvailability = availableSeats !== undefined && availableSeats > 0 && availableSeats <= 3;

  return (
    <footer className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
      {availableSeats !== undefined && (
        <div className="mb-2">
          {isLowAvailability ? (
            <div className="flex items-center gap-2 bg-amber-50 p-2 rounded-lg border border-amber-200 mb-2">
              <AlertTriangle className="h-4 w-4 text-amber-600" />
              <span className="text-sm text-amber-700 font-medium">
                {availableSeats === 1 
                  ? "Attenzione: ultimo posto disponibile!" 
                  : `Attenzione: solo ${availableSeats} posti rimasti!`}
              </span>
            </div>
          ) : availableSeats === 0 ? (
            <div className="flex items-center gap-2 bg-red-50 p-2 rounded-lg border border-red-200 mb-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-700 font-medium">
                Non ci sono più posti disponibili per questo orario
              </span>
            </div>
          ) : (
            <div className="flex items-center gap-1 mb-2">
              <Users className="h-4 w-4 text-green-600" />
              <span className="text-sm text-green-600 font-medium">
                {availableSeats} posti disponibili
              </span>
            </div>
          )}
          
          {/* Indicatore grafico di disponibilità */}
          {availableSeats > 0 && (
            <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
              <div 
                className={`h-2 rounded-full ${isLowAvailability ? 'bg-amber-500' : 'bg-green-500'}`}
                style={{ width: `${Math.min(100, isLowAvailability ? 30 : 100)}%` }}
              ></div>
            </div>
          )}
        </div>
      )}
      
      <button
        onClick={onConfirm}
        disabled={isLoading || (availableSeats !== undefined && availableSeats <= 0)}
        className="w-full bg-brand-primary text-white py-3 rounded-full font-semibold disabled:opacity-50"
      >
        {isLoading ? "Conferma in corso..." : "Conferma Prenotazione"}
      </button>
      
      {availableSeats !== undefined && availableSeats <= 0 && (
        <p className="text-center text-sm text-red-500 mt-2">
          Questa fascia oraria è al completo. Prova a selezionare un altro orario.
        </p>
      )}
    </footer>
  );
};

export default BookingFooter;

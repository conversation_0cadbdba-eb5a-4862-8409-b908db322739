import { type FormData } from "@/types/deals";

import { toast } from "sonner";
import {
  <PERSON><PERSON><PERSON>,
  ImageIcon,
  ChevronLeft,
  ChevronRight,
  Check,
} from "lucide-react";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { TimeSlotManager } from "../booking";
import DealPricing from "../core/DealPricing";
import DealImages from "../details/DealImages";
import { DealCategoriesSelect } from "../ui/DealCategoriesSelect";

/**
 * CreateDealForm Props
 * @param formData - The form data object containing all deal information
 * @param setFormData - Function to update the form data state
 * @param existingImages - Array of existing image URLs (if editing a deal)
 * @param onRemoveExistingImage - Function to remove an existing image
 * @param businessId - The ID of the business creating the deal
 * @param onSubmit - Callback function triggered when the form is submitted
 * @param onCancel - Callback function triggered when form submission is cancelled
 */
interface CreateDealFormProps {
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  existingImages?: string[];
  onRemoveExistingImage?: (index: number) => void;
  businessId: string;
  onSubmit: () => void;
  onCancel: () => void;
}

const targetAudiences = [
  { value: "famiglie", label: "Famiglie" },
  { value: "giovani", label: "Giovani" },
  { value: "professionisti", label: "Professionisti" },
  { value: "anziani", label: "Anziani" },
];

// Steps for the form
const steps = [
  "Titolo",
  "Immagini",
  "Prezzi",
  "Orari",
  "Riepilogo",
];

 const CreateDealForm = ({
  formData,
  setFormData,
  existingImages,
  onRemoveExistingImage,
  businessId,
  onSubmit,
  onCancel
}: CreateDealFormProps) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedTargetAudience, setSelectedTargetAudience] = useState(
    targetAudiences[0].value
  );
  const [businessType, setBusinessType] = useState<string>("");
  const [businessName, setBusinessName] = useState<string>("");
  const [businessCategoryId, setBusinessCategoryId] = useState<string>("");
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [imagePrompt, setImagePrompt] = useState("");
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [generatedImageUrl, setGeneratedImageUrl] = useState<string | null>(
    null
  );

  // State for multi-step form
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    const fetchBusinessInfo = async () => {
      try {
        const { data: business, error: businessError } = await supabase
          .from("businesses")
          .select("category_id, name")
          .eq("id", businessId)
          .maybeSingle();

        if (businessError) throw businessError;

        if (business?.name) {
          setBusinessName(business.name);
        }

        if (business?.category_id) {
          // Set the business category ID for filtering deal categories
          setBusinessCategoryId(business.category_id);
          
          const { data: category, error: categoryError } = await supabase
            .from("categories")
            .select("name")
            .eq("id", business.category_id)
            .maybeSingle();

          if (categoryError) throw categoryError;

          if (category?.name) {
            setBusinessType(category.name.toLowerCase());
          }
        }
      } catch (error) {
        console.error(
          "Errore nel recupero delle informazioni business:",
          error
        );
        toast.error("Errore nel recupero delle informazioni business");
      }
    };

    fetchBusinessInfo();
  }, [businessId]);

  const generateContent = async () => {
    if (!businessType) {
      toast.error("Categoria business non disponibile");
      return;
    }

    try {
      setIsGenerating(true);
      
      // Get selected deal category names for better AI generation
      let selectedCategoryNames: string[] = [];
      if (formData.deal_categories.length > 0) {
        try {
          const { data: categories, error } = await supabase
            .from("deal_categories")
            .select("name")
            .in("id", formData.deal_categories);
          
          if (error) throw error;
          
          selectedCategoryNames = categories?.map(cat => cat.name) || [];
        } catch (error) {
          console.error("Errore nel recupero delle categorie:", error);
          // Fallback: usa gli ID se il fetch fallisce
          selectedCategoryNames = formData.deal_categories;
        }
      }

      const { data, error } = await supabase.functions.invoke(
        "generate-deal-content",
        {
          body: {
            businessType,
            targetAudience: selectedTargetAudience,
            businessName,
            dealCategories: selectedCategoryNames,
          },
        }
      );

      if (error) throw error;

      if (data.title) {
        setFormData((prev) => ({ ...prev, title: data.title }));
      }
      if (data.description) {
        setFormData((prev) => ({ ...prev, description: data.description }));
      }

      // Show additional information if available
      if (data.serviceDetails) {
        console.log("Service details:", data.serviceDetails);
      }
      if (data.suggestedPrice) {
        console.log("Suggested price:", data.suggestedPrice);
        // Optionally set the original price if it's empty
        if (!formData.original_price) {
          const priceMatch = data.suggestedPrice.match(/(\d+)/);
          if (priceMatch) {
            setFormData((prev) => ({ ...prev, original_price: priceMatch[1] }));
          }
        }
      }

      toast.success("Contenuto generato con successo!");
    } catch (error) {
      console.error("Errore durante la generazione del contenuto:", error);
      toast.error("Errore durante la generazione del contenuto");
    } finally {
      setIsGenerating(false);
    }
  };

  const generateImage = async () => {
    if (!businessType && !imagePrompt) {
      toast.error("Inserisci una descrizione dell'immagine");
      return;
    }

    try {
      setIsGeneratingImage(true);
      setGeneratedImageUrl(null);

      const { data, error } = await supabase.functions.invoke(
        "generate-business-image",
        {
          body: {
            prompt: imagePrompt,
            businessType,
            businessName,
          },
        }
      );

      if (error) {
        console.error("Errore chiamata edge function:", error);
        throw error;
      }

      if (!data.success) {
        throw new Error(
          data.message || "Errore durante la generazione dell'immagine"
        );
      }

      // Se abbiamo un URL dell'immagine diretto, lo utilizziamo
      if (data.imageUrl) {
        setGeneratedImageUrl(data.imageUrl);
    //    toast.success("Immagine generata con successo!");
      } else if (data.id) {
        toast.info(
          "La generazione dell'immagine potrebbe richiedere qualche minuto"
        );
      }
    } catch (error) {
      console.error("Errore completo:", error);
      toast.error("Errore durante la generazione dell'immagine");
    } finally {
      setIsGeneratingImage(false);
    }
  };

  const saveGeneratedImage = async () => {
    if (!generatedImageUrl) return;

    try {
      // Scarica l'immagine
      const response = await fetch(generatedImageUrl);
      if (!response.ok) throw new Error("Impossibile scaricare l'immagine");

      const blob = await response.blob();

      // Crea un File dalla blob
      const file = new File([blob], `immagine-generata-${Date.now()}.jpg`, {
        type: "image/jpeg",
      });

      // Aggiungi il File all'array delle immagini
      setFormData((prev) => ({
        ...prev,
        images: [...prev.images, file],
      }));

      // Chiudi il dialogo
      setIsImageDialogOpen(false);
      setGeneratedImageUrl(null);
      setImagePrompt("");

    //  toast.success("Immagine salvata con successo!");
    } catch (error) {
      console.error("Errore durante il salvataggio dell'immagine:", error);
      toast.error("Errore durante il salvataggio dell'immagine");
    }
  };

  // Check if current step is valid before proceeding
  const isStepValid = () => {
    switch (currentStep) {
      case 0: // Title & Description
        return (
          formData.title.trim() !== "" && formData.description.trim() !== ""
        );
      case 1: // Images
        return (
          formData.images.length > 0 ||
          (existingImages && existingImages.length > 0)
        );
      case 2: // Pricing
        return (
          formData.original_price !== "" && formData.discount_percentage !== ""
        );
      case 3: // Timing
        return formData.start_date !== "" && formData.end_date !== "";
      default:
        return true;
    }
  };

  // Navigate to next step if validation passes
  const handleNext = () => {
    if (isStepValid()) {
      setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
    } else {
      toast.error("Completa tutti i campi obbligatori prima di procedere");
    }
  };

  // Navigate to previous step
  const handlePrev = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  // Stepper component to show progress
  const Stepper = () => {
    return (
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                  index < currentStep
                    ? "bg-brand-primary text-white border-brand-primary"
                    : index === currentStep
                    ? "border-brand-primary text-brand-primary"
                    : "border-gray-300 text-gray-400"
                }`}
              >
                {index < currentStep ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <span>{index + 1}</span>
                )}
              </div>
              <span
                className={`text-xs mt-1 text-center ${
                  index === currentStep
                    ? "text-brand-primary font-medium"
                    : "text-gray-500"
                }`}
              >
                {step}
              </span>
            </div>
          ))}
        </div>
        <div className="relative mt-2">
          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-full h-0.5 bg-gray-200"></div>
          <div
            className="absolute left-0 top-1/2 transform -translate-y-1/2 h-0.5 bg-brand-primary transition-all"
            style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
          ></div>
        </div>
      </div>
    );
  };

  // Form steps content
  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Title & Description
        return (
          <div className="space-y-6">
            {/* Deal Categories Section */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900">Categorie Offerta</h3>
              <DealCategoriesSelect
                selectedCategories={formData.deal_categories}
                onCategoriesChange={(categories) =>
                  setFormData((prev) => ({ ...prev, deal_categories: categories }))
                }
                businessCategoryId={businessCategoryId}
              />
            </div>

            {/* Title Section */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900">Titolo dell'offerta</h3>
              <Input
                type="text"
                required
                value={formData.title}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, title: e.target.value }))
                }
                placeholder="Inserisci il titolo dell'offerta"
                className="text-lg"
              />
            </div>

            {/* Description Section */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900">Descrizione</h3>
              <textarea
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-brand-primary resize-none"
                rows={4}
                placeholder="Descrivi la tua offerta in modo dettagliato"
                required
              />
            </div>

            {/* AI Content Generation Section */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900">Migliora con AI</h3>
              <div className="flex items-center gap-3">
                <Select
                  value={selectedTargetAudience}
                  onValueChange={setSelectedTargetAudience}
                >
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Seleziona target" />
                  </SelectTrigger>
                  <SelectContent>
                    {targetAudiences.map((audience) => (
                      <SelectItem key={audience.value} value={audience.value}>
                        {audience.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateContent}
                  disabled={isGenerating || !businessType}
                  className="flex items-center gap-2"
                >
                  <Sparkles className="h-4 w-4" />
                  <span>
                    {isGenerating ? "Generazione..." : "Genera contenuto"}
                  </span>
                </Button>
              </div>
              <p className="text-sm text-gray-500">
                Seleziona il tuo target di riferimento e migliora titolo e descrizione con l'AI
              </p>
            </div>
          </div>
        );

      case 1: // Images
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <label className="block text-sm font-medium text-gray-700">
                  Immagini
                </label>
                <button
                  type="button"
                  onClick={() => setIsImageDialogOpen(true)}
                  className="flex items-center gap-1 text-sm text-brand-primary hover:text-brand-primary/80 transition-colors"
                >
                  <Sparkles className="h-4 w-4" />
                  <span>Genera immagine</span>
                </button>
              </div>
              <DealImages
                images={formData.images}
                existingImages={existingImages}
                onImagesChange={(e) => {
                  const files = Array.from(e.target.files || []);
                  setFormData((prev) => ({
                    ...prev,
                    images: [...prev.images, ...files],
                  }));
                }}
                onImageRemove={(index) => {
                  setFormData((prev) => ({
                    ...prev,
                    images: prev.images.filter((_, i) => i !== index),
                  }));
                }}
                onExistingImageRemove={onRemoveExistingImage}
              />
            </div>
          </div>
        );

      case 2: // Pricing
        return (
          <div className="space-y-4">
            <DealPricing
              originalPrice={formData.original_price}
              discountPercentage={formData.discount_percentage}
              discountedPrice={formData.discounted_price}
              onOriginalPriceChange={(value) =>
                setFormData((prev) => ({ ...prev, original_price: value }))
              }
              onDiscountPercentageChange={(value) =>
                setFormData((prev) => ({ ...prev, discount_percentage: value }))
              }
              onDiscountedPriceChange={(value) =>
                setFormData((prev) => ({ ...prev, discounted_price: value }))
              }
            />
          </div>
        );

      case 3: // Timing
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Date di validità
              </label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">
                    Data inizio
                  </label>
                  <input
                    type="date"
                    required
                    value={formData.start_date}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        start_date: e.target.value,
                      }))
                    }
                    className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">
                    Data fine
                  </label>
                  <input
                    type="date"
                    required
                    value={formData.end_date}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        end_date: e.target.value,
                      }))
                    }
                    className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
                  />
                </div>
              </div>
            </div>
            <TimeSlotManager
              value={formData.time_slots}
              onChange={(newSchedule) =>
                setFormData((prev) => ({ ...prev, time_slots: newSchedule }))
              }
            />
          </div>
        );

      case 4: // Summary
        return (
          <div className="space-y-6">
            <div className="bg-gray-50 p-4 rounded-lg space-y-3">
              <h3 className="font-medium text-lg">{formData.title}</h3>
              <p className="text-gray-600">{formData.description}</p>
              
              <div className="grid grid-cols-2 gap-4 mt-4">
                <div>
                  <p className="text-sm text-gray-500">Prezzo originale</p>
                  <p className="font-medium">€{formData.original_price}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Sconto</p>
                  <p className="font-medium">{formData.discount_percentage}%</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Prezzo scontato</p>
                  <p className="font-medium text-brand-primary">€{formData.discounted_price}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Validità</p>
                  <p className="font-medium">
                    {new Date(formData.start_date).toLocaleDateString('it-IT')} - {new Date(formData.end_date).toLocaleDateString('it-IT')}
                  </p>
                </div>
              </div>

              <div>
                <p className="text-sm text-gray-500 mb-2">Immagini</p>
                <div className="flex flex-wrap gap-2">
                  {formData.images.map((file, i) => (
                    <div key={i} className="w-20 h-20 border border-gray-200 rounded-lg overflow-hidden">
                      <img 
                        src={URL.createObjectURL(file)} 
                        alt={`Immagine ${i+1}`} 
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                  {existingImages?.map((imgUrl, i) => (
                    <div key={`existing-${i}`} className="w-20 h-20 border border-gray-200 rounded-lg overflow-hidden">
                      <img 
                        src={imgUrl} 
                        alt={`Immagine esistente ${i+1}`} 
                        className="w-full h-full object-cover" 
                      />
                    </div>
                  ))}
                  {formData.images.length === 0 && (!existingImages || existingImages.length === 0) && (
                    <div className="text-sm text-gray-400">Nessuna immagine caricata</div>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Stato dell'offerta
              </label>
              <Select
                value={formData.status}
                onValueChange={(value) =>
                  setFormData((prev) => ({
                    ...prev,
                    status: value as "draft" | "published" | "expired",
                  }))
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Seleziona lo stato" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Bozza</SelectItem>
                  <SelectItem value="published">Pubblicata</SelectItem>
                  <SelectItem value="expired">Scaduta</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col pb-20 relative">
      {/* Stepper */}
      <Stepper />
      
      {/* Step content */}
      <div className="flex-grow">
        {renderStepContent()}
      </div>
      
      {/* Navigation buttons - Mobile style bottom toolbar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-10 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrev}
            disabled={currentStep === 0}
            className="flex items-center gap-1"
          >
            <ChevronLeft className="h-4 w-4" />
            Indietro
          </Button>
          
          {currentStep < steps.length - 1 ? (
            <Button
              type="button"
              onClick={handleNext}
              className="flex items-center gap-1"
            >
              Avanti
              <ChevronRight className="h-4 w-4" />
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                className="flex items-center gap-1"
                onClick={onCancel}
              >
                Annulla
              </Button>
              <Button
                type="button"
                className="flex items-center gap-1 bg-brand-primary hover:bg-brand-primary/90"
                onClick={onSubmit}
              >
                Pubblica
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Dialog per la generazione di immagini */}
      <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
        <DialogContent className="max-w-[calc(100%-2rem)] w-full sm:max-w-md bg-white !bg-opacity-100 border border-gray-300 rounded-lg">
          <DialogHeader className="border-b border-gray-200 pb-2">
            <DialogTitle className="text-center">Genera immagine con AI</DialogTitle>
            <DialogDescription className="text-center">
              Descrivi l'immagine che desideri generare per la tua attività
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4 bg-white">
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Descrizione immagine
              </label>
              <Input
                placeholder="Es: Interno di un ristorante moderno con tavoli in legno"
                value={imagePrompt}
                onChange={(e) => setImagePrompt(e.target.value)}
                className="border border-gray-300 bg-white"
              />
              <p className="text-xs text-gray-500">
                Lascia vuoto per generare automaticamente in base al tipo di
                attività
              </p>
            </div>

            <div className="flex justify-end gap-2 mt-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsImageDialogOpen(false)}
                disabled={isGeneratingImage}
                className="bg-white border-gray-300"
              >
                Annulla
              </Button>
              <Button
                type="button"
                onClick={() => generateImage()}
                disabled={isGeneratingImage}
                className="bg-brand-primary text-white hover:bg-brand-primary/90 min-w-[100px]"
              >
                {isGeneratingImage ? (
                  <span className="flex items-center justify-center">
                    <span className="w-2 h-2 bg-white rounded-full mx-0.5 animate-[bounce_1s_infinite_0ms]"></span>
                    <span className="w-2 h-2 bg-white rounded-full mx-0.5 animate-[bounce_1s_infinite_250ms]"></span>
                    <span className="w-2 h-2 bg-white rounded-full mx-0.5 animate-[bounce_1s_infinite_500ms]"></span>
                  </span>
                ) : generatedImageUrl ? "Rigenera" : "Genera"}
              </Button>
            </div>

            {generatedImageUrl && (
              <div className="mt-4">
                <div className="border rounded-lg overflow-hidden">
                  <img
                    src={generatedImageUrl}
                    alt="Immagine generata"
                    className="w-full h-auto object-cover"
                  />
                </div>
                <div className="flex justify-end mt-2 gap-2">
                
                  <Button 
                    type="button" 
                    onClick={saveGeneratedImage}
                    className="bg-brand-primary text-white hover:bg-brand-primary/90"
                  >
                    Usa immagine
                  </Button>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CreateDealForm;
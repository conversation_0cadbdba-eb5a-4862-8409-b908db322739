import { motion } from "framer-motion";

interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
  stepLabels?: string[];
}

export const StepIndicator = ({ currentStep, totalSteps, stepLabels }: StepIndicatorProps) => {
  return (
    <div className="max-w-sm mx-auto mb-8">
      <div className="flex items-center justify-center gap-2">
        {Array.from({ length: totalSteps }, (_, index) => (
          <div key={index} className="flex items-center">
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ 
                scale: index <= currentStep ? 1 : 0.8,
                backgroundColor: index <= currentStep ? "hsl(var(--brand-primary))" : "hsl(var(--gray-300))"
              }}
              transition={{ duration: 0.3 }}
              className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                index <= currentStep ? "text-white" : "text-gray-500"
              }`}
            >
              {index + 1}
            </motion.div>
            {index < totalSteps - 1 && (
              <motion.div
                initial={{ scaleX: 0 }}
                animate={{ 
                  scaleX: index < currentStep ? 1 : 0,
                  backgroundColor: index < currentStep ? "hsl(var(--brand-primary))" : "hsl(var(--gray-300))"
                }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className={`h-0.5 w-8 mx-1 origin-left ${
                  index < currentStep ? "bg-brand-primary" : "bg-gray-300"
                }`}
              />
            )}
          </div>
        ))}
      </div>
      
      <div className="mt-3">
        <motion.div
          initial={{ width: "0%" }}
          animate={{ width: `${((currentStep + 1) / totalSteps) * 100}%` }}
          transition={{ duration: 0.3 }}
          className="h-1 bg-brand-primary rounded-full"
        />
        <div className="h-1 bg-gray-200 rounded-full -mt-1" />
      </div>
    </div>
  );
};
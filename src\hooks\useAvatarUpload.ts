
import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export const useAvatarUpload = () => {
  const [avatar, setAvatar] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast.error("Per favore seleziona un'immagine");
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      toast.error("L'immagine deve essere più piccola di 5MB");
      return;
    }

    setAvatar(file);
    setAvatarPreview(URL.createObjectURL(file));
  };

  const uploadAvatar = async (userId: string): Promise<string | null> => {
    if (!avatar) return null;

    try {
      setIsUploadingAvatar(true);
      const fileExt = avatar.name.split('.').pop();
      const filePath = `${userId}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, avatar, {
          upsert: true,
        });

      if (uploadError) {
        throw uploadError;
      }

      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      return publicUrl;
    } catch (error) {
      console.error('Errore durante il caricamento dell\'avatar:', error);
      toast.error("Errore durante il caricamento dell'immagine");
      return null;
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  return {
    avatar,
    avatarPreview,
    isUploadingAvatar,
    handleFileChange,
    uploadAvatar
  };
};

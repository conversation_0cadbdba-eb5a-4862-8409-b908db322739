import React, { useState } from 'react';
import VoiceDialog from './VoiceDialog';

const VoiceDialogTest = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Voice Dialog Test</h1>
      <button
        onClick={() => setIsOpen(true)}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Open Voice Dialog
      </button>
      
      <VoiceDialog
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        isFullScreen={true}
      />
    </div>
  );
};

export default VoiceDialogTest;

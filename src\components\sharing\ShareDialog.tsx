import { useState } from "react";
import { Check, Users } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useUserGroups, type UserGroup } from "@/hooks/sharing/useUserGroups";
import { cn } from "@/lib/utils";

interface ShareDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description?: string;
  onShare: (groupIds: string[], message?: string) => void;
  isLoading?: boolean;
}

export const ShareDialog = ({
  open,
  onOpenChange,
  title,
  description,
  onShare,
  isLoading = false,
}: ShareDialogProps) => {
  const [selectedGroups, setSelectedGroups] = useState<Set<string>>(new Set());
  const [message, setMessage] = useState("");
  
  const { data: groups, isLoading: groupsLoading } = useUserGroups();

  const handleGroupToggle = (groupId: string) => {
    const newSelected = new Set(selectedGroups);
    if (newSelected.has(groupId)) {
      newSelected.delete(groupId);
    } else {
      newSelected.add(groupId);
    }
    setSelectedGroups(newSelected);
  };

  const handleShare = () => {
    if (selectedGroups.size === 0) return;
    
    onShare(Array.from(selectedGroups), message.trim() || undefined);
    
    // Reset form
    setSelectedGroups(new Set());
    setMessage("");
    onOpenChange(false);
  };

  const getGroupInitials = (group: UserGroup) => {
    return group.name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {title}
          </DialogTitle>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </DialogHeader>

        <div className="space-y-4">
          {/* Groups Selection */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Seleziona gruppi:</h4>
            
            {groupsLoading ? (
              <div className="space-y-2">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center space-x-3 p-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-1 flex-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                ))}
              </div>
            ) : groups && groups.length > 0 ? (
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {groups.map((group) => {
                  const isSelected = selectedGroups.has(group.id);
                  
                  return (
                    <Card
                      key={group.id}
                      className={cn(
                        "cursor-pointer transition-colors hover:bg-muted/50",
                        isSelected && "bg-primary/10 border-primary"
                      )}
                      onClick={() => handleGroupToggle(group.id)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-10 w-10">
                              <AvatarImage src={group.avatar_url} />
                              <AvatarFallback className="bg-primary/10 text-primary">
                                {getGroupInitials(group)}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-sm">{group.name}</p>
                              <div className="flex items-center space-x-2">
                                <p className="text-xs text-muted-foreground">
                                  {group.member_count} membri
                                </p>
                                {group.user_role === 'admin' && (
                                  <Badge variant="secondary" className="text-xs">
                                    Admin
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          {isSelected && (
                            <div className="h-5 w-5 rounded-full bg-primary flex items-center justify-center">
                              <Check className="h-3 w-3 text-primary-foreground" />
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground py-8 text-center">
                Non fai parte di nessun gruppo ancora.
                <br />
                Crea o unisciti a un gruppo per condividere!
              </p>
            )}
          </div>

          {/* Optional Message */}
          {selectedGroups.size > 0 && (
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Messaggio (opzionale):
              </label>
              <Textarea
                placeholder="Aggiungi un commento..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                maxLength={300}
                rows={3}
              />
              {message && (
                <p className="text-xs text-muted-foreground text-right">
                  {message.length}/300
                </p>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Annulla
            </Button>
            <Button
              onClick={handleShare}
              disabled={selectedGroups.size === 0 || isLoading}
            >
              {isLoading ? "Condividendo..." : `Condividi in ${selectedGroups.size} gruppo${selectedGroups.size !== 1 ? 'i' : ''}`}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
import React from "react";
import { AdvancedMarker, Pin } from "@vis.gl/react-google-maps";

const PinWrapper = (props: {
  background: string;
  borderColor: string;
  glyphColor: string;
  scale: number;
}) => {
  // Filtra solo le proprietà accettate da Pin per evitare errori con data-lov-id
  const { background, borderColor, glyphColor, scale } = props;
  return React.createElement(Pin, { background, borderColor, glyphColor, scale });
};

/**
 * Marker component for selected location
 */
export const SelectedLocationMarker = ({ 
  position, 
  title 
}: { 
  position: { lat: number, lng: number };
  title?: string;
}) => {
  return (
    <AdvancedMarker
      position={position}
      zIndex={2000}
      title={title || "Posizione selezionata"}
    >
      <div className="relative">
        {/* Effetto pulsante animato */}
        <div className="absolute w-12 h-12 rounded-full bg-green-500 opacity-25 animate-ping" style={{ top: '-6px', left: '-6px' }}></div>
        {/* Pin di Google Maps con colore verde */}
        <PinWrapper
          background={"#22C55E"} 
          borderColor={"#FFFFFF"}
          glyphColor={"#FFFFFF"}
          scale={1.0}
        />
      </div>
    </AdvancedMarker>
  );
};
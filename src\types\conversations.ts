
import type { Database } from "@/integrations/supabase/types";

export type ConversationRole = 'owner' | 'customer' | 'participant';

export type Conversation = Database['public']['Tables']['conversations']['Row'] & {
  business_name?: string;
  business_photo?: string;
  deal_title?: string;
  deal_image?: string;
  participants?: Array<{
    user_id: string;
    first_name: string | null;
    last_name: string | null;
    avatar_url: string | null;
    role: ConversationRole;
  }>;
  last_message?: {
    id: string;
    content: string;
    created_at: string;
    user_id: string;
  };
  unread_count?: number;
  is_booking?: boolean;
  owner_id?: string;
};

export type Message = Database['public']['Tables']['messages']['Row'];

export type MessageWithRole = Message & {
  role: ConversationRole;
  business_name?: string;
  first_name?: string;
  
};

export type ConversationParticipant = Database['public']['Tables']['conversation_participants']['Row'];

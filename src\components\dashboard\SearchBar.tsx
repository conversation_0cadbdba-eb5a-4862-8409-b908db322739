import { Search } from "lucide-react";
import { useEffect, useState } from "react";

/**
 * SearchBar Component
 * 
 * A reusable search input component with an icon.
 * 
 * @param {Object} props - Component props
 * @param {string} props.value - The current search query value
 * @param {function} props.onChange - Callback function when search query changes
 * @param {string} [props.placeholder="Cerca offerte e attività"] - Placeholder text for the input
 */
interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const SearchBar = ({ 
  value, 
  onChange, 
  placeholder = "Cerca offerte e attività" 
}: SearchBarProps) => {
  return (
    <section className="mt-6 space-y-4">
      <div className="relative">
        <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
        <input
          type="text"
          placeholder={placeholder}
          className="w-full px-4 py-3 pl-12 bg-white rounded-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-brand-primary"
          value={value}
          onChange={(e) => onChange(e.target.value)}
        />
      </div>
    </section>
  );
};

export default SearchBar;

interface DealDetailsImageProps {
  images: string[] | null;
  id: string;
  title: string;
  discountPercentage?: number | null;
}

const DealDetailsImage = ({ images, id, title, discountPercentage }: DealDetailsImageProps) => {
  return (
    <div className="relative h-[300px]">
      <img
        className="w-full h-full object-cover"
        src={images ? images[0] : `https://picsum.photos/800/600?random=${id}`}
        alt={title}
      />
      {discountPercentage && (
        <div className="absolute bottom-4 right-4 bg-white rounded-full px-4 py-2 shadow-lg">
          <span className="text-brand-primary font-bold">-{discountPercentage}%</span>
        </div>
      )}
    </div>
  );
};

export default DealDetailsImage;

import React from "react";

interface DateRangeSelectorProps {
  startDate: string;
  endDate: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  title: string;
  description?: string;
  startLabel?: string;
  endLabel?: string;
}

/**
 * A reusable component for selecting a date range.
 * 
 * @param startDate - The start date in YYYY-MM-DD format
 * @param endDate - The end date in YYYY-MM-DD format
 * @param onStartDateChange - Function to call when the start date changes
 * @param onEndDateChange - Function to call when the end date changes
 * @param title - Title text to display above the date range
 * @param description - Optional description text to display below the date range
 * @param startLabel - Label for the start date input (default: "Data Inizio")
 * @param endLabel - Label for the end date input (default: "Data Fine")
 */
const DateRangeSelector: React.FC<DateRangeSelectorProps> = ({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  title,
  description,
  startLabel = "Data Inizio",
  endLabel = "Data Fine"
}) => {
  return (
    <div className="mt-4">
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {title}
      </label>
      <div className="grid grid-cols-2 gap-2">
        <div>
          <label className="block text-xs text-gray-500 mb-1">{startLabel}</label>
          <input
            type="date"
            value={startDate}
            onChange={(e) => onStartDateChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>
        <div>
          <label className="block text-xs text-gray-500 mb-1">{endLabel}</label>
          <input
            type="date"
            value={endDate}
            onChange={(e) => onEndDateChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>
      </div>
      {description && (
        <p className="text-xs text-gray-500 mt-1">{description}</p>
      )}
    </div>
  );
};

export default DateRangeSelector;

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, Users } from 'lucide-react';
import { useGroups } from '@/hooks/group/useGroups';
import GroupCard from '@/components/groups/GroupCard';
import CreateGroupDialog from '@/components/groups/CreateGroupDialog';
import { InviteGroupMembersDialog } from '@/components/groups/InviteGroupMembersDialog';
import { useNavigate } from 'react-router-dom';

export function GroupsList() {
  const { data: groups, isLoading, error } = useGroups();
  const [selectedGroupForInvite, setSelectedGroupForInvite] = useState<string | null>(null);
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="w-12 h-12 bg-muted rounded-full" />
                <div className="h-4 bg-muted rounded w-3/4" />
                <div className="h-3 bg-muted rounded w-1/2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">Errore nel caricamento dei gruppi</p>
        </CardContent>
      </Card>
    );
  }

  if (!groups || groups.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <div className="space-y-4">
            <Users className="h-12 w-12 text-muted-foreground mx-auto" />
            <div>
              <p className="text-lg font-semibold text-foreground">Nessun gruppo</p>
              <p className="text-muted-foreground">
                Crea il tuo primo gruppo per iniziare a condividere offerte con amici e famiglia!
              </p>
            </div>
            <CreateGroupDialog 
              trigger={
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Crea gruppo
                </Button>
              }
            />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-foreground">I miei gruppi</h2>
          <p className="text-sm text-muted-foreground">
            {groups.length} {groups.length === 1 ? 'gruppo' : 'gruppi'}
          </p>
        </div>
        <CreateGroupDialog 
          trigger={
            <Button variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Nuovo gruppo
            </Button>
          }
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {groups.map((group) => (
          <GroupCard
            key={group.id}
            group={group}
            onClick={(group) => navigate(`/groups/${group.id}`)}
            onShare={(group) => setSelectedGroupForInvite(group.id)}
          />
        ))}
      </div>

      

      {selectedGroupForInvite && (
        <InviteGroupMembersDialog
          groupId={selectedGroupForInvite}
          groupName={groups?.find(g => g.id === selectedGroupForInvite)?.name || 'Gruppo'}
          open={!!selectedGroupForInvite}
          onOpenChange={(open) => !open && setSelectedGroupForInvite(null)}
        />
      )}
    </div>
  );
}
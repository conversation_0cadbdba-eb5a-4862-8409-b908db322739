import { useEffect, useState } from "react";
import { Clock } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import type { Database } from "@/integrations/supabase/types";
import { useAuth } from "@/hooks/auth/useAuth";
import { settings } from "@/config/settings";
import DealCard from '@/components/deals/core/DealCard';
import SwipeHintIcon from '@/components/ui/SwipeHintIcon';
import { useInitialBounceAnimation } from '@/hooks/useInitialBounceAnimation';
import { cn } from '@/lib/utils';

type DealWithBusiness = Database['public']['Tables']['deals']['Row'] & {
  businesses: {
    name: string;
    address: string;
  } | null;
};

const RecentlyViewedDeals = () => {
  const navigate = useNavigate();
  const { userDetails } = useAuth();
  const [recentDeals, setRecentDeals] = useState<DealWithBusiness[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const { animationClasses, showSwipeIcon } = useInitialBounceAnimation({
    delay: 2000,
    duration: 800,
    enabled: recentDeals.length > 1 && !isLoading
  });

  useEffect(() => {
    const fetchRecentDeals = async () => {
      if (!userDetails?.recently_viewed_deals) {
        setIsLoading(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('deals')
          .select(`
            *,
            businesses (
              name,
              address
            )
          `)
          .in('id', userDetails.recently_viewed_deals as string[]);

        if (error) {
          toast.error("Errore nel caricamento delle offerte recenti");
          return;
        }

        const orderedDeals = (userDetails.recently_viewed_deals as string[])
          .map(id => data?.find(deal => deal.id === id))
          .filter((deal): deal is DealWithBusiness => deal !== undefined)
          .slice(0, settings.recentlyViewedDeals.maxItems);

        setRecentDeals(orderedDeals);
      } catch (error) {
        toast.error("Si è verificato un errore");
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecentDeals();
  }, [userDetails?.recently_viewed_deals]);

  if (isLoading) {
    return (
      <section className="mt-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">Visti di Recente</h2>
        </div>
        <div className="flex space-x-4 overflow-x-auto pb-4">
          {[1, 2].map((item) => (
            <div key={item} className="flex-shrink-0 w-48 h-64 bg-gray-100 rounded-xl animate-pulse" />
          ))}
        </div>
      </section>
    );
  }

  if (recentDeals.length === 0) {
    return (
      <section className="mt-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">Visti di Recente</h2>
          <Clock className="h-5 w-5 text-gray-400" />
        </div>
        <div className="bg-white rounded-xl p-6 text-center">
          <p className="text-gray-600">Nessuna offerta visualizzata di recente</p>
        </div>
      </section>
    );
  }

  return (
    <section className="mt-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-800">Visti di Recente</h2>
        <Clock className="h-5 w-5 text-gray-400" />
      </div>
      <div className="relative">
        <div className={cn("flex space-x-4 overflow-x-auto pb-4 hide-scrollbar", animationClasses)}>
          {recentDeals.map((deal) => (
            <div key={deal.id} className="flex-shrink-0 w-72">
              <DealCard
                deal={deal}
                variant="compact"
                onClick={() => navigate(`/deal/${deal.id}`)}
                showVisitBusiness={true}
              />
            </div>
          ))}
        </div>
        
        <SwipeHintIcon show={showSwipeIcon} />
      </div>
    </section>
  );
};

export default RecentlyViewedDeals;

/**
 * Cache Management Utility for PWA Updates
 * Handles cache clearing, versioning, and cleanup
 */

export interface CacheInfo {
  name: string;
  size: number;
  created: number;
  lastAccessed: number;
}

export class CacheManager {
  private static instance: CacheManager;
  private readonly CACHE_PREFIX = 'catchup-app-';
  private readonly CACHE_VERSION_KEY = 'cache-version';
  private readonly CACHE_CLEANUP_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours

  private constructor() {}

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * Clear all app caches
   */
  async clearAllCaches(): Promise<void> {
    try {
      if (!('caches' in window)) {
        console.warn('Cache API not supported');
        return;
      }

      const cacheNames = await caches.keys();
      const deletePromises = cacheNames.map(cacheName => {
        console.log(`Deleting cache: ${cacheName}`);
        return caches.delete(cacheName);
      });

      await Promise.all(deletePromises);
      console.log('All caches cleared successfully');
      
      // Clear localStorage cache-related data
      this.clearCacheMetadata();
    } catch (error) {
      console.error('Error clearing caches:', error);
      throw error;
    }
  }

  /**
   * Clear only old version caches
   */
  async clearOldCaches(currentVersion?: string): Promise<void> {
    try {
      if (!('caches' in window)) return;

      const cacheNames = await caches.keys();
      const currentVersionKey = currentVersion || await this.getCurrentVersion();
      
      const deletePromises = cacheNames
        .filter(cacheName => {
          // Keep current version and core caches
          if (cacheName.includes(currentVersionKey)) return false;
          if (cacheName.includes('supabase-api-cache')) return false;
          if (cacheName.includes('google-maps-cache')) return false;
          return true;
        })
        .map(cacheName => {
          console.log(`Deleting old cache: ${cacheName}`);
          return caches.delete(cacheName);
        });

      await Promise.all(deletePromises);
      console.log('Old caches cleared successfully');
    } catch (error) {
      console.error('Error clearing old caches:', error);
    }
  }

  /**
   * Get information about all caches
   */
  async getCacheInfo(): Promise<CacheInfo[]> {
    try {
      if (!('caches' in window)) return [];

      const cacheNames = await caches.keys();
      const cacheInfoPromises = cacheNames.map(async (name) => {
        const cache = await caches.open(name);
        const keys = await cache.keys();
        
        return {
          name,
          size: keys.length,
          created: this.getCacheCreationTime(name),
          lastAccessed: this.getCacheLastAccessed(name)
        };
      });

      return await Promise.all(cacheInfoPromises);
    } catch (error) {
      console.error('Error getting cache info:', error);
      return [];
    }
  }

  /**
   * Clean up expired caches
   */
  async cleanupExpiredCaches(): Promise<void> {
    try {
      const cacheInfo = await this.getCacheInfo();
      const now = Date.now();
      
      const expiredCaches = cacheInfo.filter(cache => {
        const age = now - cache.lastAccessed;
        return age > this.CACHE_CLEANUP_INTERVAL;
      });

      if (expiredCaches.length > 0) {
        console.log(`Cleaning up ${expiredCaches.length} expired caches`);
        
        const deletePromises = expiredCaches.map(cache => 
          caches.delete(cache.name)
        );
        
        await Promise.all(deletePromises);
        console.log('Expired caches cleaned up successfully');
      }
    } catch (error) {
      console.error('Error cleaning up expired caches:', error);
    }
  }

  /**
   * Force refresh of specific cache
   */
  async refreshCache(cacheName: string): Promise<void> {
    try {
      if (!('caches' in window)) return;

      // Delete the specific cache
      await caches.delete(cacheName);
      console.log(`Cache ${cacheName} refreshed`);
      
      // Update metadata
      this.updateCacheMetadata(cacheName);
    } catch (error) {
      console.error(`Error refreshing cache ${cacheName}:`, error);
    }
  }

  /**
   * Get current app version
   */
  async getCurrentVersion(): Promise<string> {
    try {
      const version = localStorage.getItem(this.CACHE_VERSION_KEY);
      return version || '1.0.0';
    } catch (error) {
      console.error('Error getting current version:', error);
      return '1.0.0';
    }
  }

  /**
   * Set current app version
   */
  async setCurrentVersion(version: string): Promise<void> {
    try {
      localStorage.setItem(this.CACHE_VERSION_KEY, version);
      console.log(`App version set to: ${version}`);
    } catch (error) {
      console.error('Error setting current version:', error);
    }
  }

  /**
   * Check if cache update is needed
   */
  async isCacheUpdateNeeded(): Promise<boolean> {
    try {
      const cacheInfo = await this.getCacheInfo();
      const now = Date.now();
      
      // Check if any cache is older than 1 hour
      const staleThreshold = 60 * 60 * 1000; // 1 hour
      
      return cacheInfo.some(cache => {
        const age = now - cache.lastAccessed;
        return age > staleThreshold;
      });
    } catch (error) {
      console.error('Error checking cache update status:', error);
      return false;
    }
  }

  /**
   * Preload critical resources
   */
  async preloadCriticalResources(): Promise<void> {
    try {
      if (!('caches' in window)) return;

      const criticalResources = [
        '/',
        '/static/js/main.js',
        '/static/css/main.css',
        '/icon-192x192.png',
        '/icon-512x512.png'
      ];

      const cache = await caches.open(`${this.CACHE_PREFIX}critical-v1`);
      
      const preloadPromises = criticalResources.map(async (resource) => {
        try {
          const response = await fetch(resource);
          if (response.ok) {
            await cache.put(resource, response);
          }
        } catch (error) {
          console.warn(`Failed to preload ${resource}:`, error);
        }
      });

      await Promise.all(preloadPromises);
      console.log('Critical resources preloaded successfully');
    } catch (error) {
      console.error('Error preloading critical resources:', error);
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{
    totalCaches: number;
    totalSize: number;
    oldestCache: string;
    newestCache: string;
  }> {
    try {
      const cacheInfo = await this.getCacheInfo();
      
      if (cacheInfo.length === 0) {
        return {
          totalCaches: 0,
          totalSize: 0,
          oldestCache: '',
          newestCache: ''
        };
      }

      const totalSize = cacheInfo.reduce((sum, cache) => sum + cache.size, 0);
      const sortedByAge = cacheInfo.sort((a, b) => a.created - b.created);
      
      return {
        totalCaches: cacheInfo.length,
        totalSize,
        oldestCache: sortedByAge[0]?.name || '',
        newestCache: sortedByAge[sortedByAge.length - 1]?.name || ''
      };
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return {
        totalCaches: 0,
        totalSize: 0,
        oldestCache: '',
        newestCache: ''
      };
    }
  }

  private getCacheCreationTime(cacheName: string): number {
    try {
      const stored = localStorage.getItem(`${cacheName}-created`);
      return stored ? parseInt(stored) : Date.now();
    } catch {
      return Date.now();
    }
  }

  private getCacheLastAccessed(cacheName: string): number {
    try {
      const stored = localStorage.getItem(`${cacheName}-accessed`);
      return stored ? parseInt(stored) : Date.now();
    } catch {
      return Date.now();
    }
  }

  private updateCacheMetadata(cacheName: string): void {
    try {
      const now = Date.now().toString();
      localStorage.setItem(`${cacheName}-accessed`, now);
      
      if (!localStorage.getItem(`${cacheName}-created`)) {
        localStorage.setItem(`${cacheName}-created`, now);
      }
    } catch (error) {
      console.error('Error updating cache metadata:', error);
    }
  }

  private clearCacheMetadata(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.includes('-created') || key.includes('-accessed')) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Error clearing cache metadata:', error);
    }
  }
}

// Export singleton instance
export const cacheManager = CacheManager.getInstance(); 
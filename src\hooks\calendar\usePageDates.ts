import { useState, useCallback, useRef, useEffect } from "react";
import { addDays, isBefore, startOfToday } from "date-fns";
import { CarouselApi } from "@/components/ui/carousel";

export function usePagedDates({ pageSize = 7, maxPages = 3 }) {
  const today = startOfToday();
  const totalDays = pageSize * maxPages;
  const [currentPage, setCurrentPage] = useState(0); // pagina 0 = oggi
  const currentPageRef = useRef(0);

  // Update ref when state changes
  useEffect(() => {
    currentPageRef.current = currentPage;
    
  }, [currentPage]);

  // Setup iniziale delle date (prima pagina parte da oggi)
  const [visibleDates, setVisibleDates] = useState(() => {
    const startDate = today;

    const dates = [];
    for (let i = 0; i < totalDays; i++) {
      dates.push(addDays(startDate, i));
    }

    return dates;
  });


  const generateDates = (startDate: Date): Date[] => {
    const dates = [];
    for (let i = 0; i < totalDays; i++) {
      dates.push(addDays(startDate, i));
    }

    return dates;
  }


  // Funzione per gestire il cambiamento di slide
  const handleSlideChange = useCallback(
    (selectedSnap: number, carouselAPI: CarouselApi) => {
      const prevSnap = carouselAPI.previousScrollSnap();
      const forward = selectedSnap > prevSnap;
      const forwardFromPage0 = prevSnap === selectedSnap && !(selectedSnap === prevSnap);

      console.log(
        "🐦 handleSlideChange",
        "direction:",
        forward ? "forward" : "backward",
        " selectedSnap:",
        selectedSnap,
        "and previousSnap:",
        prevSnap,
        " and currentPage:",
        currentPageRef.current,
          " and new page", forward ? currentPageRef.current + 1 : currentPageRef.current - 1,
 
      
        " and direction:",
        forward ? "forward" : "backward",
        "from Page0Forwad",
        forwardFromPage0
      );
      //console.log("visibleDates", visibleDates);

      if (prevSnap === selectedSnap) return;

      // const delta = selectedSnap - 1; // il carosello mostra sempre la pagina centrale: index 1
      // if (delta === 0) {
      //   return; // delta 0 from page0 to page1
      // }

      // const newPage = Math.max(0, currentPageRef.current + delta);
      // console.log("newPage", newPage);
      // setCurrentPage(newPage);
      // currentPageRef.current = newPage; // Update ref immediately

      // scroll forward
      if (forward) {
        setCurrentPage((prev) => currentPageRef.current + 1);
        console.log(  "scroll forward");
        // const lastDate = visibleDates[visibleDates.length - 1];
        // const nextDates = Array.from({ length: pageSize }, (_, i) =>
        //   addDays(lastDate, i + 1)
        // );

        // setVisibleDates((prev) => [
        //   ...prev.slice(pageSize), // rimuovi la prima pagina
        //   ...nextDates, // aggiungi la nuova alla fine
        // ]);
        // console.log(newPage);
        // setCurrentPage(newPage);
      }

      // scroll backward
      if (!forward) {
           setCurrentPage((prev) => currentPageRef.current - 1);
        console.log(  "scroll backward");
        const currentStart = visibleDates[pageSize]; // pagina centrale visibile
        const prevStart = addDays(currentStart, -pageSize);

        if (isBefore(prevStart, startOfToday())) return;

        // const prevDates = Array.from({ length: pageSize }, (_, i) =>
        //   addDays(prevStart, i)
        // );

        // setVisibleDates((prev) => [
        //   ...prevDates,
        //   ...prev.slice(0, totalDays - pageSize), // rimuovi ultima pagina
        // ]);
        // console.log(newPage);
        // setCurrentPage(newPage);
      }

      //riportati alla pagina centrale (index 1)
      // setTimeout(() => {
      //   if(currentPage === 0) return;
      //   carouselAPI.scrollTo(1);
      // }, 0);
    },
    []
  );

  return {
    visibleDates,
    handleSlideChange,
    currentPage, // Assicurati che questa variabile sia esportata
  };
}

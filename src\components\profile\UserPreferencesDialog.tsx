import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Settings } from "lucide-react";

interface UserPreferencesDialogProps {
  isOpen: boolean;
  onConfirm: () => void;
}

export const UserPreferencesDialog = ({ isOpen, onConfirm }: UserPreferencesDialogProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-brand-primary/10 rounded-full">
              <Settings className="h-5 w-5 text-brand-primary" />
            </div>
            <DialogTitle>Configurazione Profilo</DialogTitle>
          </div>
          <DialogDescription className="text-left">
            Per offrirti un'esperienza personalizzata, configuriamo le tue preferenze. 
            Questo ci aiuterà a mostrarti le offerte più adatte ai tuoi gusti e interessi.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button 
            onClick={onConfirm}
            className="w-full bg-brand-primary hover:bg-brand-primary/90"
          >
            Configura Preferenze
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
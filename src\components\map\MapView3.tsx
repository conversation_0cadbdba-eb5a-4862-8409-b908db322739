import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  AdvancedMarker,
  APIProvider,
  Map,
  MapCameraChangedEvent,
  useMap,
} from "@vis.gl/react-google-maps";

import { supabase } from "@/integrations/supabase/client";
import { MapPin } from "lucide-react";

import { MapViewProps, DEFAULT_ZOOM, Business } from "./types";

import "./custom-advanced-marker";
import { BusinessMarker } from "./BusinessMarker";


export interface MapView3Props {
  businesses: Business[];
  onBusinessClick?: (business: Business) => void;
  onDealClick?: (dealId: string) => void;
  locationEnabled?: boolean;
}
/**
 * MapView3 component displaying businesses on a map with navigation features
 */
export const MapView3 = ({
  businesses,
  onBusinessClick,
  onDealClick,
  locationEnabled = true,
}: MapView3Props) => {
  const [isGoogleLoaded, setIsGoogleLoaded] = useState(false);
  const [center, setCenter] = useState<{ lat: number; lng: number }>({
    lat: 45.4671,
    lng: 9.1526,
  });
  const [apiKey, setApiKey] = useState<string>("");

  useEffect(() => {
    const getGoogleMapsKey = async () => {
      try {
        const {
          data: { GOOGLE_MAPS_API_KEY },
          error,
        } = await supabase.functions.invoke("get-google-maps-key");
        if (error) {
          console.error("Errore nel recupero della chiave API:", error);
          return;
        }
        setApiKey(GOOGLE_MAPS_API_KEY);
      } catch (error) {
        console.error("Errore:", error);
      }
    };

    getGoogleMapsKey();
  }, []);

  if (!apiKey) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center h-[calc(100vh-120px)]">
          <div className="animate-spin">
            <MapPin className="h-8 w-8 text-brand-primary" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      <div className="advanced-marker-example">
        <APIProvider apiKey={apiKey} onLoad={() => setIsGoogleLoaded(true)}>
          <Map
            mapId="DEMO_MAP_ID"
            defaultZoom={DEFAULT_ZOOM}
            defaultCenter={center}
            gestureHandling="greedy"
            onCameraChanged={(ev: MapCameraChangedEvent) =>
              console.log(
                "camera changed:",
                ev.detail.center,
                "zoom:",
                ev.detail.zoom
              )
            }
            disableDefaultUI
          >
            <BusinessMarker
              businesses={businesses}
              onBusinessClick={onBusinessClick}
            />
          </Map>
        </APIProvider>
      </div>
    </div>
  );
};

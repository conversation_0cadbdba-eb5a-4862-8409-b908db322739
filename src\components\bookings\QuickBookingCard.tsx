import { formatCurrency } from '@/lib/format-currency';
import { motion } from 'framer-motion';
import { Clock, Check, AlertCircle, Calendar } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import type { BookingWithDetails } from '@/types/booking';

export interface QuickBookingCardProps {
  id: string;
  booking_date: string;
  booking_time: string;
  status: string;
  deal_title?: string;
  business_name?: string;
  deal_image?: string;
  discounted_price?: number;
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'confirmed':
      return <Check className="h-3 w-3" />;
    case 'pending':
      return <Clock className="h-3 w-3" />;
    case 'cancelled':
      return <AlertCircle className="h-3 w-3" />;
    default:
      return <Calendar className="h-3 w-3" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'confirmed':
      return 'bg-green-500';
    case 'pending':
      return 'bg-yellow-500';
    case 'cancelled':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
};

const formatBookingDate = (dateString: string) => {
  const date = new Date(dateString);
  const today = new Date();
  
  if (date.toDateString() === today.toDateString()) {
    return "Oggi";
  }
  
  return date.toLocaleDateString('it-IT', {
    day: 'numeric',
    month: 'short'
  });
};

export const QuickBookingCard: React.FC<QuickBookingCardProps> = ({
  id,
  booking_date,
  booking_time,
  status,
  deal_title,
  business_name,
  deal_image,
  discounted_price,
}) => {
  const navigate = useNavigate();

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    return `${hours}:${minutes}`;
  };

  return (
    <motion.div
      className="flex-shrink-0 w-72 cursor-pointer"
      whileHover={{ scale: 1.02 }}
      onClick={() => navigate(`/prenotazione/${id}`)}
    >
      <div className="bg-white rounded-xl shadow-md overflow-hidden h-full">
        <div className="relative h-36">
          <img
            src={deal_image || "https://via.placeholder.com/300x200"}
            alt={deal_title || "Prenotazione"}
            className="w-full h-full object-cover"
          />
          <div className={`absolute top-2 right-2 ${getStatusColor(status)} text-white px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1`}>
            {getStatusIcon(status)}
            {status === 'confirmed' ? 'Confermata' : 
             status === 'pending' ? 'In attesa' : 
             status === 'cancelled' ? 'Annullata' : status}
          </div>
        </div>
        <div className="p-4">
          <div className="text-xs text-gray-500 mb-1">Prenotazione</div>
          <h3 className="font-semibold text-gray-800 mb-1 line-clamp-2">
            {deal_title || "Prenotazione"}
          </h3>
          <p className="text-sm text-gray-600 mb-2 line-clamp-1">
            {business_name || "Attività"}
          </p>
          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-600">
              <Clock className="h-4 w-4 mr-1" />
              <span>
                {formatBookingDate(booking_date)}, {formatTime(booking_time)}
              </span>
            </div>
            {discounted_price && (
              <span className="text-brand-primary font-bold">
                {formatCurrency(discounted_price)}
              </span>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}; 
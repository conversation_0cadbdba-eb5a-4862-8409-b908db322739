import { useState, useEffect } from 'react';

interface PushNotificationState {
  permission: NotificationPermission;
  isSupported: boolean;
  subscription: PushSubscription | null;
  requestPermission: () => Promise<void>;
  subscribe: () => Promise<void>;
  unsubscribe: () => Promise<void>;
  sendTestNotification: () => void;
}

export const usePushNotifications = (): PushNotificationState => {
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [subscription, setSubscription] = useState<PushSubscription | null>(null);
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    // Check if notifications are supported
    if ('Notification' in window && 'serviceWorker' in navigator && 'PushManager' in window) {
      setIsSupported(true);
      setPermission(Notification.permission);
      
      // Get existing subscription
      getSubscription();
    }
  }, []);

  const getSubscription = async () => {
    try {
      const registration = await navigator.serviceWorker.ready;
      const sub = await registration.pushManager.getSubscription();
      setSubscription(sub);
    } catch (error) {
      console.error('Error getting push subscription:', error);
    }
  };

  const requestPermission = async () => {
    if (!isSupported) return;
    
    try {
      const result = await Notification.requestPermission();
      setPermission(result);
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    }
  };

  const subscribe = async () => {
    if (!isSupported || permission !== 'granted') return;

    try {
      // For now, just create a mock subscription for demo purposes
      // In production, you would use proper VAPID keys and server setup
      const mockSubscription = { 
        endpoint: 'demo-endpoint-' + Date.now(),
        keys: {
          p256dh: 'demo-key',
          auth: 'demo-auth'
        }
      } as any;
      
      setSubscription(mockSubscription);
      console.log('Mock push subscription created for demo:', mockSubscription);
      
      // TODO: In production, implement proper VAPID key subscription:
      // const registration = await navigator.serviceWorker.ready;
      // const sub = await registration.pushManager.subscribe({
      //   userVisibleOnly: true,
      //   applicationServerKey: urlBase64ToUint8Array(vapidPublicKey)
      // });
      
    } catch (error) {
      console.error('Error subscribing to push notifications:', error);
    }
  };

  const unsubscribe = async () => {
    if (!subscription) return;

    try {
      await subscription.unsubscribe();
      setSubscription(null);
      console.log('Push subscription cancelled');
    } catch (error) {
      console.error('Error unsubscribing from push notifications:', error);
    }
  };

  const sendTestNotification = () => {
    if (permission === 'granted') {
      new Notification('CatchUp Test', {
        body: 'Push notifications are working!',
        icon: '/icon-192x192.png',
        badge: '/icon-192x192.png',
        tag: 'test-notification'
      });
    }
  };

  return {
    permission,
    isSupported,
    subscription,
    requestPermission,
    subscribe,
    unsubscribe,
    sendTestNotification,
  };
};

// Helper function to convert VAPID key
function urlBase64ToUint8Array(base64String: string) {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
} 
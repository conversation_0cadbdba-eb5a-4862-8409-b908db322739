
import type { Booking } from "@/types/booking";
import BookingsList from "./BookingsList";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CalendarClock, History, XCircle } from "lucide-react";

/**
 * BookingsTabs component displays upcoming, past, and cancelled bookings in a tab interface
 * using shadcn/ui Tabs component for a better user experience.
 * 
 * @param activeTab - The currently active tab ('upcoming', 'past', or 'cancelled')
 * @param onTabChange - Function to call when tab is changed
 * @param upcomingBookings - Array of upcoming bookings to display
 * @param pastBookings - Array of past bookings to display
 * @param cancelledBookings - Array of cancelled bookings to display
 */
interface BookingsTabsProps {
  activeTab: 'upcoming' | 'past' | 'cancelled';
  onTabChange: (value: string) => void;
  upcomingBookings: Booking[];
  pastBookings: Booking[];
  cancelledBookings: Booking[];
  searchQuery?: string;
  onShareClick?: (booking: Booking) => void;
}

const BookingsTabs = ({
  activeTab,
  onTabChange,
  upcomingBookings,
  pastBookings,
  cancelledBookings,
  searchQuery = "",
  onShareClick
}: BookingsTabsProps) => {
  
  // Filter bookings based on search query
  const filterBookings = (bookings: Booking[]) => {
    if (!searchQuery.trim()) return bookings;
    return bookings.filter(booking => 
      booking.deals?.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      booking.deals?.businesses?.name?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const filteredUpcoming = filterBookings(upcomingBookings);
  const filteredPast = filterBookings(pastBookings);
  const filteredCancelled = filterBookings(cancelledBookings);
  return (
    <div className="w-full bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50 shadow-lg">
      <Tabs defaultValue={activeTab} onValueChange={onTabChange} className="w-full">
        <TabsList className="w-full grid grid-cols-3 gap-1 p-1 bg-muted/50 rounded-2xl">
          <TabsTrigger 
            value="upcoming" 
            className="flex items-center gap-2 rounded-xl data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all duration-200"
          >
            <CalendarClock className="w-4 h-4" />
            <span className="hidden sm:inline">Prossime</span>
            <span className="inline sm:hidden">({filteredUpcoming.length})</span>
            <span className="hidden sm:inline ml-1 bg-primary-foreground/20 text-xs px-2 py-0.5 rounded-full">
              {filteredUpcoming.length}
            </span>
          </TabsTrigger>
          
          <TabsTrigger 
            value="past" 
            className="flex items-center gap-2 rounded-xl data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all duration-200"
          >
            <History className="w-4 h-4" />
            <span className="hidden sm:inline">Passate</span>
            <span className="inline sm:hidden">({filteredPast.length})</span>
            <span className="hidden sm:inline ml-1 bg-primary-foreground/20 text-xs px-2 py-0.5 rounded-full">
              {filteredPast.length}
            </span>
          </TabsTrigger>
          
          <TabsTrigger 
            value="cancelled" 
            className="flex items-center gap-2 rounded-xl data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all duration-200"
          >
            <XCircle className="w-4 h-4" />
            <span className="hidden sm:inline">Cancellate</span>
            <span className="inline sm:hidden">({filteredCancelled.length})</span>
            <span className="hidden sm:inline ml-1 bg-primary-foreground/20 text-xs px-2 py-0.5 rounded-full">
              {filteredCancelled.length}
            </span>
          </TabsTrigger>
        </TabsList>
        
        <div className="p-6">
          <TabsContent value="upcoming" className="mt-0">
            <BookingsList bookings={filteredUpcoming} onShareClick={onShareClick} />
          </TabsContent>
          
          <TabsContent value="past" className="mt-0">
            <BookingsList bookings={filteredPast} onShareClick={onShareClick} />
          </TabsContent>
          
          <TabsContent value="cancelled" className="mt-0">
            <BookingsList bookings={filteredCancelled} onShareClick={onShareClick} />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default BookingsTabs;


import { Check, Filter } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

export interface FilterOptions {
  withDeals: boolean;
  withBookings: boolean;
}

interface BusinessFiltersProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
}

const BusinessFilters = ({ filters, onFiltersChange }: BusinessFiltersProps) => {
  const handleFilterChange = (key: keyof FilterOptions) => {
    onFiltersChange({
      ...filters,
      [key]: !filters[key],
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.withDeals) count++;
    if (filters.withBookings) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          className="h-9 gap-1 border-dashed"
        >
          <Filter className="h-4 w-4" />
          <span>Filtri</span>
          {activeFiltersCount > 0 && (
            <span className="ml-1 rounded-full bg-brand-primary w-5 h-5 text-xs text-white flex items-center justify-center">
              {activeFiltersCount}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuCheckboxItem
          checked={filters.withDeals}
          onCheckedChange={() => handleFilterChange("withDeals")}
        >
          Con offerte
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          checked={filters.withBookings}
          onCheckedChange={() => handleFilterChange("withBookings")}
        >
          Con prenotazioni
        </DropdownMenuCheckboxItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default BusinessFilters;

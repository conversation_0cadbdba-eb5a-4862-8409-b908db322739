import { motion } from "framer-motion";
import { ChevronRight, Plus, Tag, Calendar, Clock } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import CreateBusinessDialog from "../dashboard/CreateBusinessDialog";
import { Business } from "@/hooks/useBusinesses";

interface BusinessSectionProps {
  userBusinesses: Business[];
  onCreateSuccess: () => void;
}

const BusinessSection = ({
  userBusinesses,
  onCreateSuccess,
}: BusinessSectionProps) => {
  const navigate = useNavigate();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const handleBusinessClick = (businessId: string) => {
    navigate(`/business/${businessId}`);
  };

  return (
    <>
      {userBusinesses.length > 0 ? (
        <div className="mt-6 space-y-4">
          {userBusinesses.map((business) => (
            <motion.div
              key={business.id}
              initial={{
                opacity: 0,
                y: 20,
              }}
              animate={{
                opacity: 1,
                y: 0,
              }}
              whileHover={{
                scale: 1.01,
              }}
              onClick={() => handleBusinessClick(business.id)}
              className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"
            >
              <div className="p-4 py-[8px] px-[8px]">
                <div className="flex items-start">
                  {/* Immagine attività */}
                  <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100">
                    <img
                      src={business.photos?.[0] || "/placeholder.svg"}
                      alt={business.name}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Info attività */}
                  <div className="flex-grow min-w-0 ml-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-bold text-gray-900 text-lg truncate">
                          {business.name}
                        </h3>
                        <p className="text-sm text-gray-600 truncate mt-0.5">
                          {business.address} 
                        </p>
                      </div>
                      <ChevronRight className="h-5 w-5 text-gray-400 flex-shrink-0 ml-4" />
                    </div>
                  </div>
                </div>

                {/* Badges e statistiche */}
                <div className="flex items-center mt-4 border-t pt-2">
                  {/* Offerte */}
                  <div className="flex items-center flex-1 justify-center">
                    <Tag className="w-3.5 h-3.5 text-brand-primary" />
                    <span
                      className={`text-xs font-medium ml-1 ${
                        (business.deal_count || 0) > 0
                          ? "text-brand-primary"
                          : "text-gray-500"
                      }`}
                    >
                      {(business.deal_count || 0) > 0 ? (
                        <>
                          {business.deal_count}{" "}
                          {business.deal_count === 1 ? "offerta" : "offerte"}
                        </>
                      ) : (
                        "Nessuna offerta"
                      )}
                    </span>
                  </div>

                  {/* Prenotazioni totali */}
                  <div className="flex items-center flex-1 justify-center border-l border-r px-px">
                    <Calendar className="w-3.5 h-3.5 text-blue-600" />
                    <span
                      className={`text-xs font-medium ml-1 ${
                        (business.booking_count || 0) > 0
                          ? "text-blue-600"
                          : "text-gray-500"
                      }`}
                    >
                      {business.booking_count || 0}{" "}
                      {(business.booking_count || 0) === 1
                        ? "prenotazione"
                        : "prenotazioni"}
                    </span>
                  </div>

                  {/* Prenotazioni in attesa */}
                  <div className="flex items-center flex-1 justify-center">
                    <Clock className="w-3.5 h-3.5 text-yellow-600" />
                    <span
                      className={`text-xs font-medium ml-1 ${
                        (business.pending_booking_count || 0) > 0
                          ? "text-yellow-600"
                          : "text-gray-500"
                      }`}
                    >
                      {business.pending_booking_count || 0} in attesa
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="mt-8 text-center">
          <p className="text-gray-600">Non hai ancora nessuna attività</p>
          <p className="text-sm text-gray-500 mt-1">
            Crea la tua prima attività cliccando sul pulsante in basso
          </p>
        </div>
      )}

      <motion.button
        initial={{
          scale: 0,
        }}
        animate={{
          scale: 1,
        }}
        whileHover={{
          scale: 1.1,
        }}
        onClick={() => setIsCreateDialogOpen(true)}
        className="fixed left-4 bottom-20 z-40 p-4 rounded-full shadow-lg bg-brand-primary text-white hover:bg-brand-primary/90 transition-colors"
      >
        <Plus className="h-6 w-6" />
      </motion.button>

      <CreateBusinessDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onSuccess={onCreateSuccess}
      />
    </>
  );
};

export default BusinessSection;


import { motion } from "framer-motion";
import { Store } from "lucide-react";

interface BusinessModeButtonProps {
  isBusinessMode: boolean;
  onToggle: () => void;
}

const BusinessModeButton = ({ isBusinessMode, onToggle }: BusinessModeButtonProps) => {
  return (
    <motion.button
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      whileHover={{ scale: 1.1 }}
      onClick={onToggle}
      className={`fixed left-1/2 -translate-x-1/2 bottom-20 z-40 p-4 rounded-full shadow-lg flex items-center gap-2 ${
        isBusinessMode 
          ? "bg-brand-primary text-white" 
          : "bg-white text-brand-primary"
      }`}
    >
      <Store className="h-5 w-5" />
      <span className="text-sm font-medium">
        {isBusinessMode ? "Passa ad Utente" : "Passa a Business"}
      </span>
    </motion.button>
  );
};

export default BusinessModeButton;

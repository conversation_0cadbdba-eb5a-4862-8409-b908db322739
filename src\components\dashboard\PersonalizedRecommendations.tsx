import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { useUserPreferences } from "@/hooks/useUserPreferences";
import { formatCurrency } from "@/lib/format-currency";
import { Sparkles } from "lucide-react";
import { QuickDealCard } from '@/components/deals/QuickDealCard';

const PersonalizedRecommendations = () => {
  const navigate = useNavigate();
  const { recommendedDeals, isLoading } = useUserPreferences();

  if (isLoading) {
    return (
      <section className="mt-6">
        <div className="flex items-center gap-2 mb-4">
          <h2 className="text-xl font-bold text-gray-800">Per Te</h2>
          <Sparkles className="h-5 w-5 text-yellow-500" />
        </div>
        <div className="flex space-x-4 overflow-x-auto pb-4">
          {[1, 2, 3].map((item) => (
            <div key={item} className="flex-shrink-0 w-72 h-64 bg-gray-100 rounded-xl animate-pulse" />
          ))}
        </div>
      </section>
    );
  }

  if (recommendedDeals.length === 0) {
    return null; // Non mostrare nulla se non ci sono consigli
  }

  return (
    <section className="mt-6">
      <div className="flex items-center gap-2 mb-4">
        <h2 className="text-xl font-bold text-gray-800">Per Te</h2>
        <Sparkles className="h-5 w-5 text-yellow-500" />
      </div>
      
      
      <div className="flex space-x-4 overflow-x-auto pb-4 hide-scrollbar">
        {recommendedDeals.map((deal) => (
          <QuickDealCard key={deal.id} {...deal} />
        ))}
      </div>


    </section>
  );
};

export default PersonalizedRecommendations;

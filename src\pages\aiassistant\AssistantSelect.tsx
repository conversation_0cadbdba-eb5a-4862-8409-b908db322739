
import { ArrowLeft, Play, Pause } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/hooks/auth/useAuth";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useTextToSpeechSample } from "@/hooks/useTextToSpeechSample";

interface Assistant {
  id: string;
  name: string;
  description: string;
  image_url: string;
  assistant_id: string;
  voice_id: string | null;
}

const fetchAssistants = async () => {
  const { data, error } = await supabase
    .from('ai_assistant_profile')
    .select('*');

  if (error) {
    throw error;
  }

  return data;
};

const AssistantSelect = () => {
  const navigate = useNavigate();
  const [selectedAssistant, setSelectedAssistant] = useState<string | null>(null);
  const [playingAssistantId, setPlayingAssistantId] = useState<string | null>(null);
  const { user, userDetails } = useAuth();
  const queryClient = useQueryClient();
  const { playVoiceSample, stopPlayback, isPlaying } = useTextToSpeechSample();

  const { data: assistants, isLoading } = useQuery({
    queryKey: ['assistants'],
    queryFn: fetchAssistants,
 //   staleTime: Infinity,
  });

  useEffect(() => {
    const channel = supabase
      .channel('schema-db-changes')
      .on(
        'postgres_changes',
        {
          event: '*', // ascolta tutti gli eventi (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'ai_assistant_profile'
        },
        () => {
          queryClient.invalidateQueries({ queryKey: ['assistants'] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  const updateAssistantMutation = useMutation({
    mutationFn: async (assistantId: string) => {
      const { error } = await supabase
        .from('user_details')
        .update({ selected_assistant_id: assistantId })
        .eq('id', user?.id);

      if (error) throw error;
    },
    onSuccess: () => {
      toast.success("Assistente selezionato con successo");
      queryClient.invalidateQueries({ queryKey: ['userDetails'] });
      navigate('/profile');
    },
    onError: (error) => {
      console.error('Errore nella selezione dell\'assistente:', error);
      toast.error("Errore nella selezione dell'assistente");
    }
  });

  useEffect(() => {
    if (userDetails?.selected_assistant_id) {
      setSelectedAssistant(userDetails.selected_assistant_id);
    }
  }, [userDetails]);

  const handleSaveAssistant = async () => {
    if (!user || !selectedAssistant) {
      toast.error("Errore nel salvare l'assistente selezionato");
      return;
    }

    updateAssistantMutation.mutate(selectedAssistant);
  };

  const handlePlayVoiceSample = (assistant: Assistant) => {
    if (playingAssistantId === assistant.id) {
      stopPlayback();
      setPlayingAssistantId(null);
    } else {
      setPlayingAssistantId(assistant.id);
      playVoiceSample(assistant.voice_id, `Ciao, sono ${assistant.name}. Come posso aiutarti oggi?`);
    }
  };

  return (
    <div className="bg-white min-h-screen">
      <header className="fixed top-0 left-0 right-0 bg-white border-b border-gray-100 z-50">
        <div className="flex items-center justify-between px-4 py-3">
          <button 
            onClick={() => navigate(-1)}
            className="text-gray-700"
          >
            <ArrowLeft className="h-6 w-6" />
          </button>
          <h1 className="text-lg font-semibold">Scegli il tuo Assistente</h1>
          <div className="w-8"></div>
        </div>
      </header>

      <main className="pt-16 pb-24 px-4">
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Seleziona il tuo Assistente Vocale</h2>
          <p className="text-gray-600">Scegli l'assistente AI più adatto alle tue esigenze</p>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
          {isLoading ? (
            <div className="col-span-full text-center py-8 text-gray-500">
              Caricamento assistenti...
            </div>
          ) : assistants?.map((assistant) => (
            <div 
              key={assistant.id}
              className={`bg-white rounded-xl border overflow-hidden shadow-sm transition-shadow cursor-pointer relative ${
                selectedAssistant === assistant.id 
                  ? 'border-brand-primary shadow-md' 
                  : 'border-gray-200 hover:shadow-md'
              }`}
              onClick={() => setSelectedAssistant(assistant.id)}
            >
              <div className="aspect-square bg-gray-100 relative">
                <img 
                  className="w-full h-full object-cover" 
                  src={assistant.image_url || '/placeholder.svg'} 
                  alt={`${assistant.name} assistente`}
                />
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePlayVoiceSample(assistant);
                  }}
                  className="absolute bottom-2 right-2 p-2 bg-white/80 backdrop-blur-sm rounded-full hover:bg-white transition-colors"
                >
                  {playingAssistantId === assistant.id ? (
                    <Pause className="h-4 w-4 text-brand-primary animate-pulse" />
                  ) : (
                    <Play className="h-4 w-4 text-gray-700" />
                  )}
                </button>
              </div>
              <div className="p-3">
                <h3 className="text-sm font-semibold mb-1">{assistant.name}</h3>
                <p className="text-gray-600 text-xs line-clamp-2">
                  {assistant.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </main>

      <footer className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 p-4">
        <Button 
          onClick={handleSaveAssistant}
          disabled={!selectedAssistant || updateAssistantMutation.isPending}
          className="w-full bg-brand-primary text-white py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {updateAssistantMutation.isPending ? "Salvataggio..." : "Salva"}
        </Button>
      </footer>
    </div>
  );
};

export default AssistantSelect;


import { Business } from "@/hooks/useBusinesses";

interface BusinessDetailsProps {
  business: Business;
}

export const BusinessDetails = ({ business }: BusinessDetailsProps) => {
  return (
    <>
      {business.description && (
        <div className="text-gray-600 mb-4">
          <p>{business.description}</p>
        </div>
      )}
      <div className="flex items-center gap-2 text-gray-600">
        <span className="font-medium">Indirizzo:</span>
        <span>{business.address}</span>
      </div>
      {business.phone && (
        <div className="flex items-center gap-2 text-gray-600">
          <span className="font-medium">Telefono:</span>
          <span>{business.phone}</span>
        </div>
      )}
      {business.email && (
        <div className="flex items-center gap-2 text-gray-600">
          <span className="font-medium">Email:</span>
          <span>{business.email.replace('["', '').replace('"]', '')}</span>
        </div>
      )}
      {business.website && (
        <div className="flex items-center gap-2 text-gray-600">
          <span className="font-medium">Sito web:</span>
          <a 
            href={business.website} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-brand-primary hover:underline"
          >
            {business.website}
          </a>
        </div>
      )}
    </>
  );
};

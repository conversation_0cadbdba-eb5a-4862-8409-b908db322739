import React, { useState, useEffect } from 'react';
import { availabilityService } from '../services/availabilityService';
import { AvailabilityStatus } from './AvailabilityStatus';

interface AvailabilitySummaryProps {
  dealId: string;
  onViewDetails?: () => void;
}

/**
 * Component for displaying a summary of availability for a deal
 * 
 * @param dealId - The ID of the deal
 * @param onViewDetails - Callback function when "View available dates" is clicked
 */
export function AvailabilitySummary({ dealId, onViewDetails }: AvailabilitySummaryProps) {
  const [summary, setSummary] = useState<null | {
    totalDays: number;
    availableDays: number;
    lowestAvailability: number;
    highestAvailability: number;
    status: 'available' | 'limited' | 'sold-out';
  }>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    async function fetchSummary() {
      const data = await availabilityService.getAvailabilitySummary(dealId);
      setSummary(data);
      setLoading(false);
    }
    
    fetchSummary();
  }, [dealId]);
  
  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-16 bg-gray-200 rounded"></div>
      </div>
    );
  }
  
  if (!summary) {
    return null;
  }
  
  return (
    <div className={`p-3 rounded-md ${
      summary.status === 'available' ? 'bg-green-50' :
      summary.status === 'limited' ? 'bg-yellow-50' :
      'bg-red-50'
    }`}>
      <div className="flex items-center justify-between mb-2">
        <span className="font-medium">Availability:</span>
        <AvailabilityStatus status={summary.status} />
      </div>
      
      <p className="text-sm">
        Available on {summary.availableDays} of {summary.totalDays} days
      </p>
      
      {summary.availableDays > 0 && (
        <p className="text-sm">
          {summary.lowestAvailability === summary.highestAvailability
            ? `${summary.lowestAvailability} seats available`
            : `${summary.lowestAvailability}-${summary.highestAvailability} seats available`}
        </p>
      )}
      
      {onViewDetails && (
        <button
          className="mt-2 text-sm text-blue-600 hover:text-blue-800 underline"
          onClick={onViewDetails}
        >
          View available dates
        </button>
      )}
    </div>
  );
} 
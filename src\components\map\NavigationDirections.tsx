
import React from 'react';
import { X, Clock, ChevronRight } from "lucide-react";
import { NavigationState } from './types';

interface NavigationDirectionsProps {
  navigationState: NavigationState;
  setNavigationState: (state: NavigationState) => void; // Aggiornato il tipo
  getTransportIcon: (mode: string) => JSX.Element;
  formatDuration: (seconds: number) => string;
  formatDistance: (meters: number) => string;
}

export const NavigationDirections = ({ 
  navigationState, 
  setNavigationState,
  getTransportIcon,
  formatDuration,
  formatDistance
}: NavigationDirectionsProps) => {
  if (!navigationState.isActive || !navigationState.directions) return null;

  return (
    <div className="fixed right-4 top-24 bottom-24 w-80 bg-white rounded-lg shadow-lg z-50 flex flex-col overflow-hidden">
      <div className="p-4 bg-brand-primary text-white">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold">Indicazioni stradali</h3>
          <button
            onClick={() => {
              // Modifichiamo questa parte per creare direttamente un nuovo stato
              // invece di usare una funzione di callback
              setNavigationState({
                ...navigationState,
                isActive: false,
              });
            }}
            className="p-1 hover:bg-white/10 rounded-full transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="flex items-center gap-2 text-sm">
          <div className="flex items-center gap-1.5">
            {getTransportIcon(navigationState.mode)}
            <Clock className="h-4 w-4" />
          </div>
          <span>
            {formatDuration(
              navigationState.directions.routes[0].legs[0].duration.value
            )}
          </span>
          <span className="mx-1">·</span>
          <span>
            {formatDistance(
              navigationState.directions.routes[0].legs[0].distance.value
            )}
          </span>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-4">
          {navigationState.directions.routes[0].legs[0].steps.map(
            (step, index) => (
              <div key={index} className="flex gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center">
                  {index === 0 ? (
                    <div className="w-2 h-2 rounded-full bg-brand-primary" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <div
                    className="text-sm text-gray-700"
                    dangerouslySetInnerHTML={{ __html: step.instructions }}
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    {formatDistance(step.distance.value)} ·{" "}
                    {formatDuration(step.duration.value)}
                  </div>
                </div>
              </div>
            )
          )}
        </div>
      </div>
    </div>
  );
};

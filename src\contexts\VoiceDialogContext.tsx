import React, { createContext, useContext, useState, ReactNode } from 'react';

interface VoiceDialogContextType {
  isVoiceDialogOpen: boolean;
  openVoiceDialog: () => void;
  closeVoiceDialog: () => void;
  businessId?: string;
  setBusiness: (businessId?: string) => void;
}

const VoiceDialogContext = createContext<VoiceDialogContextType | undefined>(undefined);

interface VoiceDialogProviderProps {
  children: ReactNode;
}

export const VoiceDialogProvider: React.FC<VoiceDialogProviderProps> = ({ children }) => {
  const [isVoiceDialogOpen, setIsVoiceDialogOpen] = useState(false);
  const [businessId, setBusinessId] = useState<string | undefined>();

  const openVoiceDialog = () => {
    setIsVoiceDialogOpen(true);
  };

  const closeVoiceDialog = () => {
    setIsVoiceDialogOpen(false);
  };

  const setBusiness = (id?: string) => {
    setBusinessId(id);
  };

  const value: VoiceDialogContextType = {
    isVoiceDialogOpen,
    openVoiceDialog,
    closeVoiceDialog,
    businessId,
    setBusiness,
  };

  return (
    <VoiceDialogContext.Provider value={value}>
      {children}
    </VoiceDialogContext.Provider>
  );
};

export const useVoiceDialog = (): VoiceDialogContextType => {
  const context = useContext(VoiceDialogContext);
  if (context === undefined) {
    throw new Error('useVoiceDialog must be used within a VoiceDialogProvider');
  }
  return context;
};

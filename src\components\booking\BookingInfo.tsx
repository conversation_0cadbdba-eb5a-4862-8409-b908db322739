
import { Calendar, Clock, Building2 } from "lucide-react";
import type { BookingWithDetails } from "@/types/booking";

interface BookingInfoProps {
  booking: BookingWithDetails;
}

const BookingInfo = ({ booking }: BookingInfoProps) => {
  console.log("Booking time:", booking.booking_time);

  return (
    <div className="space-y-1">
      <div className="flex items-center gap-2">
        <Building2 className="h-4 w-4 text-gray-400" />
        <p className="text-sm">{booking.deals?.businesses?.name}</p>
      </div>

      <div className="flex items-center gap-2">
        <Calendar className="h-4 w-4 text-gray-400" />
        <p className="text-sm">{new Date(booking.booking_date).toLocaleDateString('it-IT', {
          weekday: 'long',
          day: 'numeric',
          month: 'long'
        })}</p>
      </div>

      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4 text-gray-400" />
        <p className="text-sm">
          {typeof booking.booking_time === 'string' 
            ? booking.booking_time.split(':').slice(0, 2).join(':')
            : 'Orario non disponibile'}
        </p>
      </div>
    </div>
  );
};

export default BookingInfo;

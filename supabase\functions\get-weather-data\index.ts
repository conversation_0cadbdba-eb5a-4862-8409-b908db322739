import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.50.3'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface WeatherResponse {
  current: {
    temp: number;
    feels_like: number;
    humidity: number;
    pressure: number;
    visibility: number;
    uvi: number;
    wind_speed: number;
    wind_deg: number;
    weather: Array<{
      main: string;
      description: string;
      icon: string;
    }>;
  };
  daily: Array<{
    dt: number;
    temp: {
      min: number;
      max: number;
    };
    weather: Array<{
      main: string;
      description: string;
      icon: string;
    }>;
    pop: number;
    humidity: number;
    wind_speed: number;
  }>;
  hourly: Array<{
    dt: number;
    temp: number;
    weather: Array<{
      main: string;
      description: string;
      icon: string;
    }>;
    pop: number;
  }>;
}

interface LocationResponse {
  name: string;
  country: string;
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { lat, lon } = await req.json();

    if (!lat || !lon) {
      return new Response(
        JSON.stringify({ error: 'Latitude and longitude are required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Get OpenWeatherMap API key from environment
    const apiKey = Deno.env.get('OPENWEATHER_API_KEY');
    if (!apiKey) {
      console.error('OpenWeatherMap API key not found');
      return new Response(
        JSON.stringify({ error: 'Weather service configuration error' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    console.log(`Fetching weather data for coordinates: ${lat}, ${lon}`);
    console.log('API key length:', apiKey.length);
    console.log('API key starts with:', apiKey.substring(0, 8));

    // Fetch current weather data from OpenWeatherMap Current Weather API (free)
    const currentWeatherUrl = `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&units=metric&appid=${apiKey}`;
    
    const currentWeatherResponse = await fetch(currentWeatherUrl);
    
    if (!currentWeatherResponse.ok) {
      console.error('Current Weather API error:', currentWeatherResponse.status, currentWeatherResponse.statusText);
      const errorText = await currentWeatherResponse.text();
      console.error('Error details:', errorText);
      return new Response(
        JSON.stringify({ error: 'Failed to fetch current weather data' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const currentWeatherData = await currentWeatherResponse.json();

    // Fetch 5-day forecast data from OpenWeatherMap 5-day forecast API (free)
    const forecastUrl = `https://api.openweathermap.org/data/2.5/forecast?lat=${lat}&lon=${lon}&units=metric&appid=${apiKey}`;
    
    const forecastResponse = await fetch(forecastUrl);
    
    if (!forecastResponse.ok) {
      console.error('Forecast API error:', forecastResponse.status, forecastResponse.statusText);
      const errorText = await forecastResponse.text();
      console.error('Error details:', errorText);
      return new Response(
        JSON.stringify({ error: 'Failed to fetch forecast data' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const forecastData = await forecastResponse.json();

    // Fetch location name
    const locationUrl = `https://api.openweathermap.org/geo/1.0/reverse?lat=${lat}&lon=${lon}&limit=1&appid=${apiKey}`;
    const locationResponse = await fetch(locationUrl);
    let locationName = 'Unknown';
    let countryName = 'Unknown';

    if (locationResponse.ok) {
      const locationData: LocationResponse[] = await locationResponse.json();
      if (locationData.length > 0) {
        locationName = locationData[0].name;
        countryName = locationData[0].country;
      }
    }

    // Process forecast data - group by day and get daily min/max
    const dailyForecasts = new Map();
    forecastData.list.forEach((item: any) => {
      const date = new Date(item.dt * 1000).toISOString().split('T')[0];
      if (!dailyForecasts.has(date)) {
        dailyForecasts.set(date, {
          date,
          temps: [],
          conditions: [],
          icons: [],
          precipitation: [],
          humidity: [],
          windSpeed: [],
        });
      }
      const day = dailyForecasts.get(date);
      day.temps.push(item.main.temp);
      day.conditions.push(item.weather[0].main);
      day.icons.push(item.weather[0].icon);
      day.precipitation.push((item.pop || 0) * 100);
      day.humidity.push(item.main.humidity);
      day.windSpeed.push(item.wind?.speed || 0);
    });

    // Transform the data to our interface
    const transformedData = {
      current: {
        temp: currentWeatherData.main.temp,
        feelsLike: currentWeatherData.main.feels_like,
        humidity: currentWeatherData.main.humidity,
        pressure: currentWeatherData.main.pressure,
        visibility: (currentWeatherData.visibility || 10000) / 1000, // Convert to km
        uvIndex: 0, // Not available in free API
        windSpeed: currentWeatherData.wind?.speed || 0,
        windDirection: currentWeatherData.wind?.deg || 0,
        condition: currentWeatherData.weather[0].main,
        icon: currentWeatherData.weather[0].icon,
        description: currentWeatherData.weather[0].description,
      },
      location: {
        city: locationName,
        country: countryName,
        lat: parseFloat(lat),
        lon: parseFloat(lon),
      },
      forecast: Array.from(dailyForecasts.values()).slice(0, 5).map((day: any) => ({
        date: day.date,
        tempMin: Math.min(...day.temps),
        tempMax: Math.max(...day.temps),
        condition: day.conditions[0], // Take first condition
        icon: day.icons[0], // Take first icon
        description: day.conditions[0],
        precipitation: Math.max(...day.precipitation), // Max precipitation chance
        humidity: Math.round(day.humidity.reduce((a: number, b: number) => a + b, 0) / day.humidity.length),
        windSpeed: Math.round(day.windSpeed.reduce((a: number, b: number) => a + b, 0) / day.windSpeed.length),
      })),
      hourly: forecastData.list.slice(0, 12).map((hour: any) => ({
        time: new Date(hour.dt * 1000).toISOString(),
        temp: hour.main.temp,
        condition: hour.weather[0].main,
        icon: hour.weather[0].icon,
        precipitation: (hour.pop || 0) * 100, // Convert to percentage
      })),
    };

    console.log('Weather data fetched successfully');

    return new Response(
      JSON.stringify(transformedData),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Error in get-weather-data function:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
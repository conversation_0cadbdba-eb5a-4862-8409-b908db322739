
import { useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { toast } from "sonner";
import { Loader2, X } from "lucide-react";
import { v4 as uuidv4 } from 'uuid';

import { supabase } from "@/integrations/supabase/client";
import { useBusinessPhotos } from "@/hooks/useBusinessPhotos";
import { BusinessFormFields } from "@/components/business/BusinessFormFields";
import { PhotosSection } from "@/components/business/PhotosSection";

interface CreateBusinessDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateBusinessDialog = ({ isOpen, onClose, onSuccess }: CreateBusinessDialogProps) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    address: "",
    formatted_address: "",
    zip_code: "",
    city: "",
    state: "",
    country: "",
    phone: "",
    email: "",
    website: "",
    category_id: "",
    latitude: null as number | null,
    longitude: null as number | null
  });
  const [photos, setPhotos] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { isUploadingPhotos } = useBusinessPhotos();

  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        toast.error("Devi essere autenticato per creare un'attività");
        return;
      }
      const { formatted_address, ...businessData } = formData; // remove formatted_address from formData
      const { error } = await supabase
        .from('businesses')
        .insert({
          ...businessData,
          owner_id: user.id,
          photos
        });

      if (error) {
        console.error('Errore creazione business:', error);
        throw error;
      }

    //  toast.success("Attività creata con successo!");
      onSuccess();
      onClose();
      resetForm();
    } catch (error) {
      console.error('Errore completo:', error);
      toast.error("Errore durante la creazione dell'attività");
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      address: "",
      formatted_address: "",
      zip_code: "",
      city: "",
      state: "",
      country: "",
      phone: "",
      email: "",
      website: "",
      category_id: "",
      latitude: null,
      longitude: null
    });
    setPhotos([]);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/40">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white rounded-2xl w-full max-w-lg p-6 relative overflow-y-auto max-h-[90vh]"
          >
            <button
              onClick={onClose}
              className="absolute top-2 right-2 p-1 rounded-full bg-gray-100 hover:bg-gray-200"
            >
              <X className="h-5 w-5" />
            </button>
            <h2 className="text-lg font-semibold mb-4">Crea una nuova attività</h2>

            <form onSubmit={handleSubmit} className="space-y-4">
              <BusinessFormFields 
                editForm={formData}
                onFormChange={handleFormChange}
              />

              <PhotosSection
                businessId={uuidv4()}
                photos={photos}
                onPhotosChange={setPhotos}
                onSave={() => {}}
              />

              <motion.button
                type="submit"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="w-full bg-gradient-to-r from-brand-secondary to-brand-primary text-white py-3 rounded-xl font-medium mt-6"
                disabled={isSubmitting || isUploadingPhotos}
              >
                {isSubmitting || isUploadingPhotos ? (
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="h-5 w-5 animate-spin" />
                    {isUploadingPhotos ? "Caricamento foto..." : "Creazione in corso..."}
                  </div>
                ) : (
                  "Crea attività"
                )}
              </motion.button>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default CreateBusinessDialog;

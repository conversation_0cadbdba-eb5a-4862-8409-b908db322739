// src/contexts/LocationContext.tsx
import React, { createContext, useContext, useEffect, useState } from 'react';
import { locationService, LocationState } from '@/services/locationService';

// Create context with default value
const LocationContext = createContext<LocationState | null>(null);

// Custom hook to use the location context
export function useLocation() {
  const context = useContext(LocationContext);
  
  if (context === null) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  
  return context;
}

// Provider component
export function LocationProvider({ children }: { children: React.ReactNode }) {
  const [locationState, setLocationState] = useState<LocationState>(
    locationService.getState()
  );
  
  useEffect(() => {
    // Subscribe to location updates
    const unsubscribe = locationService.subscribe((newState) => {
      console.log("LocationContext received update:", newState); // Debug log
      setLocationState(newState);
    });
    
    // Cleanup subscription on unmount
    return unsubscribe;
  }, []);
  
  return (
    <LocationContext.Provider value={locationState}>
      {children}
    </LocationContext.Provider>
  );
}

// Provide access to service methods that components might need
export const refreshLocation = () => locationService.refreshLocation();
export const setFallbackMode = (isActive: boolean) => locationService.setFallbackMode(isActive);
export const setDemoMode = (enabled: boolean) => locationService.setDemoMode(enabled);
export const updateDemoCoordinates = (coordinates: { lat: number; lng: number }) => 
  locationService.updateDemoCoordinates(coordinates);
export const refreshLocationSettings = () => locationService.refreshSettings();

# SearchPanel Component

A comprehensive, reusable search and filtering panel that can be triggered from any header and applied to different pages throughout the application.

## Features

- **Unified Search**: Single search input for deals, businesses, and locations
- **Category Filtering**: Visual category selection with icons
- **Business Filters**: Filter by deals availability, bookings, etc.
- **Location Filter**: Radius-based location filtering
- **Sort Options**: Multiple sorting criteria (relevance, distance, rating, price)
- **Smooth Animations**: Slide-down panel with backdrop overlay
- **Responsive Design**: Mobile-first design with proper touch targets
- **Active Filter Indicator**: Shows count of active filters with clear option
- **Visual Search Badge**: Header search button shows active filter count and highlights when filters are applied
- **Customizable**: Configure which sections to show/hide per page

## Usage

### 1. Basic Setup

Import the required components and hooks:

```tsx
import SearchPanel from "@/components/search/SearchPanel";
import { useSearchPanel } from "@/hooks/useSearchPanel";
import HeaderDashboard from "@/components/toolbars/HeaderDashboard";
```

### 2. Initialize the Search Panel Hook

```tsx
const {
  isOpen: isSearchPanelOpen,
  filters,
  openSearchPanel,
  closeSearchPanel,
  updateFilters,
  resetFilters,
  hasActiveFilters,
  activeFiltersCount,
} = useSearchPanel();
```

### 3. Update Your Header

Add the search trigger with visual filter indicator to your header:

```tsx
<HeaderDashboard 
  showSearchButton={true}
  onSearchClick={openSearchPanel}
  hasActiveFilters={hasActiveFilters}
  activeFiltersCount={activeFiltersCount}
/>
```

### 4. Add the SearchPanel Component

```tsx
<SearchPanel
  isOpen={isSearchPanelOpen}
  onClose={closeSearchPanel}
  filters={filters}
  onFiltersChange={updateFilters}
  placeholder="Cerca offerte e attività"
  showLocationFilter={true}
  showBusinessFilters={true}
  showSortOptions={true}
/>
```

### 5. Apply Filters to Your Data

```tsx
// Filter your data based on the search panel filters
const filteredData = data.filter((item) => {
  const matchesSearch = 
    filters.searchQuery === "" || 
    item.title.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
    item.businessName.toLowerCase().includes(filters.searchQuery.toLowerCase());

  const matchesCategory =
    !filters.selectedCategoryId || 
    item.categoryId === filters.selectedCategoryId;

  return matchesSearch && matchesCategory;
});
```

## SearchPanel Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `isOpen` | `boolean` | - | Controls panel visibility |
| `onClose` | `() => void` | - | Called when panel should close |
| `filters` | `SearchFilters` | - | Current filter state |
| `onFiltersChange` | `(filters: SearchFilters) => void` | - | Called when filters change |
| `placeholder` | `string` | `"Cerca offerte e attività"` | Search input placeholder |
| `showLocationFilter` | `boolean` | `true` | Show/hide location radius filter |
| `showBusinessFilters` | `boolean` | `true` | Show/hide business filters |
| `showSortOptions` | `boolean` | `true` | Show/hide sort options |

## SearchFilters Interface

```tsx
interface SearchFilters {
  searchQuery: string;
  selectedCategoryId: string | null;
  withDeals: boolean;
  withBookings: boolean;
  radius: number;
  sortBy: 'relevance' | 'distance' | 'rating' | 'price';
}
```

## useSearchPanel Hook

The hook provides everything needed to manage the search panel state:

```tsx
const searchPanel = useSearchPanel(initialFilters?);
```

### Return Values

| Property | Type | Description |
|----------|------|-------------|
| `isOpen` | `boolean` | Current open/closed state |
| `filters` | `SearchFilters` | Current filter values |
| `openSearchPanel` | `() => void` | Open the search panel |
| `closeSearchPanel` | `() => void` | Close the search panel |
| `toggleSearchPanel` | `() => void` | Toggle panel open/closed |
| `updateFilters` | `(filters: SearchFilters) => void` | Update filter values |
| `resetFilters` | `() => void` | Reset all filters to defaults |
| `hasActiveFilters` | `boolean` | Whether any filters are active |
| `activeFiltersCount` | `number` | Number of active filters |

## Visual Filter Indicator

The SearchPanel automatically provides visual feedback when filters are active:

### Header Search Button States

**No Active Filters:**
- Gray search icon with subtle hover effect
- No badge or highlighting

**Active Filters:**
- Blue search icon with blue background
- Red badge showing filter count (1-9, or "9+" for more)
- Enhanced hover effect

### Badge Behavior

- **Count Display**: Shows exact number up to 9, then "9+"
- **Auto-Update**: Updates immediately when filters change
- **Smart Detection**: Counts all filter types (search, category, business, location, sort)

### Integration

The visual indicator is automatically handled by the `useSearchPanel` hook:

```tsx
const { hasActiveFilters, activeFiltersCount } = useSearchPanel();

// These values automatically update based on filter state
<HeaderDashboard 
  hasActiveFilters={hasActiveFilters}        // true when any filter is active
  activeFiltersCount={activeFiltersCount}    // number of active filters
/>
```

## Complete Example

See `src/pages/deals/DealsWithSearchPanel.tsx` for a complete implementation example.

## Customization Examples

### Search Only (No Filters)

```tsx
<SearchPanel
  isOpen={isSearchPanelOpen}
  onClose={closeSearchPanel}
  filters={filters}
  onFiltersChange={updateFilters}
  showLocationFilter={false}
  showBusinessFilters={false}
  showSortOptions={false}
/>
```

### Different Placeholder Text

```tsx
<SearchPanel
  // ... other props
  placeholder="Cerca ristoranti e locali"
/>
```

### Custom Initial Filters

```tsx
const { filters, updateFilters } = useSearchPanel({
  radius: 5,
  sortBy: 'distance',
  withDeals: true,
});
```

## Styling

The component uses Tailwind CSS classes and follows the application's design system:

- **Colors**: Uses `brand-primary` for active states
- **Animations**: Smooth slide-down animation with backdrop
- **Responsive**: Mobile-first design with proper touch targets
- **Accessibility**: Proper ARIA labels and keyboard navigation

## Integration with Existing Pages

### HomeUser Page

```tsx
// Add to existing HomeUser component
const { isOpen, filters, openSearchPanel, closeSearchPanel, updateFilters } = useSearchPanel();

// Update HeaderDashboard call
<HeaderDashboard 
  showSearchButton={true}
  onSearchClick={openSearchPanel}
/>

// Add SearchPanel component
<SearchPanel
  isOpen={isOpen}
  onClose={closeSearchPanel}
  filters={filters}
  onFiltersChange={updateFilters}
  showBusinessFilters={isBusinessMode}
/>
```

### Map Pages

For map-based pages, you might want to show a floating search button:

```tsx
{viewMode === "map" && (
  <button
    onClick={openSearchPanel}
    className="absolute top-4 left-4 z-10 bg-white shadow-lg rounded-full px-4 py-2 flex items-center gap-2"
  >
    <Search className="h-5 w-5" />
    <span>Cerca</span>
  </button>
)}
```

## Best Practices

1. **Performance**: Only fetch categories when the panel opens
2. **UX**: Auto-focus the search input when panel opens
3. **State Management**: Use the provided hook for consistent state management
4. **Accessibility**: Ensure keyboard navigation works properly
5. **Mobile**: Test on mobile devices for proper touch interaction
6. **Filtering**: Apply filters client-side for fast interaction, server-side for large datasets

## Troubleshooting

### Panel doesn't open
- Check that `showSearchButton` is true in HeaderDashboard
- Verify `onSearchClick` is properly connected to `openSearchPanel`

### Filters not working
- Ensure `onFiltersChange` is connected to `updateFilters`
- Check that your filtering logic matches the `SearchFilters` interface

### Categories not loading
- Verify Supabase connection and permissions
- Check browser console for error messages

### Animation issues
- Ensure Framer Motion is properly installed
- Check that the component has proper z-index values 
import { useLocation, useNavigate } from "react-router-dom";
import { useEffect } from "react";
import { motion } from "framer-motion";
import { Home, MapPin, Search } from "lucide-react";
import { Button } from "@/components/ui/button";

const NotFound = () => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md bg-white rounded-xl shadow-sm p-8 text-center"
      >
        {/* Illustration */}
        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          className="mx-auto mb-6 w-24 h-24 rounded-full bg-brand-light flex items-center justify-center"
        >
          <Search className="h-12 w-12 text-brand-primary" />
        </motion.div>

        {/* Error code with gradient */}
        <h1 className="text-5xl font-bold mb-2 bg-gradient-to-r from-brand-primary to-brand-secondary bg-clip-text text-transparent">
          404
        </h1>

        {/* Message */}
        <p className="text-xl text-gray-600 mb-6">
          Oops! Pagina non trovata
        </p>

        {/* Description */}
        <p className="text-gray-500 mb-8">
          La pagina che stai cercando non esiste o è stata spostata.
        </p>

        {/* Action buttons */}
        <div className="flex flex-col gap-3">
          <Button
            onClick={() => navigate("/")}
            className="w-full bg-brand-primary hover:bg-brand-primary/90 text-white"
          >
            <Home className="mr-2 h-4 w-4" />
            Torna alla Home
          </Button>

   
        </div>
      </motion.div>
    </div>
  );
};

export default NotFound;

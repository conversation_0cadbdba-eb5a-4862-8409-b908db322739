import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAuth } from "@/hooks/auth/useAuth";

export interface UserPreferencesData {
  id?: string;
  user_id?: string;
  categories: string[];
  price_range: string;
  notification_preferences: {
    special_offers: boolean;
    appointment_reminders: boolean;
    new_businesses: boolean;
    voice_assistant: boolean;
    [key: string]: boolean;
  };
  onboarding_completed: boolean;
  created_at?: string;
  updated_at?: string;
}

// Helper type for Supabase returned data where notification_preferences might be Json
type SupabaseUserPreferences = Omit<UserPreferencesData, 'notification_preferences'> & {
  notification_preferences: any;
};

export const useUserPreferencesManager = () => {
  const { user } = useAuth();
  const [preferences, setPreferences] = useState<UserPreferencesData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!user) return;
    
    const fetchUserPreferences = async () => {
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('user_preferences')
          .select('*')
          .eq('user_id', user.id)
          .single();
        
        if (error) {
          console.error('Error fetching user preferences:', error);
          return;
        }
        
        // Transform the data to match our expected types
        setPreferences({
          ...data,
          notification_preferences: typeof data.notification_preferences === 'string' 
            ? JSON.parse(data.notification_preferences as string)
            : data.notification_preferences as UserPreferencesData['notification_preferences']
        });
      } catch (error) {
        console.error('Error in fetchUserPreferences:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchUserPreferences();
  }, [user]);

  const updatePreferences = async (newPreferences: Partial<UserPreferencesData>) => {
    if (!user) {
      toast.error("Utente non autenticato");
      return false;
    }
    
    try {
      setIsLoading(true);
      
      // If preferences exist (we're updating), include the id
      const upsertData = preferences?.id 
        ? {
            id: preferences.id,
            user_id: user.id, 
            ...newPreferences,
            updated_at: new Date().toISOString()
          }
        : {
            user_id: user.id,
            ...newPreferences,
            updated_at: new Date().toISOString()
          };
      
      const { error } = await supabase
        .from('user_preferences')
        .upsert(upsertData, { 
          onConflict: 'user_id'  // Specify which column to match on
        });
      
      if (error) {
        console.error('Error updating preferences:', error);
        toast.error("Errore nell'aggiornamento delle preferenze");
        return false;
      }
      
      // Refresh preferences
      const { data } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single();
      
      // Transform the data to match our expected types
      const transformedData: UserPreferencesData = {
        ...data,
        notification_preferences: typeof data.notification_preferences === 'string' 
          ? JSON.parse(data.notification_preferences as string)
          : data.notification_preferences as UserPreferencesData['notification_preferences']
      };
      
      setPreferences(transformedData);
      return true;
    } catch (error) {
      console.error('Error in updatePreferences:', error);
      toast.error("Si è verificato un errore");
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const updateCategories = async (categories: string[]) => {
    return updatePreferences({ categories });
  };

  const updatePriceRange = async (priceRange: string) => {
    return updatePreferences({ price_range: priceRange });
  };

  const updateNotificationPreferences = async (notificationPreferences: Partial<Record<string, boolean>>) => {
    // Make sure we have the default values for required fields if they're not provided
    const updatedPreferences: UserPreferencesData['notification_preferences'] = {
      special_offers: preferences?.notification_preferences?.special_offers ?? false,
      appointment_reminders: preferences?.notification_preferences?.appointment_reminders ?? false,
      new_businesses: preferences?.notification_preferences?.new_businesses ?? false,
      voice_assistant: preferences?.notification_preferences?.voice_assistant ?? false,
      ...(preferences?.notification_preferences || {}),
      ...notificationPreferences
    };
    
    return updatePreferences({ notification_preferences: updatedPreferences });
  };

  const completeOnboarding = async () => {
    return updatePreferences({ onboarding_completed: true });
  };

  return {
    preferences,
    isLoading,
    updatePreferences,
    updateCategories,
    updatePriceRange,
    updateNotificationPreferences,
    completeOnboarding
  };
};

export default useUserPreferencesManager;

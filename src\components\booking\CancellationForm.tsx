
import { useState } from "react";

interface CancellationFormProps {
  onCancel: (note: string) => Promise<void>;
  onClose: () => void;
  isCancelling: boolean;
}

const CancellationForm = ({ onCancel, onClose, isCancelling }: CancellationFormProps) => {
  const [cancellationNote, setCancellationNote] = useState("");

  const handleSubmit = () => {
    onCancel(cancellationNote);
  };

  return (
    <div className="space-y-2 p-2 bg-gray-50 rounded">
      <textarea
        placeholder="Inserisci una nota per la cancellazione..."
        value={cancellationNote}
        onChange={(e) => setCancellationNote(e.target.value)}
        className="w-full p-1.5 border rounded text-xs"
        rows={2}
      />
      <div className="flex gap-2">
        <button
          onClick={handleSubmit}
          disabled={isCancelling}
          className="flex-1 bg-red-600 text-white px-2 py-1 rounded text-xs font-medium hover:bg-red-700 transition-colors disabled:opacity-50"
        >
          {isCancelling ? "Cancellazione..." : "Conferma"}
        </button>
        <button
          onClick={onClose}
          className="flex-1 bg-gray-600 text-white px-2 py-1 rounded text-xs font-medium hover:bg-gray-700 transition-colors"
        >
          Annulla
        </button>
      </div>
    </div>
  );
};

export default CancellationForm;

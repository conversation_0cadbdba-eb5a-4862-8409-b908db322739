import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>,   <PERSON>ader2, <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import OpenAIService from "@/services/OpenAIService";

import { useNavigate } from "react-router-dom";
import { useSettingsStore } from "@/stores/useSettingsStore";


const EchoVoiceAI = () => {
  const [isListening, setIsListening] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [statusText, setStatusText] = useState("Ready");
  const [transcript, setTranscript] = useState("");
  const [userTranscript, setUserTranscript] = useState("");
  const [aiResponse, setAiResponse] = useState("");
  const [timer, setTimer] = useState(0); 

  const [isInitialStart, setIsInitialStart] = useState(false);
  const [userStoppedManually, setUserStoppedManually] = useState(false);
  const navigate = useNavigate();

  
  // Get settings from store
  const { settings } = useSettingsStore();

  const { toast } = useToast();

  // Clean up connections when unmounting or navigating away
  useEffect(() => {
    return () => {
      // Always clean up when component unmounts
      stopListening();
    };
  }, []);
  


  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isListening) {
      interval = setInterval(() => {
        setTimer((prev) => prev + 1);
      }, 1000);
    } else {
      setTimer(0);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isListening]);

  // Format timer to MM:SS
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Set up service callbacks
  useEffect(() => {
    // Set up OpenAI service callbacks
    OpenAIService.setTranscriptionCallback((text) => {
      console.log("🤖 AI Transcript received:", text);
      setTranscript(text);
    });

    OpenAIService.setUserTranscriptionCallback((text) => {
      console.log("👤 User Transcript received:", text);
      setUserTranscript(text);
    });

    OpenAIService.setAIResponseCallback((text) => {
      console.log("🤖 AI Response chunk received:", text);
      setAiResponse((prevResponse) => prevResponse + text);
    });

    OpenAIService.setStatusChangeCallback((status) => {
      console.log("📊 Status changed:", status);
      setStatusText(status);
    });

    OpenAIService.setErrorCallback((error) => {
      console.log("❌ OpenAI Error:", error);
      toast({
        title: "API Error",
        description: error || "Unknown error occurred",
        variant: "destructive",
      });
    });

    return () => {
      // Clean up on component unmount
      OpenAIService.closeConnection();
    };
  }, [toast]);

  const initiateConversation = async () => {
    console.log("🚀 initiateConversation called", { userStoppedManually, isInitialStart });
    console.trace("Call stack for initiateConversation:");
    
    // Don't restart if user manually stopped
    if (userStoppedManually) {
      console.log("❌ Blocked restart - user manually stopped");
      return;
    }
    
    try {
      // First set connecting state
      setIsConnecting(true);
      setStatusText("Connecting...");
      setTranscript("");
      setUserTranscript("");
      setAiResponse("");
      setTimer(0);
      
      // Initialize OpenAI connection with model from settings
      await OpenAIService.initializeConnection(settings.aiModel);
      
      // Wait for the data channel to be ready - important for WebRTC
      const isReady = await OpenAIService.waitForDataChannel();
      if (!isReady) {
        throw new Error("Data channel failed to open");
      }
      
      // Now that we're fully connected, start listening - pass true for initial start
      await startListening(true);
    } catch (error) {
      console.error("Error initializing connection:", error);
      toast({
        title: "Connection Error",
        description: "Could not connect to OpenAI service",
        variant: "destructive",
      });
      setStatusText("Error: Could not connect");
      setIsConnecting(false);
    }
  };

  const startListening = async (isThisInitialStart = false) => {
    console.log("🎤 startListening called", { userStoppedManually, isInitialStart: isThisInitialStart });
    console.trace("Call stack for startListening:");
    
    // Don't restart if user manually stopped
    if (userStoppedManually) {
      console.log("❌ Blocked restart - user manually stopped");
      return;
    }
    
    try {
      console.log("🎤 Starting listening...", { isInitialStart: isThisInitialStart });
     
      // Once connection is successful, update states
      setIsConnecting(false);
      setIsListening(true);
      setStatusText("Listening...");

      if (isThisInitialStart && settings.autoStartConversation) { 
        console.log("🚀 Auto-starting conversation due to settings.autoStartConversation (initial start only)");
        // Now that we're connected and listening, initiate conversation
        OpenAIService.initiateConversation();
      }
      
      // Update the state after using the parameter
      setIsInitialStart(isThisInitialStart);
      
    } catch (error) {
      console.error("Error starting listening:", error);
      toast({
        title: "Connection Error",
        description: "Could not start listening",
        variant: "destructive",
      });
      setStatusText("Error: Could not connect");
      setIsConnecting(false);
      setIsListening(false);
      setIsInitialStart(false); // Reset on error
    }
  };

  const stopListening = () => {
    console.log("🛑 Stopping listening...");
    setIsListening(false);
    setIsInitialStart(false); // Ensure it's reset when stopping
    setUserStoppedManually(true); // Mark that user manually stopped
    setTranscript("");
    setUserTranscript("");
    setAiResponse("");
    setStatusText("Processing...");

    // Close the connection
    OpenAIService.closeConnection();

    setStatusText("Ready");
    console.log("✅ Stopped listening");
  };


  // Audio visualizer bars with different heights
  const renderVisualizerBars = () => {
    const bars = [];
    const animationDelays = [
      "animate-sound-wave",
      "animate-sound-wave-delay-1",
      "animate-sound-wave-delay-2",
      "animate-sound-wave-delay-3",
      "animate-sound-wave-delay-4",
      "animate-sound-wave-delay-5",
      "animate-sound-wave-delay-6",
    ];

    for (let i = 0; i < 7; i++) {
      const isActive = isListening;

      bars.push(
        <div
          key={i}
          className={`voice-bar ${
            isActive ? animationDelays[i % animationDelays.length] : ""
          }`}
          style={{ height: isActive ? undefined : "8px" }}
        ></div>
      );
    }

    return bars;
  };

  return (
    <div className="echo-container">
      {/* Back Button */}
      <button
        className="absolute top-6 left-4 z-20 p-2 rounded-full bg-echo-panel text-echo-accent hover:bg-echo-accent/20 transition-colors"
        onClick={() => navigate(-1)}
        aria-label="Go back"
        type="button"
      >
        <ArrowLeft className="w-6 h-6" />
      </button>

      {/* Header */}
      {/* <header className="flex justify-between items-center py-6">
        <div className="text-2xl font-bold text-echo-accent animate-fadeIn">
          EchoAI
        </div>
        <button 
          className="control-button p-3"
          onClick={handleNavigateToSettings}
        >
          <SettingsIcon className="w-5 h-5 text-echo-accent" />
        </button>
      </header> */}

      {/* Main Content */}
      <main className="flex flex-col items-center justify-between flex-1 py-4">
        {/* Status Indicator */}
        <div className="flex items-center gap-2 mb-8 animate-fadeIn">
          <div
            className={`w-3 h-3 rounded-full ${
              isListening
                ? "bg-echo-accent animate-pulse"
                : isConnecting
                ? "bg-yellow-500 animate-pulse"
                : "bg-echo-accent/50"
            }`}
          ></div>
          <span className="text-echo-accent flex items-center gap-2">
            {statusText}
            {isConnecting && <Loader2 className="w-4 h-4 animate-spin" />}
          </span>
        </div>

        {/* Avatar Section */}
        <div className="relative w-56 h-56 mb-10 animate-slideUp">
          <div
            className={`avatar-glow opacity-${isListening ? "70" : "30"}`}
          ></div>
          <div
            className={`absolute inset-0 rounded-full bg-echo-accent/10 ${
              isListening ? "animate-breathe" : ""
            }`}
          ></div>
          <img
            className="w-full h-full rounded-full object-cover"
            src="https://storage.googleapis.com/uxpilot-auth.appspot.com/dff718baa5-335ba1dc1406d22a0a56.png"
            alt="AI assistant avatar"
          />
        </div>

        {/* Sound Visualizer */}
        <div className="w-full h-24 bg-echo-panel rounded-2xl mb-8 p-4 flex items-center justify-center animate-fadeIn">
          <div className="flex items-end gap-2 h-full">
            {renderVisualizerBars()}
          </div>
        </div>

        {/* Conversation Output */}
        { settings.showTranscript && (userTranscript || transcript || aiResponse) && (
          <div className="w-full mb-6 p-4 bg-echo-panel rounded-2xl max-h-40 overflow-y-auto">
            {userTranscript && (
              <div className="mb-2">
                <span className="font-semibold text-echo-accent">You:</span> <span className="text-white">{userTranscript}</span>
              </div>
            )}
            {transcript && (
              <div className="mb-2">
                <span className="font-semibold text-cyan-400">AI Audio:</span> <span className="text-white">{transcript}</span>
              </div>
            )}
            {aiResponse && (
              <div>
                <span className="font-semibold text-echo-accent">EchoAI:</span> <span className="text-white">{aiResponse}</span>
              </div>
            )}
          </div>
        )}

        {/* Debug Info */}
        {process.env.NODE_ENV === 'development' && (
          <div className="w-full mb-4 p-2 bg-gray-800 rounded text-xs text-gray-300">
            Debug: showTranscript={settings.showTranscript.toString()}, userTranscript="{userTranscript}", aiTranscript="{transcript}", aiResponse="{aiResponse}"
          </div>
        )}

        {/* Circular Talk Button with Timer */}
        <div className="w-full mb-8 flex flex-col items-center animate-slideUp">
          <button
            className={`w-32 h-32 rounded-full flex items-center justify-center transition-all duration-300 ${
              isListening
                ? "bg-red-500 hover:bg-red-600 scale-110"
                : isConnecting
                ? "bg-yellow-500 hover:bg-yellow-600 cursor-wait"
                : "bg-echo-accent hover:bg-echo-accent/90"
            }`}
            onClick={
              !isListening && !isConnecting
                ? () => {
                    setUserStoppedManually(false); // Reset flag when user manually starts
                    initiateConversation();
                  }
                : isListening
                ? stopListening
                : undefined
            }
            onTouchStart={
              !isListening && !isConnecting
                ? () => {
                    setUserStoppedManually(false); // Reset flag when user manually starts
                    initiateConversation();
                  }
                : isListening
                ? stopListening
                : undefined
            }
            disabled={isConnecting}
          >
            <div className="flex flex-col items-center">
              {isConnecting ? (
                <Loader2 className="w-8 h-8 mb-2 text-black animate-spin" />
              ) : (
                <Mic
                  className={`w-8 h-8 mb-2 ${
                    isListening ? "text-black animate-pulse" : "text-black"
                  }`}
                />
              )}
              <span className="text-sm text-black font-medium">
                {isConnecting ? "Connecting" : !isListening ? "Start" : "Stop"}
              </span>
            </div>
          </button>
          {/* Timer Display */}
          <div
            className={`mt-4 px-4 py-1 rounded-full bg-black/50 text-white font-mono transition-opacity duration-300 ${
              isListening ? "opacity-100" : "opacity-0"
            }`}
          >
            {formatTime(timer)}
          </div>
        </div>

        {/* Quick Actions */}
        {/* <div className="flex justify-around w-full animate-fadeIn">
          <button className="control-button" aria-label="Adjust volume">
            <Volume2 className="w-5 h-5" />
          </button>
          <button className="control-button" aria-label="View history">
            <Clock className="w-5 h-5" />
          </button>
          <button className="control-button" aria-label="Use keyboard">
            <Keyboard className="w-5 h-5" />
          </button>
        </div> */}
      </main>
    </div>
  );
};

export default EchoVoiceAI;

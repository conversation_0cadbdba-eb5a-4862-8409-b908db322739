import React from "react";
import { MapPin } from "lucide-react";

interface BusinessAddressProps {
  address: string;
  zipCode: string;
  city: string;
  state: string;
  showIcon: boolean;
  iconSize?: number;
  className?: string;
  
 
}

/**
 * BusinessAddress component for displaying a business address with consistent formatting
 * 
 * @param {BusinessAddressProps} props - Component props
 * @returns {JSX.Element} Rendered BusinessAddress component
 */
const BusinessAddress: React.FC<BusinessAddressProps> = ({
  address,
  zipCode,
  city,
  state,
  className = "",
  iconSize = 4,
  showIcon 
}) => {
  return (
    <div className={`flex gap-1 text-sm text-gray-600 ${className}`}>
      {showIcon ? (
        <div className="flex items-center self-center pt-1 mr-1">
          <MapPin className={`h-${iconSize} w-${iconSize}`} />
        </div>
      ) : null}
      <div className="flex flex-col">
        <span>{address}</span>
        <span>{zipCode} {city}, {state}</span>
      </div>
    </div>
  );
};

export default BusinessAddress;
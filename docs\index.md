
# Documentation Index

## Core Workflows
- **[Booking Workflow](BookingWorkflow.md)** - Comprehensive booking system documentation covering entities, components, Supabase integration, and streamlined processes
- **[Notification System Design](NotificationSystemDesign.md)** - Mobile-first notification system with swipe gestures and modern UX patterns

## Component & Feature Documentation
- [Deal Components Options](DealComponentsOptions.md)
- [Deals Page Flow](DealsPageFlow.md)
- [Deals Expiration](DealsExpiration.md)
- [Nearest Deals & Businesses Flow](NearestSearchFlow.md)

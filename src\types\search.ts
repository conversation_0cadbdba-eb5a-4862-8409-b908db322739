
/**
 * Tipi per la ricerca di offerte e disponibilità
 */

import { DealStatus } from "./deals";

export interface DealAvailability {
  id: string;
  name: string;
  description: string;
  original_price: number;
  discounted_price: number;
  discount_percentage: number;
  images: string[] | null;
  time_slots: any;
  status: DealStatus;
  created_at: string;
  updated_at: string;
  auto_confirm: boolean;
  category_id: string;
  start_date: string;
  end_date: string;
  terms_conditions?: string;
  business?: {
    id: string;
    name: string;
    address: string;
    city: string;
    state: string;
    zip_code: string;
    country: string;
    phone: string;
    email: string;
    website: string;
    latitude: number;
    longitude: number;
    distance: number;
    category_id: string;
  };
}

export interface SearchLocation {
  lat: number;
  lng: number;
  radius?: number;
}

export interface TimeSlotQueryParams {
  date: string;
  time?: string;
  location?: SearchLocation;
  cityName?: string;
  category_id?: string;
  cursor?: string;
  limit?: number;
}

export interface SearchMetrics {
  queryTime: number;
  totalResults: number;
  isCached: boolean;
  pageNumber: number;
}


import { ChevronLeft, ChevronRight } from "lucide-react";

interface BusinessImageCarouselProps {
  photos: string[];
  businessName: string;
  currentImageIndex: number;
  onNext: () => void;
  onPrevious: () => void;
  onSelectImage: (index: number) => void;
}

export const BusinessImageCarousel = ({
  photos,
  businessName,
  currentImageIndex,
  onNext,
  onPrevious,
  onSelectImage,
}: BusinessImageCarouselProps) => {
  if (!photos || photos.length === 0) return null;

  return (
    <div className="mb-4 relative rounded-xl overflow-hidden aspect-video">
      <img 
        src={photos[currentImageIndex]} 
        alt={`${businessName} - Foto ${currentImageIndex + 1}`}
        className="w-full h-full object-cover"
      />
      {photos.length > 1 && (
        <>
          <button
            onClick={onPrevious}
            className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>
          <button
            onClick={onNext}
            className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
          >
            <ChevronRight className="h-5 w-5" />
          </button>
          <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1">
            {photos.map((_, index) => (
              <button
                key={index}
                onClick={() => onSelectImage(index)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                }`}
              />
            ))}
          </div>
        </>
      )}
    </div>
  );
};

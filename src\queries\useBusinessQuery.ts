
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useBusinessQuery = (options = {}) => {
  return useQuery({
    queryKey: ['businesses'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('businesses')
        .select('*');
      
      if (error) {
        throw new Error(`Errore nel recupero delle attività: ${error.message}`);
      }
      
      return data || [];
    },
    ...options
  });
};

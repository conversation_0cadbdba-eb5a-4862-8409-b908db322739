import { motion } from "framer-motion";
//import * as Icons from 'lucide-react';
import type { Database } from "@/integrations/supabase/types";
import { Icon } from '@iconify/react';

type Category = Database['public']['Tables']['categories']['Row'];

interface CategoriesProps {
  categories: Category[];
  selectedCategoryIds?: string[];
  onCategorySelect?: (categoryId: string) => void;
}

const Categories = ({ categories, selectedCategoryIds = [], onCategorySelect }: CategoriesProps) => {
  const getCategoryIcon = (iconName: string | null) => {
    if (!iconName) return null;
    // const Icon = (Icons as unknown)[iconName];
    // console.log("Icont", Icon);
    return <Icon icon={iconName} width="32" />  ;
  };

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  const isSelected = (categoryId: string) => selectedCategoryIds.includes(categoryId);

  return (
    <section className="mt-6">
      <div className="flex items-center justify-between px-4 mb-4">
        <h2 className="text-lg font-semibold text-gray-800">Categorie</h2>
        {selectedCategoryIds.length > 0 && (
          <span className="text-sm text-brand-primary font-medium">
            {selectedCategoryIds.length} selezionat{selectedCategoryIds.length === 1 ? 'a' : 'e'}
          </span>
        )}
      </div>
      <div className="overflow-x-auto hide-scrollbar">
        <motion.div 
          className="flex space-x-6 px-4 pb-2"
          variants={container}
          initial="hidden"
          animate="show"
        >
          {categories.map((category) => (
            <motion.div
              key={category.id}
              variants={item}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex-shrink-0 flex flex-col items-center cursor-pointer group relative"
              onClick={() => onCategorySelect?.(category.id)}
            >
              <div className={`w-16 h-16 rounded-2xl shadow-sm 
                            flex items-center justify-center
                            group-hover:shadow-md transition-all duration-200
                            relative ${
                              isSelected(category.id)
                                ? 'bg-brand-primary/10 border-2 border-brand-primary text-brand-primary'
                                : 'bg-gradient-to-br from-white to-gray-50 border border-gray-100 text-brand-primary'
                            }`}>
                {getCategoryIcon(category.icon)}
                {isSelected(category.id) && (
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-brand-primary rounded-full flex items-center justify-center">
                    <Icon icon="lucide:check" width="12" className="text-white" />
                  </div>
                )}
              </div>
              <span className={`mt-2 text-sm font-medium ${
                isSelected(category.id)
                  ? 'text-brand-primary' 
                  : 'text-gray-700 group-hover:text-brand-primary'
              } transition-colors`}>
                {category.name}
              </span>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Categories;


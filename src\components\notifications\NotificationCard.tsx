
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { Bell, Users, Calendar, MessageCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface NotificationCardProps {
  notification: {
    id: string;
    entity: string;
    entity_id: string;
    created_at: string;
  };
  onClick?: () => void;
}

const NotificationCard = ({ notification, onClick }: NotificationCardProps) => {
  // Determina l'icona e il messaggio basandosi sull'entity
  const getNotificationDetails = (entity: string) => {
    switch (entity) {
      case 'group_invites':
        return {
          icon: <Users className="h-5 w-5 text-blue-500" />,
          title: "Invito al gruppo",
          message: "Hai ricevuto un nuovo invito per unirti a un gruppo",
          bgColor: "bg-blue-50",
          borderColor: "border-blue-200"
        };
      case 'bookings':
        return {
          icon: <Calendar className="h-5 w-5 text-green-500" />,
          title: "Prenotazione",
          message: "Aggiornamento sulla tua prenotazione",
          bgColor: "bg-green-50",
          borderColor: "border-green-200"
        };
      case 'messages':
        return {
          icon: <MessageCircle className="h-5 w-5 text-orange-500" />,
          title: "Nuovo messaggio",
          message: "Hai ricevuto un nuovo messaggio",
          bgColor: "bg-orange-50",
          borderColor: "border-orange-200"
        };
      default:
        return {
          icon: <Bell className="h-5 w-5 text-gray-500" />,
          title: "Notifica",
          message: "Hai una nuova notifica",
          bgColor: "bg-gray-50",
          borderColor: "border-gray-200"
        };
    }
  };

  const details = getNotificationDetails(notification.entity);

  return (
    <div
      onClick={onClick}
      className={`${details.bgColor} ${details.borderColor} border rounded-lg p-4 cursor-pointer hover:shadow-md transition-all duration-200`}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-1">
          {details.icon}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h3 className="font-medium text-gray-900 text-sm">
              {details.title}
            </h3>
            <span className="text-xs text-gray-500 flex-shrink-0">
              {format(new Date(notification.created_at), "HH:mm", { locale: it })}
            </span>
          </div>
          
          <p className="text-sm text-gray-600 mb-2">
            {details.message}
          </p>
          
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="text-xs">
              {notification.entity.replace('_', ' ')}
            </Badge>
            
            <span className="text-xs text-gray-400">
              {format(new Date(notification.created_at), "dd MMM", { locale: it })}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationCard;

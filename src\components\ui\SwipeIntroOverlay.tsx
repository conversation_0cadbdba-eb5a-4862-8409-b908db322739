import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronLeft, ChevronRight, X, Mic } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import ButtonDate from "@/components/ButtonDate";

interface SwipeIntroOverlayProps {
  onClose: () => void; //
  onDontShowAgain?: () => void;
  onOpenVoiceDialog?: () => void;
  className?: string;
}

const SwipeIntroOverlay: React.FC<SwipeIntroOverlayProps> = ({
  onDontShowAgain,
  onClose,
  onOpenVoiceDialog,
  className,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [dontShowAgain, setDontShowAgain] = useState(false);
  const [selectedDayIndex, setSelectedDayIndex] = useState(0); // Track which day is selected
  
  // Create sample dates for the animation
  const today = new Date();
  const sampleDates = [
    new Date(today.getFullYear(), today.getMonth(), 7), // Day 7
    new Date(today.getFullYear(), today.getMonth(), 8), // Day 8
    new Date(today.getFullYear(), today.getMonth(), 9), // Day 9
    new Date(today.getFullYear(), today.getMonth(), 10), // Day 10
    new Date(today.getFullYear(), today.getMonth(), 11), // Day 11
  ];
  
  // Voice assistant example sentences
  const voiceExamples = [
    "Cerco un taglio di capelli vicino a me, per domani alle 18.00. Sconto minimo del 20%",
    "Trova un ristorante per questa sera con almeno il 30% di sconto",
    "Voglio prenotare una pizza per le 19:30, zona centro",
    "Cerca una SPA per il weekend con massaggio incluso per tutta la famiglia.",
    "Mostrami le offerte per un aperitivo stasera all'uscita dal lavoro.",
    "Prenota un corso di yoga per la prossima settimana tra le 18 e le 20.",
    "Nel weekend sono a Milano. Organizzami una meravigliosa esperienza turistica."
  ];
  
  const [currentVoiceExample, setCurrentVoiceExample] = useState(0);
  const [showVoiceBalloon, setShowVoiceBalloon] = useState(false);

  // Animate day selection cycling
  useEffect(() => {
    if (currentStep === 1) { // Only animate on day selector step
      setSelectedDayIndex(0); // Reset to first day when entering step
      const interval = setInterval(() => {
        setSelectedDayIndex((prev) => (prev + 1) % sampleDates.length);
      }, 1500); // Change selected day every 1.5 seconds
      
      return () => clearInterval(interval);
    }
  }, [currentStep, sampleDates.length]);

  // Animate voice examples cycling
  useEffect(() => {
    if (currentStep === 4) { // Only animate on voice assistant step (now step 4)
      setCurrentVoiceExample(0); // Reset to first example when entering step
      setShowVoiceBalloon(true); // Show balloon immediately
      
      const interval = setInterval(() => {
        // Fade out current balloon
        setShowVoiceBalloon(false);
        
        // After fade out, change text and fade in
        setTimeout(() => {
          setCurrentVoiceExample((prev) => (prev + 1) % voiceExamples.length);
          setShowVoiceBalloon(true);
        }, 500); // 0.5s delay for fade out
      }, 3000); // Change example every 3 seconds
      
      return () => {
        clearInterval(interval);
        setShowVoiceBalloon(false);
      };
    } else {
      setShowVoiceBalloon(false);
    }
  }, [currentStep, voiceExamples.length]);

  const steps = [
    {
      title: "Scorri per esplorare",
      description:
        "Puoi scorrere le carte a sinistra e destra per vedere più contenuti",
      animation: "swipe-left-right",
    },
    {
      title: "Seleziona il giorno",
      description:
        "Scegli un giorno specifico per vedere le offerte disponibili in quella data",
      animation: "day-selector",
    },
    {
      title: "Naviga per orario",
      description:
        "Usa lo slider per selezionare un orario e trovare offerte disponibili in quel momento",
      animation: "time-slider",
    },
    {
      title: "Sei pronto per iniziare!",
      description: "Tocca qualsiasi carta per vedere i dettagli, prenotare e iniziare a risparmiare con CatchUp!",
      animation: "tap",
    },
          {
        title: "Xxx Xxxxxx xxxxx xxxxx x xxxxxx xxxxx",
        description: "Xxx x'xxxxxxx xxxxx x xxxxxxx xxx^xx xxx x xx x x xx ",
        animation: "voice-assistant",
      },
  ];

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleClose();
    }
  };

  const skipIntro = () => {
    handleClose();
  };

  const handleClose = () => {
    if (dontShowAgain && onDontShowAgain) {
      onDontShowAgain();
    } else {
      onClose();
    }
  };

    const AnimatedTimeSync = () => {
    const [displayTime, setDisplayTime] = useState("08:30");
    
    useEffect(() => {
      const startTime = Date.now();
      
      const updateTime = () => {
        const elapsed = Date.now() - startTime;
        const cycle = (elapsed % 6000) / 6000; // 6 second cycle to match cursor
        
        let progress;
        if (cycle <= 0.5) {
          // First half: 20% to 70% position
          progress = 0.2 + cycle * 2 * 0.5; // 0.2 to 0.7
        } else {
          // Second half: 70% to 20% position  
          progress = 0.7 - (cycle - 0.5) * 2 * 0.5; // 0.7 to 0.2
        }
        
        // Map progress (0.2-0.7) to time (08:30-16:30)
        const minTime = 8.5; // 08:30
        const maxTime = 16.5; // 16:30
        const currentTime =
          minTime + ((progress - 0.2) * (maxTime - minTime)) / 0.5;
        
        // Convert to hours and minutes
        const hours = Math.floor(currentTime);
        const minutesDecimal = (currentTime - hours) * 60;
        
        // Round minutes to nearest 10 (0, 10, 20, 30, 40, 50)
        const minutes = Math.round(minutesDecimal / 10) * 10;
        
        const timeString = `${hours.toString().padStart(2, "0")}:${minutes
          .toString()
          .padStart(2, "0")}`;
        setDisplayTime(timeString);
      };
      
      // Start immediately
      updateTime();
      
      // Update every 100ms for smooth animation
      const interval = setInterval(updateTime, 100);
      
      return () => clearInterval(interval);
    }, []);
    
    return <span>{displayTime}</span>;
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className={cn(
          "fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4",
          className
        )}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className={cn(
            "bg-white rounded-2xl p-6 max-w-sm w-full shadow-2xl relative overflow-hidden",
            steps[currentStep].animation === "voice-assistant" && "bg-gradient-to-br from-white via-red-50 to-pink-50"
          )}
        >
          {/* Final step background animation */}
          {steps[currentStep].animation === "voice-assistant" && (
            <>
              {/* Solid background for readability */}
              <div className="absolute inset-0 bg-gradient-to-br from-red-50/80 via-pink-50/60 to-white/90" />
              
              {/* Animated overlay */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-red-500/8 to-pink-500/8"
                animate={{
                  opacity: [0.4, 0.7, 0.4],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
              
              {/* Subtle pattern overlay for texture */}
              <div 
                className="absolute inset-0 opacity-5"
                style={{
                  backgroundImage: `radial-gradient(circle at 20% 50%, rgba(239, 68, 68, 0.3) 0%, transparent 50%), 
                                   radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.3) 0%, transparent 50%),
                                   radial-gradient(circle at 40% 80%, rgba(239, 68, 68, 0.2) 0%, transparent 50%)`,
                }}
              />
            </>
          )}
          {/* <Button
            variant="ghost"
            size="sm"
            onClick={skipIntro}
            className="absolute top-4 right-4 h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button> */}

          <div className="text-center space-y-6 relative z-10">
            {/* Animation Visual */}
            <div className="relative h-36 flex items-center justify-center">
              {steps[currentStep].animation === "swipe-left-right" && (
                <div className="flex items-center gap-4">
                  <motion.div
                    animate={{
                      x: [-20, 20, -20],
                      opacity: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                    className="bg-primary/20 rounded-xl p-4"
                  >
                    <ChevronLeft className="h-6 w-6 text-primary" />
                  </motion.div>

                  <motion.div
                    animate={{
                      scale: [1, 1.1, 1],
                      backgroundColor: [
                        "hsl(var(--primary))",
                        "hsl(var(--primary))",
                        "hsl(var(--primary))",
                      ],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                    className="w-16 h-24 bg-primary/10 rounded-lg border-2 border-primary/30"
                  />

                  <motion.div
                    animate={{
                      x: [20, -20, 20],
                      opacity: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                    className="bg-primary/20 rounded-xl p-4"
                  >
                    <ChevronRight className="h-6 w-6 text-primary" />
                  </motion.div>
                </div>
              )}

              {steps[currentStep].animation === "day-selector" && (
                <div className="flex items-center justify-center gap-3">
                  {sampleDates.map((date, i) => (
                    <motion.div
                      key={i}
                      animate={{
                        scale: i === selectedDayIndex ? [1, 1.05, 1] : [0.95, 1, 0.95],
                        opacity: i === selectedDayIndex ? [1, 1, 1] : [0.4, 0.8, 0.4],
                      }}
                      transition={{
                        duration: i === selectedDayIndex ? 2 : 0.5, // Slower pulse for selected day
                        repeat: i === selectedDayIndex ? Infinity : 0,
                        ease: "easeInOut",
                        delay: i === selectedDayIndex ? 0 : 0,
                      }}
                    >
                      <motion.div
                        animate={{
                          scale: i === selectedDayIndex ? 1 : 1,
                        }}
                        transition={{
                          duration: 0.3,
                          ease: "easeInOut",
                        }}
                      >
                        <ButtonDate
                          date={date}
                          isSelected={i === selectedDayIndex} // Dynamic selection
                          today={today}
                          onSelect={() => {}} // No-op for animation
                        />
                      </motion.div>
                    </motion.div>
                  ))}
                </div>
              )}

              {steps[currentStep].animation === "time-slider" && (
                <div className="w-full space-y-2">
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>06:00</span>
                    <span>18:00</span>
                  </div>
                  <div className="relative w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                    <motion.div
                      className="absolute inset-0 rounded-full"
                      style={{
                        background: `linear-gradient(to right,
            rgb(251 191 36) 0%,
            hsl(var(--primary)) 50%,
            rgb(79 70 229) 100%
          )`,
                      }}
                    />
                    <motion.div
                      animate={{
                        left: ["20%", "70%", "20%"],
                      }}
                      transition={{
                        duration: 6, // Changed from 3 to 6 seconds
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                      className="absolute top-1/2 -translate-y-1/2 w-4 h-4 bg-white rounded-full border-2 border-primary shadow-lg"
                    />
                  </div>
                  <motion.div
                    animate={{
                      opacity: [0.7, 1, 0.7],
                    }}
                    transition={{
                      duration: 2, // Slower opacity animation
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                    className="text-center text-sm font-semibold text-primary"
                  >
                    <motion.span
                      animate={{
                        scale: [1, 1.05, 1],
                      }}
                      transition={{
                        duration: 6, // Match the cursor timing
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    >
                      <AnimatedTimeSync />
                    </motion.span>
                  </motion.div>
                </div>
              )}

              {steps[currentStep].animation === "voice-assistant" && (
                <div className="relative flex flex-col items-center gap-4">
                  {/* Celebration sparkles around the microphone */}
                  {Array.from({ length: 12 }).map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-1 bg-gradient-to-r from-yellow-400 to-red-400 rounded-full"
                      animate={{
                        x: [0, Math.cos(i * 30 * Math.PI / 180) * 80],
                        y: [0, Math.sin(i * 30 * Math.PI / 180) * 80],
                        opacity: [0, 1, 0],
                        scale: [0.5, 1, 0.5],
                      }}
                      transition={{
                        duration: 2.5,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: i * 0.15,
                      }}
                      style={{
                        left: "50%",
                        top: "20%",
                        transform: "translate(-50%, -50%)",
                      }}
                    />
                  ))}
                  
                  {/* Enhanced microphone button */}
                  <motion.div
                    animate={{
                      scale: [1, 0.95, 1],
                      boxShadow: [
                        "0 4px 20px rgba(239, 68, 68, 0.2)",
                        "0 12px 40px rgba(239, 68, 68, 0.4)",
                        "0 4px 20px rgba(239, 68, 68, 0.2)",
                      ],
                      rotate: [0, 2, -2, 0],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                    className="relative w-16 h-16 bg-gradient-to-br from-brand-primary via-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg overflow-hidden"
                  >
                    {/* Glass morphism overlay */}
                    <div className="absolute inset-0 bg-white/20 backdrop-blur-sm rounded-full"></div>
                    
                    {/* Shine effect */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12"
                      animate={{
                        x: ["-100%", "100%"],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    />
                    
                    <Mic className="h-6 w-6 text-white relative z-10" />
                    
                    {/* Animated glow effect */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-br from-brand-primary/50 to-purple-500/50 rounded-full opacity-60"
                      animate={{
                        opacity: [0.4, 0.8, 0.4],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    />
                  </motion.div>

                  {/* Speech balloon below microphone */}
                  <motion.div
                    animate={{
                      opacity: showVoiceBalloon ? 1 : 0,
                      scale: showVoiceBalloon ? 1 : 0.9,
                    }}
                    transition={{
                      duration: 0.5,
                      ease: "easeInOut",
                    }}
                    className="relative"
                  >
                    {/* Balloon tail pointing up to microphone */}
                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full">
                      <div className="w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-white/95"></div>
                    </div>
                    
                    {/* Balloon */}
                    <div className="backdrop-blur-sm rounded-2xl px-4 py-3 shadow-lg border bg-white/95 border-white/20 max-w-xs">
                      <p className="font-medium text-xs text-purple-800 leading-relaxed text-center">
                        {voiceExamples[currentVoiceExample]}
                      </p>
                    </div>
                  </motion.div>
                </div>
              )}

              {steps[currentStep].animation === "tap" && (
                <div className="relative">
                  {/* Simple card animation */}
                  <motion.div
                    animate={{
                      scale: [1, 0.95, 1],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                    className="w-24 h-32 bg-gradient-to-br from-primary/20 to-primary/30 rounded-xl border-2 border-primary/40 flex items-center justify-center relative overflow-hidden"
                  >
                    {/* Center pulse */}
                    <motion.div
                      animate={{
                        scale: [1, 1.3, 1],
                        opacity: [0.8, 1, 0.8],
                      }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                      className="w-8 h-8 bg-gradient-to-r from-primary to-primary/80 rounded-full shadow-lg"
                    />
                    
                    {/* Tap indicator */}
                    <motion.div
                      className="absolute -top-2 -right-2 text-lg"
                      animate={{
                        scale: [1, 1.2, 1],
                        rotate: [0, 10, -10, 0],
                      }}
                      transition={{
                        duration: 1,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    >
                      👆
                    </motion.div>
                  </motion.div>
                </div>
              )}
            </div>

            {/* Content */}
            <div className="space-y-3">
              <motion.h3 
                className="text-xl font-semibold text-gray-900"
                animate={steps[currentStep].animation === "voice-assistant" ? {
                  scale: [1, 1.02, 1],
                  backgroundImage: [
                    "linear-gradient(45deg, #ef4444, #ec4899)",
                    "linear-gradient(45deg, #ec4899, #ef4444)",
                    "linear-gradient(45deg, #ef4444, #ec4899)",
                  ],
                  backgroundClip: "text",
                  color: "transparent",
                } : {}}
                transition={{
                  duration: 2,
                  repeat: steps[currentStep].animation === "voice-assistant" ? Infinity : 0,
                  ease: "easeInOut",
                }}
              >
                {steps[currentStep].title}
              </motion.h3>
              <p className="text-gray-600 leading-relaxed">
                {steps[currentStep].description}
              </p>
            </div>

            {/* Progress indicators */}
            <div className="flex justify-center gap-1.5">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={cn(
                    "w-2 h-2 rounded-full transition-colors",
                    index === currentStep ? "bg-primary" : "bg-gray-300"
                  )}
                />
              ))}
            </div>

            {/* Action Button */}
            {steps[currentStep].animation === "voice-assistant" ? (
              <div className="relative">
                {/* Celebration particles */}
                <div className="absolute inset-0 pointer-events-none">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-2 h-2 bg-gradient-to-r from-red-400 to-pink-400 rounded-full"
                      animate={{
                        x: [0, Math.cos(i * 45 * Math.PI / 180) * 60],
                        y: [0, Math.sin(i * 45 * Math.PI / 180) * 60],
                        opacity: [1, 0],
                        scale: [1, 0.5],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeOut",
                        delay: i * 0.2,
                      }}
                      style={{
                        left: "50%",
                        top: "50%",
                        transform: "translate(-50%, -50%)",
                      }}
                    />
                  ))}
                </div>
                
                {/* Enhanced button */}
                <motion.div
                  animate={{
                    scale: [1, 1.02, 1],
                    boxShadow: [
                      "0 4px 20px rgba(239, 68, 68, 0.3)",
                      "0 8px 30px rgba(239, 68, 68, 0.5)",
                      "0 4px 20px rgba(239, 68, 68, 0.3)",
                    ],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                >
                  <Button 
                    onClick={() => {
                      if (onOpenVoiceDialog) {
                        onOpenVoiceDialog();
                      }
                      handleClose();
                    }}
                    className="w-full bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white font-bold text-lg py-4 shadow-lg border-0 relative overflow-hidden"
                    size="lg"
                  >
                    <motion.div
                      className="absolute inset-0 bg-white/20"
                      animate={{
                        x: ["-100%", "100%"],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    />
                    <span className="relative z-10 flex items-center justify-center gap-2">
                      🎉 Let's CatchUp! 🚀
                    </span>
                  </Button>
                </motion.div>
              </div>
            ) : (
              <Button onClick={nextStep} className="w-full" size="lg">
                Avanti
              </Button>
            )}

            {/* Don't show again checkbox */}
            <div className="flex items-center justify-center space-x-2 mt-4">
              <Checkbox
                id="dont-show-again"
                checked={dontShowAgain}
                onCheckedChange={(checked) =>
                  setDontShowAgain(checked === true)
                }
              />
              <label
                htmlFor="dont-show-again"
                className="text-sm text-gray-600 cursor-pointer"
              >
                Non mostrare più
              </label>
            </div>

            {/* Skip option */}
            {currentStep === 0 && (
              <button
                onClick={skipIntro}
                className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
              >
                Salta introduzione
              </button>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default SwipeIntroOverlay;

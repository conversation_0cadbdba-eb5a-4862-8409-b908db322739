# Nearest Deals & Businesses Flow

This document explains how the application retrieves nearby businesses and deals through a comprehensive system of frontend hooks, components, and Supabase edge functions.

## Architecture Overview

The application uses two main edge functions for location-based searches:
- **`get-nearest-businesses`** - Finds nearby businesses with optional deal filtering
- **`get-nearby-deals-by-location`** - Searches for deals with availability filtering

## Components & Hooks

### Frontend Components
- **`NearestBusinesses`** – Component at `src/components/map/NearestBusinesses.tsx`
  - Displays list of nearby businesses with deal counts
  - Handles navigation to business pages or first available deal
  - Shows business thumbnails, distance, and interactive elements

### Frontend Hooks
- **`useNearestBusinesses`** – Hook at `src/hooks/useNearestBusinesses.tsx`
- **`useNearbyDealsSearch`** – Hook at `src/hooks/search/useNearbyDealsSearch.tsx`

## Edge Function: `get-nearest-businesses`

### Request Parameters
```typescript
interface RequestParams {
  latitude: number;           // Required - User's latitude
  longitude: number;          // Required - User's longitude
  limit?: number;            // Optional - Max results (default: 5)
  transportMode?: string;    // Optional - "DRIVING", "WALKING", etc. (default: "DRIVING")
  useDirections?: boolean;   // Optional - Include routing details (default: false)
  maxDistance_meters?: number; // Optional - Search radius (default: 5000)
  require_deals?: boolean;   // Optional - Only return businesses with deals (default: false)
}
```

### Response Format
```typescript
interface BusinessResponse {
  businesses: Array<{
    id: string;
    name: string;
    latitude: number;
    longitude: number;
    deal_count: number;
    address: string;
    distance: number;          // Distance in meters
    distanceText: string;      // Formatted distance (e.g., "1.2 km", "500 m")
    photos: string[] | null;
  }>;
  nearestBusiness: Business | null;
}
```

### Caching Strategy
- **Cache Key**: `${latitude}_${longitude}_${limit}_${transportMode}_${useDirections}_${require_deals}`
- **TTL**: 10 minutes (600,000 ms)
- **Storage**: In-memory cache within the edge function
- **Cache Hit**: Returns cached data immediately
- **Cache Miss**: Queries database and updates cache

### Database Integration
```sql
-- Calls PostGIS stored procedure
get_nearest_businesses_postgis(
  user_lat: float,
  user_lon: float, 
  max_distance_km: float,
  max_results: integer,
  require_deals: boolean
)
```

## Edge Function: `get-nearby-deals-by-location`

### Request Parameters
```typescript
interface RequestParams {
  date: string;              // Required - Search date (YYYY-MM-DD)
  time?: string;            // Optional - Specific time (HH:MM)
  location?: {              // Optional - User coordinates
    lat: number;
    lng: number;
    radius?: number;        // Search radius in meters (default: 5000)
  };
  cityName?: string;        // Optional - Alternative to coordinates
  category_id?: string;     // Optional - Filter by category
  cursor?: string;          // Optional - Pagination cursor
  limit?: number;           // Optional - Results per page (default: 20)
}
```

### Response Format
```typescript
interface AvailabilityResponse {
  date: string;
  time?: string;
  deal_count: number;
  user_location?: LocationParams;
  deals: Array<{
    id: string;
    name: string;
    description: string;
    original_price: number;
    discounted_price: number;
    discount_percentage: number;
    images: string[] | null;
    time_slots: any;           // Available time slots
    status: "published" | "draft" | "expired";
    created_at: string;
    updated_at: string;
    auto_confirm: boolean;
    category_id: string;
    start_date: string;
    end_date: string;
    terms_conditions: string;
    business: {
      id: string;
      name: string;
      address: string;
      latitude: number;
      longitude: number;
      distance: number;
      category_id: string;
    }
  }>;
  next_cursor: string | null;  // Pagination cursor
  cached?: boolean;
}
```

### Database Integration
```sql
-- Calls optimized stored procedure
get_nearby_deals(
  p_date: date,
  p_time: time,
  p_lat: float,
  p_lng: float,
  p_radius: integer,
  p_category_id: uuid,
  p_cursor: text,
  p_limit: integer
)
```

## Detailed Sequence Diagrams

### NearestBusinesses Component Flow

```mermaid
sequenceDiagram
    participant UI as "NearestBusinesses"
    participant Hook as "useNearestBusinesses"
    participant Edge as "get-nearest-businesses"
    participant Cache as "Edge Function Cache"
    participant DB as "Supabase DB"
    participant DealsDB as "Deals Table"

    UI->>Hook: Initialize with options {require_deals: true}
    Hook->>Edge: POST {lat, lng, maxDistance_meters: 5000, require_deals: true}
    
    alt Cache Hit
        Edge->>Cache: Check cache key
        Cache-->>Edge: Return cached data
        Edge-->>Hook: Cached businesses list
    else Cache Miss
        Edge->>DB: RPC get_nearest_businesses_postgis()
        DB-->>Edge: Raw business data with PostGIS calculations
        Edge->>Edge: Format distance text, structure response
        Edge->>Cache: Store in cache (TTL: 10min)
        Edge-->>Hook: Fresh businesses list
    end
    
    Hook-->>UI: businesses[], loading: false
    UI->>DealsDB: Query deals for each business_id
    DealsDB-->>UI: Deal IDs per business
    UI->>UI: Render business list with deal counts
    
    Note over UI: User clicks business
    UI->>UI: Navigate to first deal or business page
```

### useNearbyDealsSearch Hook Flow

```mermaid
sequenceDiagram
    participant Hook as "useNearbyDealsSearch"
    participant Edge as "get-nearby-deals-by-location"
    participant DB as "Supabase DB"
    participant Validation as "Input Validation"

    Hook->>Hook: Initialize params {date, time?, location?, category_id?}
    Hook->>Edge: POST search parameters + pagination cursor
    
    Edge->>Validation: Validate required params (date)
    alt Validation Failed
        Validation-->>Edge: Error response
        Edge-->>Hook: 400 Error
        Hook->>Hook: Set error state
    else Validation Passed
        Edge->>DB: RPC get_nearby_deals() with all parameters
        DB->>DB: PostGIS distance calculation + time slot filtering
        DB-->>Edge: Deals with business info + distance
        Edge->>Edge: Transform data, validate deal status
        Edge-->>Hook: Structured deal response + next_cursor
    end
    
    Hook->>Hook: Update state {results, metrics, nextCursor}
    
    Note over Hook: Load More Called
    Hook->>Edge: POST with existing cursor
    Edge->>DB: RPC get_nearby_deals() with cursor offset
    DB-->>Edge: Next page of deals
    Edge-->>Hook: Additional deals
    Hook->>Hook: Append to existing results
```

## Error Handling & Resilience

### get-nearest-businesses Error Handling
```mermaid
flowchart TD
    A[Request Received] --> B{Valid Coordinates?}
    B -->|No| C[Return 400 Error]
    B -->|Yes| D{Cache Available?}
    D -->|Yes| E[Return Cached Data]
    D -->|No| F[Query PostGIS]
    F --> G{DB Success?}
    G -->|No| H[Log Error + Return Empty Response]
    G -->|Yes| I[Format & Cache Data]
    I --> J[Return Formatted Response]
    
    H --> K[Status: 500, businesses: []]
    C --> L[Status: 400, Missing coordinates]
    E --> M[Status: 200, Cached Response]
    J --> N[Status: 200, Fresh Response]
```

### get-nearby-deals-by-location Error Handling
```mermaid
flowchart TD
    A[Request Received] --> B{Valid JSON?}
    B -->|No| C[Return 400: Invalid JSON]
    B -->|Yes| D{Date Present?}
    D -->|No| E[Return 400: Missing Date]
    D -->|Yes| F{Location or City?}
    F -->|No| G[Return 400: Missing Location]
    F -->|Yes| H[Call get_nearby_deals RPC]
    H --> I{DB Success?}
    I -->|No| J[Return 500: DB Error]
    I -->|Yes| K[Transform & Validate Data]
    K --> L[Return Structured Response]
```

## Performance Optimizations

### Caching Strategy
1. **Edge Function Level**: In-memory cache with 10-minute TTL
2. **Database Level**: PostGIS spatial indexing for fast geographic queries
3. **Frontend Level**: React Query caching in hooks

### Database Optimizations
- **Spatial Indexing**: PostGIS indexes on business coordinates
- **Composite Indexes**: On deal availability and location fields
- **Query Optimization**: Stored procedures reduce round trips

### Data Transformations

#### Distance Formatting
```typescript
// In get-nearest-businesses
let distanceText = "";
if (business.distance < 1000) {
  distanceText = `${Math.round(business.distance)} m`;
} else {
  distanceText = `${(business.distance / 1000).toFixed(1)} km`;
}
```

#### Deal Status Validation
```typescript
// In get-nearby-deals-by-location
function validateDealStatus(status: string | undefined): "published" | "draft" | "expired" {
  if (status === 'published' || status === 'draft' || status === 'expired') {
    return status;
  }
  return 'published'; // Default fallback
}
```

## Frontend Integration Patterns

### Hook Usage Pattern
```typescript
// useNearestBusinesses
const { businesses, loading, error } = useNearestBusinesses({
  maxDistance_meters: 5000,
  require_deals: true,
});

// useNearbyDealsSearch
const {
  results,
  isLoading,
  hasMore,
  loadMore,
  updateSearchParams
} = useNearbyDealsSearch({
  date: selectedDate,
  location: userLocation
});
```

### Component State Management
```typescript
// NearestBusinesses component manages:
// - Business data from hook
// - Deal IDs per business (separate query)
// - Navigation logic
// - Loading states for UI feedback
```

## API Contracts

### Edge Function URLs
- **Production**: `https://[project].supabase.co/functions/v1/get-nearest-businesses`
- **Production**: `https://[project].supabase.co/functions/v1/get-nearby-deals-by-location`

### Authentication
- Uses Supabase service role key for database access
- No user authentication required for public searches

### Rate Limiting
- Edge functions inherit Supabase's rate limiting
- Caching reduces database load for repeated requests

This comprehensive flow ensures efficient, scalable location-based search functionality with proper error handling and performance optimizations.


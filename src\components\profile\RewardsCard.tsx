import React from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";

const RewardsCard = () => {
  const navigate = useNavigate();

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="overflow-hidden border-none bg-gradient-to-br from-white to-brand-light shadow-md">
        <CardContent className="p-6 flex flex-col items-center text-center">
          <div className="flex items-center mb-4 font-bold text-2xl">
            <img
              src="/icon-192x192.png"
              alt="CatchUp Logo"
              className="w-12 h-12 object-contain"
            />
            <span className="bg-gradient-to-r from-orange-400 via-red-500 to-pink-500 bg-clip-text text-transparent">
              CATCHUP
            </span>
          </div>

          <h3 className="text-xl font-bold mb-2 bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
            <PERSON><PERSON> prenoti, più risparmi!
          </h3>

          <p className="text-gray-700 mb-6 relative">
            <span className="relative inline-block">
              Unisciti alla community e ricevi buoni da
              <span className="font-semibold mx-1 text-brand-primary relative">
                €10
                <span className="absolute -bottom-1 left-0 w-full h-1 bg-brand-light rounded-full"></span>
              </span>
              o
              <span className="font-semibold mx-1 text-brand-primary relative">
                €25
                <span className="absolute -bottom-1 left-0 w-full h-1 bg-brand-light rounded-full"></span>
              </span>
            </span>
          </p>

          <button
            onClick={() => navigate("/login")}
            className="w-full py-3 px-4 bg-brand-primary text-white rounded-lg font-medium hover:bg-brand-secondary transition-colors mb-4"
          >
            Accedi
          </button>

          <div className="text-gray-600">
            Non sei ancora membro?{" "}
            <button
              onClick={() => navigate("/signup")}
              className="text-brand-primary font-medium hover:underline"
            >
              Registrati
            </button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default RewardsCard;

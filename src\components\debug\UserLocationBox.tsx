// src/components/debug/UserLocationBox.tsx
import React, { useState } from "react";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, EyeOff, RefreshCw, AlertTriangle } from "lucide-react";
import { refreshLocation } from "@/contexts/LocationContext";

const UserLocationBox = () => {
  const { 
    userLocation, 
    demoEnabled, 
    fallbackMode, 
    isLoading,
    isPermissionDenied,
    source 
  } = useLocationManagement();
  // Debug log when props change
  // console.log("UserLocationBox rendering with:",
  //    {
  //   userLocation,
  //   demoEnabled,
  //   source
  // });
  const [isHidden, setIsHidden] = useState(true);

  if (isHidden) {
    return (
      <div className="flex justify-end mb-2">
        <Button
          variant="ghost"
          size="sm"
          className="text-xs h-6 px-2"
          onClick={() => setIsHidden(false)}
        >
          Show Location
          <Eye className="h-3 w-3 ml-1" />
        </Button>
      </div>
    );
  }

  const handleRefresh = () => {
    refreshLocation();
  };

  return (
    <div className="text-xs bg-gray-50 border border-gray-200 p-2 rounded mb-2">
      <div className="flex justify-between items-center">
        <span className="font-medium">User Location</span>
        <div className="flex items-center gap-2">
          {demoEnabled && <Badge className="text-[10px] h-4 bg-yellow-100 text-yellow-800">Demo</Badge>}
          {fallbackMode && <Badge className="text-[10px] h-4 bg-red-100 text-red-800">Fallback</Badge>}
          {source === 'cache' && <Badge className="text-[10px] h-4 bg-blue-100 text-blue-800">Cached</Badge>}
          {isPermissionDenied && <Badge className="text-[10px] h-4 bg-red-100 text-red-800">Denied</Badge>}
          
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0"
            onClick={() => setIsHidden(true)}
          >
            <EyeOff className="h-3 w-3" />
          </Button>
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex items-center mt-1">
          <span className="text-gray-500">Loading...</span>
          <div className="ml-2 h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
        </div>
      ) : !userLocation ? (
        <div className="flex items-center justify-between mt-1">
          <div className="flex items-center">
            <AlertTriangle className="h-3 w-3 text-amber-500 mr-1" />
            <span className="text-amber-600">Location unavailable</span>
          </div>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-5 w-5 p-0" 
            onClick={handleRefresh}
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>
      ) : (
        <div className="flex justify-between items-center mt-1">
          <span className="font-mono">
            {userLocation.lat.toFixed(6)},{userLocation.lng.toFixed(6)}
          </span>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-5 w-5 p-0" 
            onClick={handleRefresh}
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>
      )}
    </div>
  );
};

export default UserLocationBox;


// import React, { useState } from "react";
// import { useLocationManagement } from "@/hooks/location/useLocationManagement";
// import { Badge } from "@/components/ui/badge";
// import { Button } from "@/components/ui/button";
// import { Eye, EyeOff } from "lucide-react";

// const UserLocationBox = () => {
//   const { userLocation, demoEnabled, fallbackMode } = useLocationManagement();
//   const [isHidden, setIsHidden] = useState(false);

//   if (isHidden) {
//     return (
//       <div className="flex justify-end mb-2">
//         <Button
//           variant="ghost"
//           size="sm"
//           className="text-xs h-6 px-2"
//           onClick={() => setIsHidden(false)}
//         >
//             Show Location
//           <Eye className="h-3 w-3 mr-1" />
        
//         </Button>
//       </div>
//     );
//   }

//   return (
//     <div className="text-xs bg-gray-50 border border-gray-200 p-2 rounded mb-2">
//       <div className="flex justify-between items-center">
//         <span className="font-medium">User Location</span>
//         <div className="flex items-center gap-2">
//           {demoEnabled && <Badge className="text-[10px] h-4 bg-yellow-100 text-yellow-800">Demo</Badge>}
//           {fallbackMode && <Badge className="text-[10px] h-4 bg-red-100 text-red-800">Fallback</Badge>}
//           <Button
//             variant="ghost"
//             size="sm"
//             className="h-4 w-4 p-0"
//             onClick={() => setIsHidden(true)}
//           >
//             <EyeOff className="h-3 w-3" />
//           </Button>
//         </div>
//       </div>
//       {!userLocation ? (
//         <span className="text-gray-500">Loading...</span>
//       ) : (
//         <span className="font-mono">
//           {userLocation.lat.toFixed(6)},{userLocation.lng.toFixed(6)}
//         </span>
//       )}
//     </div>
//   );
// };

// export default UserLocationBox;

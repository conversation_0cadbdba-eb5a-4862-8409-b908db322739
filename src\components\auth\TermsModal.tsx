
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface TermsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const TermsModal = ({ isOpen, onClose }: TermsModalProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto bg-white border-brand-light">
        <DialogHeader className="border-b pb-3 mb-2">
          <DialogTitle className="text-brand-primary">Termini e Condizioni</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 text-sm text-gray-600">
          <p>Ultimo aggiornamento: {new Date().toLocaleDateString()}</p>

          <h3 className="font-semibold text-brand-primary">1. Accettazione dei Termini</h3>
          <p>
            <PERSON><PERSON><PERSON><PERSON><PERSON> CatchUp, l'utente accetta di essere vincolato dai presenti termini e condizioni.
            Questi termini si applicano a tutti i servizi offerti attraverso la piattaforma.
          </p>

          <h3 className="font-semibold text-brand-primary">2. Descrizione del Servizio</h3>
          <p>
            CatchUp è una piattaforma che permette agli utenti di scoprire e prenotare offerte locali.
            Ci riserviamo il diritto di modificare o interrompere qualsiasi aspetto del servizio in qualsiasi momento.
          </p>

          <h3 className="font-semibold text-brand-primary">3. Registrazione</h3>
          <p>
            Per utilizzare alcuni servizi di CatchUp, è necessario registrarsi fornendo informazioni accurate e complete.
            L'utente è responsabile del mantenimento della sicurezza delle proprie credenziali di accesso.
          </p>

          <h3 className="font-semibold text-brand-primary">4. Utilizzo del Servizio</h3>
          <p>
            L'utente si impegna a utilizzare il servizio in conformità con le leggi applicabili e le nostre politiche.
            È vietato utilizzare il servizio per scopi illegali o non autorizzati.
          </p>

          <h3 className="font-semibold text-brand-primary">5. Limitazioni di Responsabilità</h3>
          <p>
            CatchUp non è responsabile per eventuali danni diretti, indiretti, incidentali o consequenziali
            derivanti dall'utilizzo o dall'impossibilità di utilizzo del servizio.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};


import { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Users, MoreVertical, Edit, Trash2, Share2, UserPlus } from 'lucide-react';
import type { GroupWithMemberCount } from '@/types/groups';
import { useDeleteGroup } from '@/hooks/group/useGroups';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface GroupCardProps {
  group: GroupWithMemberCount;
  onEdit?: (group: GroupWithMemberCount) => void;
  onShare?: (group: GroupWithMemberCount) => void;
  onClick?: (group: GroupWithMemberCount) => void;
}

const GroupCard = ({ group, onEdit, onShare, onClick }: GroupCardProps) => {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const deleteGroupMutation = useDeleteGroup();

  const handleDelete = async () => {
    try {
      await deleteGroupMutation.mutateAsync(group.id);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Error deleting group:', error);
    }
  };

  const isAdmin = group.user_role === 'admin';
  const hasPendingInvites = group.pending_invites_count && group.pending_invites_count > 0 && isAdmin;

  return (
    <>
      <Card className="hover:shadow-md transition-shadow cursor-pointer relative">
        {/* Pending invites badge */}
        {hasPendingInvites ?(
          <div className="absolute -top-2 -right-2 z-10">
            <Badge variant="secondary" className="bg-orange-100 text-orange-800 text-xs">
              <UserPlus className="h-3 w-3 mr-1" />
              {group.pending_invites_count} {group.pending_invites_count === 1 ? 'invito' : 'inviti'}
            </Badge>
          </div>
        ): null}
        
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3" onClick={() => onClick?.(group)}>
              <div className="w-12 h-12 rounded-full bg-brand-light flex items-center justify-center overflow-hidden">
                {group.avatar_url ? (
                  <img 
                    src={group.avatar_url} 
                    alt={group.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <Users className="h-6 w-6 text-brand-primary" />
                )}
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 line-clamp-1">{group.name}</h3>
                {group.description && (
                  <p className="text-sm text-gray-600 line-clamp-2 mt-1">{group.description}</p>
                )}
              </div>
            </div>
            
            {isAdmin && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit?.(group)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Modifica
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onShare?.(group)}>
                    <Share2 className="h-4 w-4 mr-2" />
                    Condividi
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => setShowDeleteDialog(true)}
                    className="text-red-600"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Elimina
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="pt-0" onClick={() => onClick?.(group)}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                {group.member_count} {group.member_count === 1 ? 'membro' : 'membri'}
              </span>
            </div>
            
            {isAdmin && (
              <Badge variant="secondary" className="bg-brand-light text-brand-primary">
                Admin
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Eliminare il gruppo?</AlertDialogTitle>
            <AlertDialogDescription>
              Questa azione non può essere annullata. Il gruppo "{group.name}" sarà permanentemente eliminato 
              insieme a tutte le condivisioni.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Annulla</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
              disabled={deleteGroupMutation.isPending}
            >
              {deleteGroupMutation.isPending ? 'Eliminazione...' : 'Elimina'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default GroupCard;

# Data Generation Components

This folder contains reusable components used in the data generation features of the application.

## Components

### BusinessSelectionList

A reusable component for selecting businesses from a list with checkboxes.

**Features:**
- Select/Deselect all businesses button
- Individual business selection with checkboxes
- Mobile-friendly design
- Scrollable list with fixed height

**Props:**
- `businesses`: Array of business objects to display
- `selectedBusinesses`: Array of selected business IDs
- `onToggleBusiness`: Function to call when a business is toggled
- `onSelectAll`: Function to call when "Select All" button is clicked
- `title`: Title text to display above the list (optional)
- `selectAllText`: Text to display for the "Select All" button (default: "Seleziona tutte")
- `deselectAllText`: Text to display for the "Deselect All" button (default: "Deseleziona tutte")
- `idPrefix`: Prefix for the checkbox IDs to ensure uniqueness (default: "business")

**Usage Example:**
```jsx
<BusinessSelectionList
  businesses={filteredBusinesses}
  selectedBusinesses={selectedBusinesses}
  onToggleBusiness={handleBusinessToggle}
  onSelectAll={handleSelectAllBusinesses}
  title={`Seleziona Attività (${selectedBusinesses.length} selezionate)`}
/>
```

### DateRangeSelector

A reusable component for selecting a date range with start and end date inputs.

**Features:**
- Two date inputs for start and end dates
- Mobile-friendly grid layout
- Optional description text
- Customizable labels

**Props:**
- `startDate`: The start date in YYYY-MM-DD format
- `endDate`: The end date in YYYY-MM-DD format
- `onStartDateChange`: Function to call when the start date changes
- `onEndDateChange`: Function to call when the end date changes
- `title`: Title text to display above the date range
- `description`: Optional description text to display below the date range (optional)
- `startLabel`: Label for the start date input (default: "Data Inizio")
- `endLabel`: Label for the end date input (default: "Data Fine")

**Usage Example:**
```jsx
<DateRangeSelector
  startDate={dealStartDate}
  endDate={dealEndDate}
  onStartDateChange={(date) => setDealStartDate(date)}
  onEndDateChange={(date) => setDealEndDate(date)}
  title="Periodo storico delle offerte"
/>
```
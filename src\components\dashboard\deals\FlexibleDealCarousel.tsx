import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/auth/useAuth";
import { Skeleton } from "@/components/ui/skeleton";
import { Deal } from "@/types/deals";
import { ChevronRight, LucideIcon } from "lucide-react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { Icon } from '@iconify/react';
import { 
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import SwipeHintIcon from '@/components/ui/SwipeHintIcon';
import { useInitialBounceAnimation } from '@/hooks/useInitialBounceAnimation';

interface DealItem {
  id: string;
  title: string;
  discounted_price: number;
  original_price: number;
  discount_percentage: number;
  images: string[];
  businesses: {
    name: string;
    city?: string;
  };
}

interface DealSection {
  id: string;
  name: string;
  icon?: string | LucideIcon;
  iconType?: 'iconify' | 'lucide';
  deals: DealItem[];
  viewAllUrl?: string;
  customFilter?: (deal: any) => boolean;
}

interface FlexibleDealCarouselProps {
  title: string;
  titleIcon?: LucideIcon;
  sections: DealSection[];
  className?: string;
  isLoading?: boolean;
  maxSections?: number;
  dealsPerSection?: number;
}

const FlexibleDealCarousel = ({ 
  title,
  titleIcon: TitleIcon,
  sections,
  className = "",
  isLoading = false,
  maxSections = 6,
  dealsPerSection = 10
}: FlexibleDealCarouselProps) => {
  const navigate = useNavigate();
  const [processedSections, setProcessedSections] = useState<DealSection[]>([]);
  const [loading, setLoading] = useState(isLoading);

  const { animationClasses, showSwipeIcon } = useInitialBounceAnimation({
    delay: 1500,
    duration: 800,
    enabled: processedSections.length > 0 && !loading
  });

  const handleDealClick = (dealId: string) => {
    navigate(`/deal/${dealId}`);
  };

  const handleViewAllSection = (section: DealSection) => {
    if (section.viewAllUrl) {
      navigate(section.viewAllUrl);
    }
  };

  useEffect(() => {
    // Process and limit sections
    const limitedSections = sections
      .slice(0, maxSections)
      .filter(section => section.deals.length > 0);
    
    setProcessedSections(limitedSections);
    setLoading(false);
  }, [sections, maxSections]);

  const renderIcon = (section: DealSection) => {
    if (!section.icon) return null;
    
    if (section.iconType === 'lucide' && typeof section.icon !== 'string') {
      const LucideIconComponent = section.icon as LucideIcon;
      return <LucideIconComponent className="h-6 w-6" />;
    }
    
    if (section.iconType === 'iconify' || typeof section.icon === 'string') {
      return <Icon icon={section.icon as string} width="24" height="24" />;
    }
    
    return null;
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Skeleton className="h-8 w-48" />
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="space-y-3">
              <Skeleton className="h-6 w-32" />
              <div className="flex gap-3 overflow-hidden">
                {[1, 2, 3, 4].map((j) => (
                  <Skeleton key={j} className="h-32 w-32 rounded-2xl flex-shrink-0" />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (processedSections.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Title Header */}
      <div className="flex items-center gap-3">
        {TitleIcon && (
          <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-2 rounded-xl">
            <TitleIcon className="h-5 w-5 text-white" />
          </div>
        )}
        <h2 className="text-xl font-bold text-gray-900">{title}</h2>
      </div>

      {/* Sections */}
      <div className="space-y-6">
        {processedSections.map((section) => (
          <motion.div
            key={section.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-3"
          >
            {/* Section Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {renderIcon(section)}
                <h3 className="text-lg font-semibold text-gray-900">{section.name}</h3>
                <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs font-medium">
                  {section.deals.length} offerte
                </span>
              </div>
              {section.viewAllUrl && (
                <button
                  onClick={() => handleViewAllSection(section)}
                  className="flex items-center gap-1 text-brand-primary hover:text-brand-primary/80 font-medium text-sm transition-colors"
                >
                  Vedi tutto
                  <ChevronRight className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Deals Swipeable Carousel */}
            <div className="relative">
              <Carousel
                opts={{
                  align: "start",
                  loop: false
                }}
                className={animationClasses}
              >
                <CarouselContent className="-ml-2" key={section.id}>
                  {section.deals.slice(0, dealsPerSection).map((deal) => (
                    <CarouselItem key={deal.id} className="pl-2 basis-auto">
                      <motion.div
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleDealClick(deal.id)}
                        className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer flex-shrink-0 w-40 border border-gray-100"
                      >
                        {/* Deal Image */}
                        <div className="relative h-24 overflow-hidden">
                          <img
                            src={
                              deal.images && deal.images.length > 0
                                ? deal.images[0]
                                : `https://picsum.photos/160/96?random=${deal.id}`
                            }
                            alt={deal.title}
                            className="w-full h-full object-cover"
                          />
                          {/* Business name */}  
                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-2">
                            <p className="text-white text-sm font-bold truncate">{deal.businesses.name}</p>
                          </div>
                          {/* Price Badge */}
                          <div className="absolute top-2 right-2">
                            <div className="bg-gradient-to-r from-brand-primary to-purple-600 text-white px-2 py-1 rounded-lg text-xs font-bold">
                              {deal.discounted_price === 0 ? (
                                <span>-{deal.discount_percentage}%</span>
                              ) : (
                                <span>€{deal.discounted_price}</span>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Deal Info */}
                        <div className="p-3 space-y-2 truncate">
                          <h4 className="font-semibold text-sm text-gray-900 line-clamp-2 leading-tight">
                            {deal.title}
                          </h4>
                          
                          <div className="text-xs text-gray-600 truncate">
                            {deal.businesses.name}
                            {deal.businesses.city && (
                              <span className="text-gray-400"> • {deal.businesses.city}</span>
                            )}
                          </div>

                          {/* Pricing */}
                          <div className="flex items-center gap-2">
                            {deal.discounted_price > 0 && (
                              <>
                                <span className="text-xs text-gray-400 line-through">
                                  €{deal.original_price}
                                </span>
                                <span className="text-sm font-bold text-brand-primary">
                                  €{deal.discounted_price}
                                </span>
                              </>
                            )}
                            {deal.discounted_price === 0 && (
                              <span className="text-sm font-bold text-brand-primary">
                                -{deal.discount_percentage}% OFF
                              </span>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    </CarouselItem>
                  ))}
                </CarouselContent>
              </Carousel>
              
              <SwipeHintIcon show={showSwipeIcon} />
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default FlexibleDealCarousel;
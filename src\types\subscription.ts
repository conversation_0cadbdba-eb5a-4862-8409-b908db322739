export interface Subscription {
  id: string;
  user_id: string;
  plan_type: SubscriptionPlan;
  status: SubscriptionStatus;
  start_date: string;
  end_date: string;
  created_at: string;
  updated_at: string;
  trial_end_date?: string;
  is_yearly?: boolean;
}



export type SubscriptionPlan = 'basic' | 'professional' | 'enterprise';

export type SubscriptionStatus = 'active' | 'inactive' | 'expired' | 'cancelled' | 'trial' | 'upgraded' | 'downgraded';

export interface SubscriptionPlanDetails {
  id: SubscriptionPlan;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: string[];
  max_businesses: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  trial_end_date?: string;
  is_yearly?: boolean;
}

// Type for Supabase pricing_tiers table
export interface PricingTier {
  tier_type: 'basic' | 'professional' | 'enterprise';
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: any;
  max_businesses: number;
  max_agents: number;
  allowed_agents: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
  id: string;
  trial_end_date?: string;
  is_yearly?: boolean;
}

export interface SubscriptionState {
  subscription: Subscription | null;
  isLoading: boolean;
  error: string | null;
  hasActiveSubscription: boolean;
  planDetails: SubscriptionPlanDetails | null;
}

// SUBSCRIPTION_PLANS is now fetched from the database using usePricingPlans hook
// The constant has been removed in favor of dynamic loading from pricing_tiers table 


# Sistema di Notifiche Mobile-First 2025

## Panoramica

Il nuovo sistema di notifiche per CatchUp implementa un approccio mobile-first con gestural interface seguendo le migliori pratiche del design 2025.

## Caratteristiche Principali

### 1. **Swipe Gestures per Group Invites**
- **Swipe destro**: <PERSON>ccetta invito (colore verde)
- **Swipe sinistro**: <PERSON><PERSON><PERSON><PERSON> invito (colore rosso)
- **Feedback aptico**: Vibrazioni per confermare le azioni
- **Animazioni fluide**: Utilizzando react-spring per transizioni naturali

### 2. **Sistema di Deduplicazione Intelligente**
- Evita la duplicazione di notifiche di booking che hanno già conversazioni attive
- Filtra automaticamente le notifiche processate
- Mantiene la coerenza dei dati tra notifications e conversations

### 3. **UI/UX Moderna**
- **Cards differenziate per tipo**: Colori e icone specifiche per ogni categoria
- **Micro-interazioni**: Animazioni di hover, scale e feedback visivo
- **Accessibilità**: Supporto VoiceOver e pulsanti di fallback per desktop
- **Tutorial interattivo**: Overlay iniziale per spiegare i gesti

## Architettura Tecnica

### Componenti Principali

#### `SwipeableGroupInviteCard`
```typescript
interface SwipeableGroupInviteCardProps {
  notification: Notification;
  onProcessed?: () => void;
}
```
- Gestisce i gesti di swipe con `@use-gesture/react`
- Implementa feedback aptico nativo
- Animazioni con `@react-spring/web`

#### `EnhancedNotificationCard`
```typescript
interface EnhancedNotificationCardProps {
  notification: Notification;
  onClick?: () => void;
  variant?: 'default' | 'success' | 'booking';
}
```
- Card unificate per tutti i tipi di notifica
- Styling dinamico basato sul tipo
- Ottimizzazione per tap gesture mobile

#### `SwipeHintOverlay`
- Tutorial interattivo per primo utilizzo
- Animazioni dimostrative
- Persistenza stato con localStorage

#### `useNotificationDeduplication`
```typescript
const useNotificationDeduplication = (
  notifications: Notification[], 
  bookingConversations: Conversation[]
) => {
  // Logica di deduplicazione
  // Separazione per tipo
  // Conteggio unificato
}
```

## Flusso Utente

### Tab "Notifiche"
1. **Group Invites** (in alto, swipeable)
2. **Booking Conversations** (distintive con badge blu)
3. **Booking Notifications** (filtrate per duplicati)
4. **Altre Notifiche** (generiche)

### Interazioni
- **Tap**: Navigazione standard
- **Swipe** (solo group invites): Azioni rapide accept/decline
- **Long press**: Future espansioni (context menu)

## Sequence Diagrams

### 1. Swipe Gesture per Group Invite

```mermaid
sequenceDiagram
    participant U as User
    participant C as SwipeableCard
    participant G as useGesture Hook
    participant S as React Spring
    participant A as API Hook
    participant T as Toast

    U->>C: Swipe Right (Accept)
    C->>G: onDrag event
    G->>S: Update x position
    S->>C: Animate card movement
    
    alt Swipe threshold reached
        C->>C: handleAccept()
        C->>A: acceptInvite.mutateAsync()
        A->>A: Update group_invites status
        A-->>C: Success response
        C->>S: Animate card off-screen
        C->>T: Show success toast
        C->>U: onProcessed callback
    else Swipe cancelled
        G->>S: Snap back to center
        S->>C: Reset position
    end
```

### 2. Notification Loading e Deduplicazione

```mermaid
sequenceDiagram
    participant P as Page Load
    participant H as useNotifications
    participant C as useConversations
    participant D as useDeduplication
    participant U as UI

    P->>H: Fetch notifications
    P->>C: Fetch conversations
    H-->>P: notifications[]
    C-->>P: conversations[]
    
    P->>D: useNotificationDeduplication()
    D->>D: Filter booking duplicates
    D->>D: Separate by type
    D-->>P: {groupInvites, bookingNotifications, otherNotifications}
    
    P->>U: Render notification cards
    U->>U: Show swipeable group invites
    U->>U: Show booking conversations
    U->>U: Show other notifications
```

### 3. Tab Navigation e Bell Click

```mermaid
sequenceDiagram
    participant U as User
    participant H as Header
    participant R as Router
    participant C as Conversations Page
    participant S as State Management

    U->>H: Click bell icon
    H->>R: navigate('/conversations?tab=notifications')
    R->>C: Load page with URL params
    C->>S: setActiveTab('notifications')
    C->>C: Update sliding animation
    C->>U: Show notifications tab active
```

### 4. Tutorial Overlay Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Conversations
    participant O as SwipeHintOverlay
    participant L as localStorage

    C->>L: Check hasSeenSwipeHint
    alt First time user with group invites
        L-->>C: null (not seen)
        C->>O: setShowSwipeHint(true)
        O->>U: Show tutorial steps
        U->>O: Click "Ho Capito"
        O->>L: Set hasSeenSwipeHint = true
        O->>C: onDismiss()
        C->>O: setShowSwipeHint(false)
    else Returning user
        L-->>C: true (already seen)
        C->>U: Skip tutorial
    end
```

### 5. Real-time Notification Update

```mermaid
sequenceDiagram
    participant S as Supabase
    participant R as Real-time Channel
    participant Q as React Query
    participant C as Component
    participant U as UI

    S->>R: Database change event
    R->>Q: Invalidate notifications query
    Q->>S: Refetch notifications
    S-->>Q: Updated data
    Q->>C: Trigger re-render
    C->>U: Update notification count
    C->>U: Refresh notification list
```

## Ottimizzazioni Mobile

### Performance
- **Lazy loading** per grandi liste
- **Virtual scrolling** se necessario
- **Debounced gestures** per evitare azioni accidentali

### UX
- **Touch targets** di minimo 44px
- **Feedback immediato** su ogni interazione
- **Prevenzione scroll conflicts** con `touch-action: pan-y`

### Accessibilità
- **Screen reader support** con ARIA labels
- **High contrast mode** supportato
- **Pulsanti di fallback** per utenti senza touch

## Metriche e Analytics

### Eventi Tracciati
- `swipe_accept_invite`
- `swipe_decline_invite`
- `tap_notification`
- `tutorial_completed`
- `notification_processed`

### KPI da Monitorare
- Tasso di utilizzo gesture vs pulsanti
- Tempo medio per processare inviti
- Tasso di completamento tutorial
- Errori di gesture (false swipes)

## Roadmap Future

### Fase 2
- **Batch actions**: Selezione multipla
- **Custom gestures**: Configurabili dall'utente
- **Smart notifications**: ML per priorità
- **Push integration**: Notifiche native

### Fase 3
- **Voice commands**: "Accetta tutti gli inviti"
- **Gesture shortcuts**: Swipe patterns complessi
- **Social features**: Condivisione rapida
- **AR integration**: Notifiche spaziali

## Testing

### Device Testing
- **iOS**: Safari, Chrome, in-app browser
- **Android**: Chrome, Samsung Browser, Firefox
- **Touch precision**: Vari screen sizes
- **Performance**: Dispositivi low-end

### Scenari di Test
1. Swipe veloce vs lento
2. Swipe parziale (< soglia)
3. Interruzioni durante gesture
4. Rotazione schermo durante swipe
5. Multitasking durante animazioni

## Considerations per PWA

### Service Worker
- Cache delle animazioni CSS
- Offline fallback per gesture
- Background sync per azioni pending

### App Shell
- Preload componenti critici
- Lazy loading non-critici
- Resource hints per asset

### Installation
- Gesture tutorial nel onboarding
- Feature detection per haptics
- Graceful degradation per browser limitati

## Conclusioni

Il nuovo sistema di notifiche posiziona CatchUp all'avanguardia nel mobile UX, combinando:
- **Semplicità d'uso** con gesture intuitive
- **Performance ottimale** su tutti i dispositivi
- **Accessibilità** per tutti gli utenti
- **Scalabilità** per future funzionalità

L'implementazione segue i principi del Material Design 3 e Human Interface Guidelines, garantendo un'esperienza nativa su entrambe le piattaforme.


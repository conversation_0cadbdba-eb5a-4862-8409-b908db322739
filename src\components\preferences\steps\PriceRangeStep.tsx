import { motion } from "framer-motion";

interface PriceRange {
  id: string;
  name: string;
  description: string;
  selected: boolean;
}

interface PriceRangeStepProps {
  priceRanges: PriceRange[];
  onSelectPriceRange: (id: string) => void;
}

export const PriceRangeStep = ({ priceRanges, onSelectPriceRange }: PriceRangeStepProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -50 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">
          Preferenza fascia di prezzo
        </h1>
        <p className="text-gray-600">
          Seleziona la tua fascia di prezzo preferita per offerte personalizzate
        </p>
      </div>

      <div className="space-y-4">
        {priceRanges.map((range, index) => (
          <motion.div
            key={range.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.99 }}
            onClick={() => onSelectPriceRange(range.id)}
            className={`p-5 rounded-xl border-2 cursor-pointer transition-all ${
              range.selected
                ? "border-brand-primary bg-white shadow-lg"
                : "border-gray-200 bg-white hover:border-gray-300"
            }`}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h3
                  className={`font-semibold text-lg mb-1 ${
                    range.selected ? "text-brand-primary" : "text-gray-800"
                  }`}
                >
                  {range.name}
                </h3>
                <p className="text-gray-500 text-sm">{range.description}</p>
              </div>
              <div
                className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ml-4 ${
                  range.selected
                    ? "border-brand-primary bg-brand-primary"
                    : "border-gray-300"
                }`}
              >
                {range.selected && (
                  <motion.svg
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="w-3 h-3 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </motion.svg>
                )}
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};
import { useState, useEffect } from 'react';
import { Mail, Shield, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useGmailIntegration } from '@/hooks/auth/useGmailIntegration';
import { Skeleton } from '@/components/ui/skeleton';

export const GmailPermissionDialog = () => {
  const { 
    isConnected, 
    isConnecting, 
    isLoading,
    connectGmail,
    disconnectGmail,
    integration 
  } = useGmailIntegration();
  
  const [showPermissions, setShowPermissions] = useState(false);

  // Handle OAuth callback on component mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const authParam = urlParams.get('auth');
    
    if (authParam === 'callback') {
      // This will be handled by the parent component
      setShowPermissions(false);
    }
  }, []);

  if (isLoading) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Mail className="h-6 w-6" />
            <CardTitle>Gmail</CardTitle>
          </div>
          <CardDescription>Caricamento stato connessione...</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-10 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (isConnected && integration) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <div className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-500" />
            <CardTitle>Gmail Connesso</CardTitle>
          </div>
          <CardDescription>
            Il tuo account Gmail è connesso e funzionante
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              Account: {integration.metadata?.email || 'Email non disponibile'}
            </AlertDescription>
          </Alert>
          
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Permessi attivi:</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Lettura email</li>
              <li>• Invio email</li>
              <li>• Gestione allegati</li>
            </ul>
          </div>

          <Button 
            variant="destructive" 
            onClick={disconnectGmail}
            className="w-full"
          >
            Disconnetti Gmail
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Mail className="h-6 w-6" />
          <CardTitle>Connetti Gmail</CardTitle>
        </div>
        <CardDescription>
          Connetti il tuo account Gmail per accedere alle tue email
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!showPermissions ? (
          <>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Per utilizzare il client email, devi connettere il tuo account Gmail.
              </AlertDescription>
            </Alert>

            <Button 
              onClick={() => setShowPermissions(true)}
              className="w-full"
              variant="outline"
            >
              Visualizza permessi richiesti
            </Button>

            <Button 
              onClick={connectGmail}
              disabled={isConnecting}
              className="w-full"
            >
              {isConnecting ? 'Connessione in corso...' : 'Connetti Gmail'}
            </Button>
          </>
        ) : (
          <>
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Permessi richiesti:</h4>
              
              <div className="space-y-2">
                <div className="flex items-start gap-2 text-sm">
                  <Shield className="h-4 w-4 mt-0.5 text-blue-500" />
                  <div>
                    <div className="font-medium">Lettura email</div>
                    <div className="text-muted-foreground text-xs">
                      Accesso alle tue email per visualizzarle nell&apos;app
                    </div>
                  </div>
                </div>

                <div className="flex items-start gap-2 text-sm">
                  <Shield className="h-4 w-4 mt-0.5 text-blue-500" />
                  <div>
                    <div className="font-medium">Invio email</div>
                    <div className="text-muted-foreground text-xs">
                      Possibilità di inviare email dal tuo account
                    </div>
                  </div>
                </div>

                <div className="flex items-start gap-2 text-sm">
                  <Shield className="h-4 w-4 mt-0.5 text-blue-500" />
                  <div>
                    <div className="font-medium">Gestione allegati</div>
                    <div className="text-muted-foreground text-xs">
                      Visualizzazione e download degli allegati
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription className="text-xs">
                I tuoi dati sono protetti e utilizzati solo per fornire il servizio email. 
                Non condivideremo mai le tue informazioni con terze parti.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <Button 
                onClick={connectGmail}
                disabled={isConnecting}
                className="w-full"
              >
                {isConnecting ? 'Connessione in corso...' : 'Autorizza e Connetti'}
              </Button>

              <Button 
                variant="ghost"
                onClick={() => setShowPermissions(false)}
                className="w-full"
              >
                Indietro
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};
class OpenAIService {
  // OpenAI API Key (hardcoded for development)
  private OPENAI_API_KEY = "********************************************************************************************************************************************************************";
  
  // WebRTC references
  private peerConnection: RTCPeerConnection | null = null;
  private dataChannel: RTCDataChannel | null = null;
  private audioElement: HTMLAudioElement | null = null;
  private mediaStream: MediaStream | null = null;

  // Event callbacks
  private onTranscriptionCallback: ((text: string) => void) | null = null;
  private onUserTranscriptionCallback: ((text: string) => void) | null = null;
  private onAIResponseCallback: ((text: string) => void) | null = null;
  private onStatusChangeCallback: ((status: string) => void) | null = null;
  private onErrorCallback: ((error: string) => void) | null = null;

  // Accumulate transcript deltas
  private currentTranscript: string = '';
  private currentUserTranscript: string = '';

  // Initialize WebRTC connection
  public async initializeConnection(model: string): Promise<void> {
    try {
      console.log('Initializing WebRTC connection to OpenAI');

      // Reset transcript accumulation for new connection
      this.currentTranscript = '';
      this.currentUserTranscript = '';

      // Get ephemeral token
      const ephemeralKey = await this.getEphemeralKey();
      
      // Create peer connection
      this.peerConnection = new RTCPeerConnection();
      
      // Set up audio element for playback
      this.audioElement = document.createElement('audio');
      this.audioElement.autoplay = true;
      
      // Handle incoming audio stream
      this.peerConnection.ontrack = (event) => {
        if (this.audioElement) {
          this.audioElement.srcObject = event.streams[0];
        }
      };

      // Get microphone access
      this.mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      this.peerConnection.addTrack(this.mediaStream.getTracks()[0]);

      // Set up data channel
      this.dataChannel = this.peerConnection.createDataChannel('oai-events');
      this.setupDataChannelHandlers();

      // Create and set local description
      const offer = await this.peerConnection.createOffer();
      await this.peerConnection.setLocalDescription(offer);

      // Send offer to OpenAI and get answer
      const baseUrl = 'https://api.openai.com/v1/realtime';
      
      const sdpResponse = await fetch(`${baseUrl}?model=${model}`, {
        method: 'POST',
        body: offer.sdp,
        headers: {
          'Authorization': `Bearer ${ephemeralKey}`,
          'Content-Type': 'application/sdp',
          'OpenAI-Beta': 'realtime=v1'
        },
      });

      if (!sdpResponse.ok) {
        throw new Error(`Failed to get SDP answer: ${sdpResponse.statusText}`);
      }

      const answer: RTCSessionDescriptionInit = {
        type: 'answer' as RTCSdpType,
        sdp: await sdpResponse.text(),
      };

      await this.peerConnection.setRemoteDescription(answer);

      if (this.onStatusChangeCallback) {
        this.onStatusChangeCallback('Connected');
      }

    } catch (error) {
      console.error('Error initializing WebRTC:', error);
      if (this.onErrorCallback) {
        this.onErrorCallback(error instanceof Error ? error.message : 'Failed to initialize connection');
      }
      if (this.onStatusChangeCallback) {
        this.onStatusChangeCallback('Connection Error');
      }
      throw error;
    }
  }

  private async getEphemeralKey(): Promise<string> {
    try {
      const response = await fetch('https://api.openai.com/v1/realtime/sessions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.OPENAI_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o-realtime-preview-2024-12-17',
          voice: 'verse',
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to get ephemeral key: ${response.statusText}`);
      }

      const data = await response.json();
      return data.client_secret.value;
    } catch (error) {
      console.error('Error getting ephemeral key:', error);
      throw error;
    }
  }

  private setupDataChannelHandlers(): void {
    if (!this.dataChannel) return;

    this.dataChannel.onopen = () => {
      console.log('Data channel opened');
      
      // Configure session to enable input audio transcription
      if (this.dataChannel) {
        this.dataChannel.send(JSON.stringify({
          type: 'session.update',
          session: {
            input_audio_transcription: {
              model: 'whisper-1'
            }
          }
        }));
        console.log('Sent session configuration to enable input audio transcription');
      }
    };

    this.dataChannel.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        console.log('Received message:', message);

        // Handle different message types from OpenAI Realtime API
        if (message.type === 'error') {
          // Handle error messages
          console.error('API error:', message);
          if (this.onErrorCallback) {
            const errorMessage = message.error?.message || message.message || 'Unknown error occurred';
            this.onErrorCallback(errorMessage);
          }
        } 
        // Handle real-time transcription deltas
        else if (message.type === 'response.audio_transcript.delta') {
          if (message.delta && this.onTranscriptionCallback) {
            console.log('Processing AI transcript delta:', message.delta);
            // Accumulate the delta text
            this.currentTranscript += message.delta;
            this.onTranscriptionCallback(this.currentTranscript);
          }
        }
        // Handle user input transcription completed
        else if (message.type === 'conversation.item.input_audio_transcription.completed') {
          if (message.transcript && this.onUserTranscriptionCallback) {
            console.log('User transcript completed:', message.transcript);
            this.onUserTranscriptionCallback(message.transcript);
          }
        }
        // Handle input audio buffer transcription
        else if (message.type === 'input_audio_buffer.transcription.completed') {
          if (message.transcript && this.onUserTranscriptionCallback) {
            console.log('User audio buffer transcript:', message.transcript);
            this.onUserTranscriptionCallback(message.transcript);
          }
        }
        // Handle transcription updates
        else if (message.type === 'conversation.item.created' && 
                message.item?.type === 'message' && 
                message.item?.role === 'user') {
          if (message.item.content && this.onTranscriptionCallback) {
            // Find text content in the content array
            const textContent = message.item.content.find(c => c.type === 'input_text');
            if (textContent && textContent.text) {
              this.onTranscriptionCallback(textContent.text);
            }
          }
        }
        // Handle AI text responses
        else if (message.type === 'response.text.delta') {
          if (message.delta && this.onAIResponseCallback) {
            this.onAIResponseCallback(message.delta.text || '');
          }
        }
        // Handle status updates
        else if (message.type === 'session.created' || message.type === 'session.updated') {
          if (this.onStatusChangeCallback) {
            this.onStatusChangeCallback('Connected');
          }
        }
        else if (message.type === 'input_audio_buffer.speech_started') {
          if (this.onStatusChangeCallback) {
            this.onStatusChangeCallback('Listening...');
          }
          // Reset transcript for new user speech
          this.currentTranscript = '';
          this.currentUserTranscript = '';
        }
        else if (message.type === 'input_audio_buffer.speech_stopped') {
          if (this.onStatusChangeCallback) {
            this.onStatusChangeCallback('Processing...');
          }
        }
        else if (message.type === 'response.done') {
          if (this.onStatusChangeCallback) {
            this.onStatusChangeCallback('Ready');
          }
          // Reset transcript accumulation when response is complete
          this.currentTranscript = '';
          this.currentUserTranscript = '';
        }
      } catch (error) {
        console.error('Error handling data channel message:', error);
      }
    };

    this.dataChannel.onerror = (error) => {
      console.error('Data channel error:', error);
      if (this.onErrorCallback) {
        this.onErrorCallback('Data channel error');
      }
    };

    this.dataChannel.onclose = () => {
      console.log('Data channel closed');
      if (this.onStatusChangeCallback) {
        this.onStatusChangeCallback('Disconnected');
      }
    };
  }

  // Close connection and cleanup
  public closeConnection(): void {
    // Reset transcript accumulation
    this.currentTranscript = '';
    this.currentUserTranscript = '';
    
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }

    if (this.dataChannel) {
      this.dataChannel.close();
      this.dataChannel = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    if (this.audioElement) {
      this.audioElement.srcObject = null;
      this.audioElement = null;
    }
  }

  // Set event callbacks
  public setTranscriptionCallback(callback: (text: string) => void): void {
    this.onTranscriptionCallback = callback;
  }

  public setUserTranscriptionCallback(callback: (text: string) => void): void {
    this.onUserTranscriptionCallback = callback;
  }

  public setAIResponseCallback(callback: (text: string) => void): void {
    this.onAIResponseCallback = callback;
  }

  public setStatusChangeCallback(callback: (status: string) => void): void {
    this.onStatusChangeCallback = callback;
  }

  public setErrorCallback(callback: (error: string) => void): void {
    this.onErrorCallback = callback;
  }

  // Add these methods to the OpenAIService class

  /**
   * Check if the data channel is ready
   */
  public isDataChannelReady(): boolean {
    return !!this.dataChannel && this.dataChannel.readyState === 'open';
  }

  /**
   * Wait for the data channel to be open
   */
  public waitForDataChannel(maxWaitMs: number = 5000): Promise<boolean> {
    return new Promise((resolve) => {
      if (this.isDataChannelReady()) {
        console.log('Data channel already open');
        return resolve(true);
      }

      console.log('Waiting for data channel to open...');
      let waitTime = 0;
      const interval = 100; // check every 100ms
      
      const checkInterval = setInterval(() => {
        waitTime += interval;
        
        if (this.isDataChannelReady()) {
          console.log('Data channel opened after waiting');
          clearInterval(checkInterval);
          resolve(true);
        } else if (waitTime >= maxWaitMs) {
          console.warn(`Data channel not open after ${maxWaitMs}ms`, 
            this.dataChannel ? `State: ${this.dataChannel.readyState}` : 'No data channel');
          clearInterval(checkInterval);
          resolve(false);
        }
      }, interval);
    });
  }

  /**
   * Initiate conversation when user is silent
   */
  public initiateConversation(): boolean {
    if (this.isDataChannelReady()) {
      try {
        console.log('OpenAIService: Sending prompt to initiate conversation');
        
        // First create a conversation item with the user's message
        this.dataChannel!.send(JSON.stringify({
          type: 'conversation.item.create',
          item: {
            type: 'message',
            role: 'user',
            content: [{
              type: 'input_text',
              text: 'Ciao'
            }]
          }
        }));

        // Then create a response
        this.dataChannel!.send(JSON.stringify({
          type: 'response.create',
          response: {
            modalities: ['text', 'audio']
          }
        }));
        
        return true;
      } catch (error) {
        console.error('Error initiating conversation:', error);
        return false;
      }
    } else {
      console.warn('Cannot initiate conversation: Data channel not open', 
        this.dataChannel ? `State: ${this.dataChannel.readyState}` : 'No data channel');
      return false;
    }
  }
}

export default new OpenAIService();

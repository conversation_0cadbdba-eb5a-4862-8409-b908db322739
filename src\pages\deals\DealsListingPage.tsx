
import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import DealCard from '@/components/deals/core/DealCard';
import { Deal, DealFromDB } from '@/types/deals';
import { toast } from 'sonner';

/**
 * Page component for displaying a list of deals
 */
export function DealsListingPage() {
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    async function fetchDeals() {
      try {
        const { data, error } = await supabase
          .from('deals')
          .select(`
            *,
            businesses (
              name,
              address,
              city,
              zip_code,
              state,
              country
            )
          `)
          .order('created_at', { ascending: false });
        
        if (error) {
          throw error;
        }
        
        if (data) {
          // Trasforma i dati dal DB al formato dell'interfaccia Deal
          const transformedDeals: Deal[] = data.map((deal: DealFromDB) => ({
            id: deal.id,
            title: deal.title,
            description: deal.description || '',
            business_id: deal.business_id,
            discounted_price: deal.discounted_price,
            original_price: deal.original_price,
            discount_percentage: deal.discount_percentage,
            start_date: deal.start_date,
            end_date: deal.end_date,
            time_slots: deal.time_slots,
            status: deal.status,
            created_at: deal.created_at,
            updated_at: deal.updated_at,
            images: deal.images,
            auto_confirm: deal.auto_confirm,
            category_id: deal.category_id,
            fake: deal.fake,
            terms_conditions: deal.terms_conditions,
            // Campi aggiunti per compatibilità
            price: deal.discounted_price,
            image_url: deal.images && deal.images.length > 0 ? deal.images[0] : '/placeholder.svg',
            business_name: deal.businesses?.name || 'Business',
            capacity: 10, // Valore di default
            businesses: deal.businesses
          }));
          
          setDeals(transformedDeals);
        }
      } catch (err) {
        console.error('Error fetching deals:', err);
        setError('Impossibile caricare le offerte. Riprova più tardi.');
      } finally {
        setLoading(false);
      }
    }
    
    fetchDeals();
  }, []);
  
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Offerte in evidenza</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="w-full h-48 bg-gray-200 animate-pulse"></div>
              <div className="p-4">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-3 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4 animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 text-red-800 p-4 rounded-md">
          {error}
        </div>
      </div>
    );
  }
  
  if (deals.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Offerte in evidenza</h1>
        <div className="bg-gray-50 p-8 text-center rounded-lg">
          <h2 className="text-xl font-medium text-gray-600">Nessuna offerta disponibile al momento</h2>
          <p className="text-gray-500 mt-2">Controlla di nuovo più tardi per nuove offerte!</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Offerte in evidenza</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {deals.map(deal => (
          <DealCard key={deal.id} deal={deal} variant="compact" onClick={() => {}} showVisitBusiness={true} />
        ))}
      </div>
    </div>
  );
}

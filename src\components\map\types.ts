
/**
 * Represents a business entity with location and deal information
 * @interface Business
 */
export interface Business {
  id: string;
  name: string;
  latitude: number | null;
  longitude: number | null;
  deal_count: number | null;
  deals?: any[];
  photos: string[] | null;
}

/**
 * Props for the MapView2 component
 * @interface MapViewProps
 * @property {Business[]} businesses - Array of businesses to display on the map
 * @property {Function} [onBusinessClick] - Optional callback when a business is clicked
 * @property {Function} [onDealClick] - Optional callback when a deal is clicked
 * @property {boolean} [locationEnabled] - Whether to use user location (defaults to true)
 */
export interface MapViewProps {
  businesses: Business[];
  onBusinessClick?: (business: Business) => void;
  onDealClick?: (dealId: string) => void;
  locationEnabled?: boolean;
}

/**
 * Possible transportation modes for directions
 * @type TransportMode
 */
export type TransportMode = "DRIVING" | "WALKING" | "TRANSIT" | "BICYCLING";

/**
 * Navigation state for directions
 * @interface NavigationState
 */
export interface NavigationState {
  isActive: boolean;
  mode: TransportMode;
  directions: google.maps.DirectionsResult | null;
}

export const DEFAULT_ZOOM = 13;
export const DEFAULT_DRAG_DELAY = 500;

/**
 * Map styling options
 */
export const mapOptions = {
  styles: [
    {
      featureType: "poi",
      elementType: "labels",
      stylers: [{ visibility: "off" }],
    },
  ],
};

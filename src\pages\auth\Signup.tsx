
import { motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useRef, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAvatarUpload } from "@/hooks/useAvatarUpload";
import { AvatarUpload } from "@/components/auth/AvatarUpload";
import { SignupForm } from "@/components/auth/SignupForm";

const Signup = () => {
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const {
    avatarPreview,
    isUploadingAvatar,
    handleFileChange,
    uploadAvatar
  } = useAvatarUpload();

  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();

    if (password !== confirmPassword) {
      toast.error("Le password non corrispondono");
      return;
    }

    if (!acceptedTerms) {
      toast.error("Devi accettare i termini e le condizioni");
      return;
    }

    setIsLoading(true);

    try {
      // Sign up the user - this will trigger the database function to create the user_details record
      const { data: { user }, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
            display_name: displayName,
            phone_number: phoneNumber || null,
            role: "user",
          },
        },
      });

      if (error) {
        console.error('Errore durante la registrazione:', error);
        toast.error(error.message);
        return;
      }

      if (!user) {
        toast.error("Nessun utente creato. Riprova più tardi.");
        return;
      }

      // Upload avatar if available
      let avatarUrl = null;
      try {
        avatarUrl = await uploadAvatar(user.id);
      } catch (avatarError) {
        console.error('Errore durante il caricamento dell\'avatar:', avatarError);
        // Continue with registration even if avatar upload fails
      }

      // Attendiamo un momento per assicurarci che il record user_details sia stato creato
      await new Promise(resolve => setTimeout(resolve, 500));

      // Aggiorniamo i dettagli dell'utente includendo la email
      const { error: updateError } = await supabase
        .from('user_details')
        .update({
          avatar_url: avatarUrl
       
        })
        .eq('id', user.id);

      if (updateError) {
        console.error('Errore durante l\'aggiornamento dei dettagli utente:', updateError);
        toast.error("Registrazione completata, ma c'è stato un problema nell'aggiornamento dei dettagli.");
      }

   //   toast.success("Registrazione effettuata con successo!");

      // Redirect to personalization page instead of login
      navigate("/personalize-preferences");
    } catch (error) {
      console.error('Errore non gestito durante la registrazione:', error);
      toast.error("Si è verificato un errore durante la registrazione");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 max-w-md mx-auto">
      <header className="flex items-center justify-between mb-8 pt-4">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => navigate(-1)}
          className="text-gray-800"
        >
          <ArrowLeft className="h-6 w-6" />
        </motion.button>
        <div className="flex items-center gap-2">
          <div className="w-2.5 h-2.5 rounded-full bg-brand-primary"></div>
          <div className="w-2.5 h-2.5 rounded-full bg-gray-300"></div>
          <div className="w-2.5 h-2.5 rounded-full bg-gray-300"></div>
        </div>
      </header>

      <main className="space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Crea il tuo account</h1>
          <p className="text-gray-600">Unisciti a CatchUp per scoprire offerte locali</p>
        </motion.div>

        <AvatarUpload
          fileInputRef={fileInputRef}
          avatarPreview={avatarPreview}
          isUploadingAvatar={isUploadingAvatar}
          onAvatarClick={handleAvatarClick}
          onFileChange={handleFileChange}
        />

        <SignupForm
          firstName={firstName}
          lastName={lastName}
          displayName={displayName}
          phoneNumber={phoneNumber}
          email={email}
          password={password}
          confirmPassword={confirmPassword}
          acceptedTerms={acceptedTerms}
          isLoading={isLoading}
          onFirstNameChange={(e) => setFirstName(e.target.value)}
          onLastNameChange={(e) => setLastName(e.target.value)}
          onDisplayNameChange={(e) => setDisplayName(e.target.value)}
          onPhoneNumberChange={(e) => setPhoneNumber(e.target.value)}
          onEmailChange={(e) => setEmail(e.target.value)}
          onPasswordChange={(e) => setPassword(e.target.value)}
          onConfirmPasswordChange={(e) => setConfirmPassword(e.target.value)}
          onTermsChange={(e) => setAcceptedTerms(e.target.checked)}
          onSubmit={handleSignup}
        />
      </main>

      <footer className="mt-8">
        <p className="text-center text-gray-600">
          Hai già un account?{" "}
          <button onClick={() => navigate("/login")} className="text-brand-primary font-medium">
            Accedi
          </button>
        </p>
      </footer>
    </div>
  );
};

export default Signup;

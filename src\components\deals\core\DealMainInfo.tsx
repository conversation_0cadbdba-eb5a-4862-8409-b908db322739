
import { Star, MapPin } from "lucide-react";

interface DealMainInfoProps {
  title: string;
  description: string;
}

const DealMainInfo = ({ title, description }: DealMainInfoProps) => {
  return (
    <div className="px-4 py-6 bg-white">
      <h1 className="text-2xl font-bold mb-2">{title}</h1>
      <div className="flex items-center mb-4">
        <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
        <span className="ml-1 text-sm">4.8 (128 recensioni)</span>
        <span className="mx-2">•</span>
        <MapPin className="h-4 w-4 text-gray-400" />
        <span className="ml-1 text-sm">x.x km (to be done DealMainInfo.tsx from DealDetails.tsx)</span>
      </div>
      <p className="text-gray-600 mb-6">{description}</p>
    </div>
  );
};

export default DealMainInfo;

# VoiceDialog Component Specification

## Overview

`VoiceDialog` is a modal voice interaction component for the CatchUp PWA that enables real-time voice conversations between users and AI assistants. It provides an intuitive, mobile-first interface with visual feedback, drag-to-dismiss functionality, and seamless integration with 11Labs voice technology.

## Features

### Core Functionality
- **Real-time Voice Conversation**: Bidirectional voice communication with AI assistants
- **Visual State Feedback**: Animated indicators for speaking, listening, and connection status
- **Drag-to-Dismiss**: Mobile-friendly gesture to close the dialog
- **Authentication Integration**: Requires user login with graceful fallback
- **Assistant Personalization**: Displays selected assistant's name and avatar
- **Location-Aware**: Optionally shares user location with assistant for contextual responses

### User Experience
- **Mobile-First Design**: Optimized for mobile touch interactions
- **Smooth Animations**: Framer Motion animations for enhanced UX
- **Visual Feedback**: Pulsing animations during active conversation
- **Error Handling**: Clear error messages and recovery flows
- **Accessibility**: Proper ARIA labels and keyboard navigation support

## Component Interface

### Props
# Voice Interaction Sequence Diagram

```mermaid
sequenceDiagram
    participant U as User
    participant VD as VoiceDialog
    participant Auth as useAuth
    participant AD as useAssistantDetails
    participant VC as useVoiceConversation
    participant API as 11Labs API
    participant SB as Supabase
    participant WH as n8n Webhook

    Note over U,WH: Voice Dialog Initialization
    U->>VD: Opens voice dialog (isOpen=true)
    VD->>Auth: Check user authentication
    
    alt User not authenticated
        Auth-->>VD: No user data
        VD->>U: Show login alert
        U->>VD: Close dialog
    else User authenticated
        Auth-->>VD: User details & selected_assistant_id
        VD->>AD: Fetch assistant details
        AD->>SB: Query ai_assistant_profile
        SB-->>AD: Assistant data (name, voice_id, image)
        AD-->>VD: Assistant details
        VD->>U: Display dialog with assistant info
    end

    Note over U,WH: Voice Conversation Flow
    U->>VD: Tap microphone button
    VD->>VC: startConversation()
    
    VC->>VC: Request microphone permission
    alt Permission denied
        VC-->>VD: Error: Microphone access denied
        VD->>U: Display error message
    else Permission granted
        VC->>API: Start session with agentId
        Note over VC,API: Session configuration:<br/>- agentId: jmxsviJHHXsRhmtInl9X<br/>- user_name, assistant_name<br/>- user_location (if enabled)<br/>- voice_id override
        
        API-->>VC: Session started
        VC-->>VD: Update status: "connecting" -> "connected"
        VD->>U: Show "connected" state (red button, animations)
        
        loop Active Conversation
            U->>API: Speak to microphone
            API->>WH: Process voice input via webhook
            Note over WH: Webhook URL:<br/>https://genenrativepangea.app.n8n.cloud/webhook/catchup/
            WH-->>API: Generated response
            API->>VC: Stream transcript updates
            VC-->>VD: Update transcript state
            VD->>U: Display live transcript
            API->>U: Play AI voice response
            VC-->>VD: Update isSpeaking state
            VD->>U: Show speaking animation
        end

        Note over U,WH: Conversation End
        U->>VD: Tap microphone (end conversation)
        VD->>VC: endConversation()
        VC->>API: End session
        API-->>VC: Session ended
        VC-->>VD: Reset state (transcript, error)
        VD->>U: Return to initial state
    end

    Note over U,WH: Dialog Dismissal
    alt Drag to dismiss
        U->>VD: Drag down > 100px
        VD->>VD: Trigger handleClose()
    else Button close
        U->>VD: Tap X button
        VD->>VD: Trigger handleClose()
    end
    
    VD->>VC: endConversation() (if active)
    VD->>VD: onClose() callback
    VD->>U: Dialog hidden (isOpen=false)
```

## Usage Examples

### Basic Implementation
```typescript
import VoiceDialog from '@/components/voice/VoiceDialog';

const App = () => {
  const [isVoiceOpen, setIsVoiceOpen] = useState(false);

  return (
    <div>
      <button onClick={() => setIsVoiceOpen(true)}>
        Start Voice Chat
      </button>
      
      <VoiceDialog
        isOpen={isVoiceOpen}
        onClose={() => setIsVoiceOpen(false)}
      />
    </div>
  );
};
```

### Integration with Navigation
```typescript
// In a navigation component or main layout
const Navigation = () => {
  const [showVoiceDialog, setShowVoiceDialog] = useState(false);

  return (
    <>
      <nav>
        <button 
          className="voice-trigger"
          onClick={() => setShowVoiceDialog(true)}
        >
          <Mic className="w-5 h-5" />
        </button>
      </nav>

      <VoiceDialog
        isOpen={showVoiceDialog}
        onClose={() => setShowVoiceDialog(false)}
      />
    </>
  );
};
```

## Technical Implementation Details

### Animation System
- **Entry/Exit**: Slide up from bottom with spring animation
- **Drag Interaction**: Drag threshold of 100px to trigger close
- **Visual Feedback**: Pulsing rings during active conversation
- **Speaking Animation**: Scale animation when AI is speaking

### State Management
```typescript
// Internal state management
const [showLoginAlert, setShowLoginAlert] = useState(false);

// Hook-based state from useVoiceConversation
const {
  transcript,        // Live speech-to-text
  error,            // Error messages
  isConversationStarted,
  conversation,     // 11Labs conversation object
  startConversation,
  endConversation
} = useVoiceConversation(userDetails?.first_name, assistantDetails);
```

### 11Labs Integration
- **Agent ID**: `jmxsviJHHXsRhmtInl9X`
- **Webhook URL**: `https://genenrativepangea.app.n8n.cloud/webhook/catchup/`
- **Voice Customization**: Uses assistant's voice_id or fallback
- **Dynamic Variables**: Passes user context (name, location, assistant details)

### Supabase Integration
- **Assistant Profiles**: Fetches from `ai_assistant_profile` table
- **User Authentication**: Integrates with useAuth hook
- **Data Fields**: `id`, `voice_id`, `name`, `image_url`

## Error Handling

### Authentication Errors
- **No User**: Shows login alert dialog
- **Invalid Session**: Graceful fallback to login prompt

### Voice Errors
- **Microphone Permission**: Clear error message with instructions
- **Connection Issues**: Network error handling with retry capability
- **API Errors**: User-friendly error messages in Italian

### Graceful Degradation
- **No Assistant Selected**: Uses default assistant name and voice
- **Missing Avatar**: Falls back to initial letter display
- **Network Issues**: Maintains UI state with appropriate error messaging

## Accessibility Features

### Keyboard Navigation
- **Focus Management**: Proper tab order and focus trapping
- **Keyboard Shortcuts**: Escape key to close dialog
- **Screen Reader**: ARIA labels for all interactive elements

### Mobile Accessibility
- **Touch Targets**: Minimum 44x44px touch targets
- **Gesture Support**: Drag-to-dismiss for motor accessibility
- **Visual Indicators**: High contrast state indicators

### Internationalization
- **Text Labels**: Currently in Italian, ready for i18n
- **RTL Support**: CSS structure supports right-to-left layouts
- **Cultural Adaptation**: Voice interaction patterns can be localized

## Security Considerations

### Data Privacy
- **Voice Data**: Processed by 11Labs with appropriate privacy controls
- **User Location**: Optional sharing with explicit user consent
- **Authentication**: Secure user session management

### API Security
- **Environment Variables**: API keys stored securely
- **Rate Limiting**: 11Labs API has built-in rate limiting
- **Webhook Security**: n8n webhook endpoint security considerations

## Performance Optimizations

### Lazy Loading
- **Component Loading**: Only loads when `isOpen` is true
- **API Calls**: Assistant details fetched only when needed
- **Resource Cleanup**: Properly cleans up voice streams on unmount

### Memory Management
- **State Reset**: Clears transcript and error states on session end
- **Event Listeners**: Proper cleanup of voice event listeners
- **Animation Cleanup**: Framer Motion handles animation memory management

## Integration Points

### CatchUp App Ecosystem
- **Authentication System**: Integrates with app-wide auth state
- **User Preferences**: Respects selected assistant preferences
- **Location Services**: Optional location sharing for context
- **UI Theme**: Follows app design system and theme

### External Services
- **11Labs**: Voice conversation platform
- **Supabase**: Database and authentication
- **n8n**: Webhook processing for AI responses
- **Framer Motion**: Animation library

## Future Enhancements

### Planned Features
- **Multi-language Support**: Expand beyond Italian
- **Voice Commands**: Quick actions via voice
- **Conversation History**: Persistent conversation logs
- **Offline Mode**: Basic functionality without internet

### AI Improvements
- **Context Awareness**: Better conversation context management
- **Personalization**: Learning user preferences over time
- **Integration**: Deeper integration with CatchUp business logic
- **Analytics**: Voice interaction analytics and insights

## Development Notes

### Testing Strategy
- **Unit Tests**: Component behavior and state management
- **Integration Tests**: Voice API integration testing
- **E2E Tests**: Complete user interaction flows
- **Accessibility Tests**: Screen reader and keyboard navigation

### Debugging
- **Console Logging**: Comprehensive error logging for development
- **State Inspection**: React DevTools integration
- **Network Monitoring**: API call monitoring and debugging
- **Voice Debugging**: 11Labs conversation state inspection

---

*This specification covers the VoiceDialog component as implemented in the CatchUp PWA. For questions or contributions, refer to the main project documentation.*
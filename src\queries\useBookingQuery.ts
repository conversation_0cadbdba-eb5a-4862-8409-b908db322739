
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useBookingQuery = (userId: string | undefined, options = {}) => {
  return useQuery({
    queryKey: ['bookings', userId],
    queryFn: async () => {
      if (!userId) return [];
      
      const { data, error } = await supabase
        .from('bookings')
        .select('*')
        .eq('user_id', userId);
      
      if (error) {
        throw new Error(`Errore nel recupero delle prenotazioni: ${error.message}`);
      }
      
      return data || [];
    },
    enabled: !!userId,
    ...options
  });
};

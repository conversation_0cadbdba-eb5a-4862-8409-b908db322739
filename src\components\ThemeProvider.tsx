import React, { useEffect } from 'react';
import { useABTest } from '../contexts/ABTestContext';
import { generateCSSVariables, getPalette } from '../styles/colorPalettes';

interface ThemeProviderProps {
  children: React.ReactNode;
}

const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { variants } = useABTest();

  useEffect(() => {
    // Get the palette based on the variant
    const palette = getPalette(variants.colorPalette);

    // Generate CSS variables
    const cssVariables = generateCSSVariables(palette);

    // Apply CSS variables to the document root
    const root = document.documentElement;
    root.setAttribute('style', cssVariables);

    // Update theme-color meta tag for browser UI
    const themeColorMeta = document.querySelector('meta[name="theme-color"]');
    if (themeColorMeta) {
      themeColorMeta.setAttribute('content', palette.brandPrimary);
    }

    // No need to update the update-banner directly anymore
    // It now uses CSS variables that are automatically updated

  }, [variants.colorPalette]);

  return <>{children}</>;
};

export default ThemeProvider;

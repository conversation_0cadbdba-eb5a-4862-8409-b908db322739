import { supabase } from '@/integrations/supabase/client';
import { SubscriptionPlanDetails } from '@/types/subscription';

export interface PricingTier {
  trial_end_date?: string;
  tier_type: string;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: any;
  max_businesses: number;
  max_agents?: number;
  allowed_agents?: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
  is_yearly?: boolean;
  id?: string;
}

export class SubscriptionService {
  /**
   * Fetch all active pricing tiers from the database
   */
  static async fetchPricingTiers(): Promise<PricingTier[]> {
    try {
      const { data, error } = await supabase
        .from('pricing_tiers')
        .select('*')
        .eq('is_active', true)
        .order('price_monthly', { ascending: true });

      if (error) {
        console.error('Error fetching pricing tiers:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch pricing tiers:', error);
      throw error;
    }
  }

  /**
   * Convert database PricingTier to SubscriptionPlanDetails
   */
  static convertToSubscriptionPlanDetails(pricingTier: PricingTier): SubscriptionPlanDetails {
    return {
      id: pricingTier.tier_type as any, // Cast to SubscriptionPlan type
      name: pricingTier.name,
      description: pricingTier.description,
      price_monthly: pricingTier.price_monthly,
      price_yearly: pricingTier.price_yearly,
      features: Array.isArray(pricingTier.features) ? pricingTier.features : [],
      max_businesses: pricingTier.max_businesses,
      isActive: pricingTier.is_active,
      trial_end_date: pricingTier.trial_end_date,
      createdAt: pricingTier.created_at,
      updatedAt: pricingTier.updated_at,
    };
  }

  /**
   * Get subscription plans as a Record<SubscriptionPlan, SubscriptionPlanDetails>
   */
  static async getSubscriptionPlans(): Promise<Record<string, SubscriptionPlanDetails>> {
    try {
      const pricingTiers = await this.fetchPricingTiers();
      
      const subscriptionPlans: Record<string, SubscriptionPlanDetails> = {};
      
      pricingTiers.forEach(tier => {
        subscriptionPlans[tier.tier_type] = this.convertToSubscriptionPlanDetails(tier);
      });

      return subscriptionPlans;
    } catch (error) {
      console.error('Failed to get subscription plans:', error);
      throw error;
    }
  }

  /**
   * Get a specific subscription plan by ID
   */
  static async getSubscriptionPlan(planId: string): Promise<SubscriptionPlanDetails | null> {
    try {
      const { data, error } = await supabase
        .from('pricing_tiers')
        .select('*')
        .eq('tier_type', planId as any)
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('Error fetching subscription plan:', error);
        return null;
      }

      return data ? this.convertToSubscriptionPlanDetails(data as PricingTier) : null;
    } catch (error) {
      console.error('Failed to fetch subscription plan:', error);
      return null;
    }
  }
} 

const BusinessDashboardSkeleton = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="h-16 bg-gray-200 animate-pulse" />
      <div className="p-4 space-y-4">
        <div className="aspect-video bg-gray-200 rounded-xl animate-pulse" />
        <div className="h-64 bg-gray-200 rounded-xl animate-pulse" />
        <div className="grid grid-cols-2 gap-4">
          {[1, 2, 3, 4].map((item) => (
            <div key={item} className="h-32 bg-gray-200 rounded-xl animate-pulse" />
          ))}
        </div>
        <div className="h-14 bg-gray-200 rounded-xl animate-pulse" />
      </div>
    </div>
  );
};

export default BusinessDashboardSkeleton;

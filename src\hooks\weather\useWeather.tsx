import { useState, useEffect } from 'react';
import { useLocation } from '@/contexts/LocationContext';
import { weatherService, WeatherData, WeatherAlert } from '@/services/weatherService';
import { toast } from 'sonner';

export const useWeather = () => {
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [alerts, setAlerts] = useState<WeatherAlert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const locationState = useLocation();
  const userLocation = locationState.coordinates;
  const locationLoading = locationState.isLoading;

  const fetchWeatherData = async (lat: number, lng: number) => {
    try {
      setIsLoading(true);
      setError(null);

      console.log(`Fetching weather for coordinates: ${lat}, ${lng}`);

      const [weather, weatherAlerts] = await Promise.all([
        weatherService.getWeatherData(lat, lng),
        weatherService.getWeatherAlerts(lat, lng).catch(() => []) // Don't fail if alerts fail
      ]);

      setWeatherData(weather);
      setAlerts(weatherAlerts);
      setLastUpdated(new Date());
      
    } catch (err) {
      console.error('Weather fetch error:', err);
      setError(err instanceof Error ? err.message : 'Errore nel caricamento dei dati meteo');
      toast.error('Impossibile caricare i dati meteo');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshWeather = () => {
    if (userLocation) {
      fetchWeatherData(userLocation.lat, userLocation.lng);
    }
  };

  // Fetch weather when location is available
  useEffect(() => {
    if (!locationLoading && userLocation) {
      fetchWeatherData(userLocation.lat, userLocation.lng);
    }
  }, [userLocation, locationLoading]);

  // Auto-refresh every 15 minutes
  useEffect(() => {
    if (!weatherData) return;

    const interval = setInterval(() => {
      if (userLocation) {
        fetchWeatherData(userLocation.lat, userLocation.lng);
      }
    }, 15 * 60 * 1000); // 15 minutes

    return () => clearInterval(interval);
  }, [weatherData, userLocation]);

  return {
    weatherData,
    alerts,
    isLoading: isLoading || locationLoading,
    error,
    lastUpdated,
    refreshWeather,
    hasLocation: !!userLocation,
  };
};
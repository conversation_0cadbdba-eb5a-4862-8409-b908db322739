import React, { useEffect, useRef, useState } from "react";
import { useAppVersion } from "@/hooks/useAppVersion";

/**
 * SplashScreen Component
 * Displays an animated clock-based splash screen with revenue visualization.
 *
 * @component
 * @param {Object} props - Component props
 * @param {Function} props.onFinish - Callback function to execute when splash screen animation completes
 */
interface SplashScreenProps {
  onFinish: () => void;
}

const SplashScreen = ({ onFinish }: SplashScreenProps) => {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const [revenue, setRevenue] = useState(0);
  const [isOffPeak, setIsOffPeak] = useState(true);
  const [currentTime, setCurrentTime] = useState("00:00");
  const [currentHour, setCurrentHour] = useState(0);
  const { data: appVersion, isLoading: isVersionLoading } = useAppVersion();

  // Create a ref to track start time to ensure both calculations use the same base
  const startTimeRef = useRef(Date.now());

  // Timer to finish the splash screen
  useEffect(() => {
    const timer = setTimeout(() => {
      onFinish();
    }, 10000);

    return () => clearTimeout(timer);
  }, [onFinish]);

  // Separate effect specifically for time updates
  // This ensures time display updates run in their own cycle
  useEffect(() => {
    const updateTimeDisplay = () => {
      // Use the same calculation logic as the animation function but DOUBLE the speed
      const elapsedMs = Date.now() - startTimeRef.current;
      const accelerationFactor = (24 * 60 * 60 * 1000) / 5000; // Double speed (24hrs in 5sec)
      const acceleratedMs = (elapsedMs * accelerationFactor) % (24 * 60 * 60 * 1000);

      // Convert to hours, minutes, seconds
      const totalSeconds = acceleratedMs / 1000;
      const hours = Math.floor(totalSeconds / 3600) % 24;
      const minutes = Math.floor((totalSeconds % 3600) / 60);

      // Update time display
      setCurrentTime(
        `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`
      );

      // Update current hour
      setCurrentHour(Math.floor(totalSeconds / 3600) % 24);

      // Schedule next update at animation frame rate
      requestAnimationFrame(updateTimeDisplay);
    };

    // Start the time update cycle
    const timeUpdateId = requestAnimationFrame(updateTimeDisplay);

    // Cleanup
    return () => cancelAnimationFrame(timeUpdateId);
  }, []);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    const width = canvas.width;
    const height = canvas.height;

    // Use the shared start time from ref
    const startTime = startTimeRef.current;

    // Get brand primary color from CSS variable
    const getComputedColor = (cssVar) => {
      const rootStyle = getComputedStyle(document.documentElement);
      const hsl = rootStyle.getPropertyValue(cssVar).trim();
      return `hsl(${hsl})`;
    };

    // Configuration with colors from CSS variables
    const config = {
      radius: (Math.min(width, height) / 2) * 0.85,
      centerX: width / 2,
      centerY: height / 2,
      clockColor: getComputedColor('--brand-primary'), // Brand primary color
      busyColor: "rgba(68, 68, 68, 0.8)", // Dark gray
      availableColor: "rgba(46, 212, 119, 0.8)", // Vibrant green
      logoColor: getComputedColor('--brand-primary'), // Brand primary color
      backgroundColor: "#FFFFFF", // White background
      hourHandColor: "#444444", // Dark hands for white background
      minuteHandColor: "#444444",
      secondHandColor: getComputedColor('--brand-primary'), // Brand primary color
    };

    // Helper function to draw clock hands
    const drawHand = (angle, length, width, color) => {
      ctx.beginPath();
      ctx.lineWidth = width;
      ctx.lineCap = "round";
      ctx.strokeStyle = color;

      ctx.moveTo(config.centerX, config.centerY);
      ctx.lineTo(
        config.centerX + Math.sin(angle) * length,
        config.centerY - Math.cos(angle) * length
      );

      ctx.stroke();
    };

    // Main draw function
    const draw = (() => {
      // Create a closure to store the lastHour
      let lastHour = -1;

      return function drawFrame() {
        // Clear canvas
        ctx.clearRect(0, 0, width, height);

        // Fill with white background
        ctx.fillStyle = config.backgroundColor;
        ctx.fillRect(0, 0, width, height);

        // Draw outer circle (clock face) - no fill, just border
        ctx.beginPath();
        ctx.arc(config.centerX, config.centerY, config.radius, 0, 2 * Math.PI);
        // No fill to keep the white background
        ctx.strokeStyle = config.clockColor;
        ctx.lineWidth = 8;
        ctx.stroke();

        // Calculate accelerated time (24 hours in 10 seconds)
        // Use the same time base as the time display effect
        const elapsedMs = Date.now() - startTime;
        const accelerationFactor = (24 * 60 * 60 * 1000) / 10000; // 24hrs in 10sec
        const acceleratedMs =
          (elapsedMs * accelerationFactor) % (24 * 60 * 60 * 1000);

        // Convert to hours, minutes, seconds
        const totalSeconds = acceleratedMs / 1000;
        const hours = Math.floor(totalSeconds / 3600) % 24;
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = Math.floor(totalSeconds % 60);

        // Update revenue based on hour changes
        const offPeak = !(hours >= 9 && hours < 17);
        const currentHourValue = Math.floor(totalSeconds / 3600) % 24;

        // Only update revenue when the hour changes
        if (currentHourValue !== lastHour) {
          lastHour = currentHourValue;

          // Add revenue with different amounts for peak vs off-peak
          const increase = offPeak
            ? Math.floor(250 + Math.random() * 200)
            : Math.floor(50 + Math.random() * 100);

          setRevenue((prev) => {
            // Reset if we're starting a new day and have accumulated revenue
            if (currentHourValue === 0 && prev > 1000) {
              return increase;
            }
            return prev + increase;
          });

          // Update off-peak state
          setIsOffPeak(offPeak);
        }

        // Draw time segments (busy vs available hours)
        for (let i = 0; i < 24; i++) {
          const startAngle = (i / 24) * Math.PI * 2;
          const endAngle = ((i + 1) / 24) * Math.PI * 2;
          const isBusyHour = i >= 9 && i < 17; // 9am-5pm are busy business hours

          // Highlight current hour segment
          const isCurrentHour = i === hours;
          const highlightFactor = isCurrentHour ? 0.2 : 0;

          ctx.beginPath();
          ctx.arc(
            config.centerX,
            config.centerY,
            config.radius * 0.75,
            startAngle,
            endAngle
          );
          ctx.arc(
            config.centerX,
            config.centerY,
            config.radius * 0.5,
            endAngle,
            startAngle,
            true
          );
          ctx.closePath();

          const segmentColor = isBusyHour
            ? config.busyColor
            : config.availableColor;
          ctx.fillStyle = isCurrentHour
            ? lightenColor(segmentColor, highlightFactor)
            : segmentColor;
          ctx.fill();

          // Add subtle border around segments
          ctx.strokeStyle = isCurrentHour
            ? "rgba(0, 0, 0, 0.3)"
            : "rgba(0, 0, 0, 0.1)";
          ctx.lineWidth = isCurrentHour ? 1.5 : 0.5;
          ctx.stroke();
        }

        // Draw 24 hour markers
        for (let i = 0; i < 24; i++) {
          const angle = (i / 24) * Math.PI * 2;
          const outerX = config.centerX + Math.sin(angle) * config.radius;
          const outerY = config.centerY - Math.cos(angle) * config.radius;
          const innerX = config.centerX + Math.sin(angle) * config.radius * 0.9;
          const innerY = config.centerY - Math.cos(angle) * config.radius * 0.9;

          // Draw hour marker
          ctx.beginPath();
          ctx.moveTo(innerX, innerY);
          ctx.lineTo(outerX, outerY);
          ctx.strokeStyle = "#555555"; // Darker markers for white background
          ctx.lineWidth = 2;
          ctx.stroke();

          // Draw hour number
          const textX = config.centerX + Math.sin(angle) * config.radius * 1.1;
          const textY = config.centerY - Math.cos(angle) * config.radius * 1.1;
          ctx.font = "bold 14px Arial";
          ctx.fillStyle = "#333333"; // Dark text for white background
          ctx.textAlign = "center";
          ctx.textBaseline = "middle";
          ctx.fillText(`${i}`, textX, textY);
        }

        // Draw center logo circle (without text)
        ctx.beginPath();
        ctx.arc(
          config.centerX,
          config.centerY,
          config.radius * 0.25,
          0,
          2 * Math.PI
        );
        ctx.fillStyle = config.logoColor;
        ctx.fill();
        ctx.strokeStyle = "rgba(255, 255, 255, 0.4)";
        ctx.lineWidth = 2;
        ctx.stroke();

        // No text in center of clock - removed status text

        // Calculate hand angles
        // Convert to 12-hour format for traditional clock display
        const hourAngle = ((hours % 12) + minutes / 60) * (Math.PI / 6);
        const minuteAngle = minutes * (Math.PI / 30);
        // const secondAngle = seconds * (Math.PI / 30);

        // // Draw second hand
        // drawHand(secondAngle, config.radius * 0.6, 2, config.secondHandColor);

        // Draw minute hand
        drawHand(minuteAngle, config.radius * 0.7, 4, config.minuteHandColor);

        // Draw hour hand
        drawHand(hourAngle, config.radius * 0.5, 6, config.hourHandColor);

        // Draw center pin
        ctx.beginPath();
        ctx.arc(config.centerX, config.centerY, 8, 0, 2 * Math.PI);
        ctx.fillStyle = config.secondHandColor; // Match second hand color
        ctx.fill();
        ctx.strokeStyle = "rgba(255, 255, 255, 0.5)";
        ctx.lineWidth = 2;
        ctx.stroke();

        // Request next animation frame
        animationRef.current = requestAnimationFrame(drawFrame);
      };
    })();

    // Helper to lighten a color
    const lightenColor = (color, factor) => {
      // Simple implementation for rgba strings
      if (color.startsWith("rgba(")) {
        const parts = color.substring(5, color.length - 1).split(",");
        const r = Math.min(255, parseInt(parts[0]) + factor * 255);
        const g = Math.min(255, parseInt(parts[1]) + factor * 255);
        const b = Math.min(255, parseInt(parts[2]) + factor * 255);
        const a = parseFloat(parts[3]);
        return `rgba(${r}, ${g}, ${b}, ${a})`;
      }
      return color;
    };

    // Start the animation
    animationRef.current = requestAnimationFrame(draw);

    // Cleanup
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  // Format revenue for display
  const formattedRevenue = new Intl.NumberFormat("it-IT", {
    style: "currency",
    currency: "EUR",
    maximumFractionDigits: 0,
  }).format(revenue);

  // Calculate progress percentage
  const revenueProgress = Math.min(revenue / 5000, 1) * 100;

  // Calculate percentage increase for off-peak vs peak hours
  const peakHourAvg = 75; // Average revenue during peak hours
  const offPeakAvg = 300; // Average revenue during off-peak hours
  const increasePercentage = Math.round(
    ((offPeakAvg - peakHourAvg) / peakHourAvg) * 100
  );

  // Determine if current hour is busy or available
  const isBusyHour = currentHour >= 9 && currentHour < 17;
  const statusText = isBusyHour ? "Ore di Punta" : "Sconto Disponibile";

  return (
    <div className="fixed inset-0 flex flex-col justify-between bg-white">
      {/* Top Gradient Area - Brand light fading to white */}
      <div className="w-full h-12 bg-gradient-to-b from-brand-light via-brand-light/50 to-white"></div>

      {/* Content Container - All content centered between gradient areas */}
      <div className="flex-1 flex flex-col items-center justify-center py-4">
        {/* Header */}
        <div className="w-full text-center mb-4">
          <h1 className="text-5xl font-bold text-brand-primary">CatchUp</h1>
        </div>

        {/* Canvas Container with consistent height */}
        <div className="flex justify-center items-center w-full" style={{ height: "38vh" }}>
          <canvas
            ref={canvasRef}
            width={350}
            height={350}
            className="max-w-full max-h-full"
          />
        </div>

        {/* Time Display */}
        <div className="flex flex-col items-center gap-3 mt-4">
          <div className="text-2xl font-bold bg-brand-primary text-white px-4 py-2 rounded-lg shadow-sm">
            {currentTime}
          </div>
          {isOffPeak ? (
            <span className="text-sm font-bold px-5 py-2 rounded-full bg-gradient-to-r from-amber-400 to-yellow-500 text-white shadow-md animate-pulse border-2 border-yellow-300">
              {statusText}
            </span>
          ) : (
            <span className="text-sm font-medium px-4 py-1.5 rounded-full bg-gray-100 text-gray-600">
              {statusText}
            </span>
          )}
        </div>

        {/* Revenue Display */}
        <div className="w-full max-w-md px-6 mt-4">
          <div className="w-full bg-gray-100 border border-gray-200 rounded-lg p-4 shadow-sm">
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-800 font-medium">Ricavi Giornalieri:</span>
              <span className="text-xl font-bold text-green-500">
                {formattedRevenue}
              </span>
            </div>

            <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2">
              <div
                className="h-2.5 rounded-full bg-green-400 transition-all duration-300"
                style={{
                  width: `${revenueProgress}%`,
                }}
              ></div>
            </div>

            <div className="text-sm text-right flex justify-end items-center gap-1.5">
              <span className="text-green-600 font-medium">Ricavi Extra!</span>
              {isOffPeak && (
                <span className="text-green-500 font-bold">
                  +{increasePercentage}%
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Footer Text Content */}
        <div className="text-center text-gray-800 px-6 mt-4">
          <p className="text-xl font-bold mb-2">Connetti & Risparmia</p>
          <p className="text-sm text-gray-600">Trasforma gli slot vuoti in ricavi</p>
        </div>
      </div>

      {/* Bottom Gradient Area - White fading to brand light */}
      <div className="w-full h-12 bg-gradient-to-t from-brand-light via-brand-light/50 to-white relative">
        {/* App Version */}
        <div className="absolute bottom-1 right-3 text-xs text-gray-500">
          {appVersion ? `Versione ${appVersion}` : ""}
        </div>
      </div>
    </div>
  );
};

export default SplashScreen;

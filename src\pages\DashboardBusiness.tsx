import { useState, useEffect } from "react";
// import { supabase } from "@/lib/supabaseClient";
import { 
  Layers, 
  Users, 
  Calendar, 
  TrendingUp, 
  DollarSign, 
  Clock, 
  ShoppingBag, 
  Star,
  Tag,
  PlusCircle,
  Building
} from "lucide-react";
import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";
import { useNavigate, useParams } from "react-router-dom";

/**
 * DashboardBusiness - Main dashboard for business users
 * Supports both multi-tenant overview and single business view
 */
const DashboardBusiness = () => {
  const navigate = useNavigate();
  const { id: businessId } = useParams();
  const [metrics, setMetrics] = useState({
    totalBookings: 0,
    pendingBookings: 0,
    activeDeals: 0,
    totalCustomers: 0,
    revenue: 0,
    avgRating: 0
  });
  const [recentBookings, setRecentBookings] = useState([]);
  const [userBusinesses, setUserBusinesses] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // For demo purposes, using mock data
        // In production, this would fetch from Supabase
        
        // If businessId is provided, fetch data for that specific business
        if (businessId) {
          setTimeout(() => {
            setMetrics({
              totalBookings: 247,
              pendingBookings: 12,
              activeDeals: 5,
              totalCustomers: 180,
              revenue: 8650.75,
              avgRating: 4.7
            });
            
            setRecentBookings([
              { id: 1, customer: "Marco Rossi", service: "Taglio capelli", date: "2023-11-15 10:00", status: "confirmed" },
              { id: 2, customer: "Giulia Bianchi", service: "Manicure", date: "2023-11-15 14:30", status: "pending" },
              { id: 3, customer: "Luca Verdi", service: "Massaggio", date: "2023-11-16 11:00", status: "confirmed" },
              { id: 4, customer: "Sofia Neri", service: "Trattamento viso", date: "2023-11-17 09:15", status: "confirmed" },
            ]);
          }, 1000);
        } 
        // Otherwise, fetch the list of user's businesses
        else {
          setTimeout(() => {
            setUserBusinesses([
              { id: "b1", name: "Salone Bellezza", type: "Parrucchiere", bookings: 24, revenue: 1250.50 },
              { id: "b2", name: "Spa Relax", type: "Centro benessere", bookings: 18, revenue: 2400.75 },
              { id: "b3", name: "Studio Fitness", type: "Palestra", bookings: 32, revenue: 1680.00 },
            ]);
          }, 1000);
        }
        
        setTimeout(() => {
          setIsLoading(false);
        }, 1200);
      } catch (error) {
        console.error("Error fetching business data:", error);
        setIsLoading(false);
      }
    };

    fetchData();
  }, [businessId]);

  const getStatusColor = (status) => {
    switch(status) {
      case 'confirmed': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-amber-600 bg-amber-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusLabel = (status) => {
    switch(status) {
      case 'confirmed': return 'Confermato';
      case 'pending': return 'In attesa';
      case 'cancelled': return 'Cancellato';
      default: return status;
    }
  };

  // Render an organization-level view when no specific business is selected
  const renderOrganizationDashboard = () => {
    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Dashboard Aziendale</h1>
          <button 
            onClick={() => navigate('/business-settings')}
            className="p-2 rounded-full bg-gray-100 hover:bg-gray-200"
          >
            <Clock className="h-5 w-5 text-gray-600" />
          </button>
        </div>
        
        {/* Businesses List */}
        <div className="bg-white rounded-lg shadow p-4 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Le tue Attività</h2>
            <button 
              onClick={() => navigate('/create-business')}
              className="flex items-center text-sm text-brand-primary"
            >
              <PlusCircle className="h-4 w-4 mr-1" />
              Aggiungi
            </button>
          </div>
          
          {isLoading ? (
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-100 animate-pulse rounded"></div>
              ))}
            </div>
          ) : (
            <div className="space-y-3">
              {userBusinesses.map((business) => (
                <div 
                  key={business.id} 
                  className="p-4 border rounded-lg hover:border-brand-primary cursor-pointer"
                  onClick={() => navigate(`/business/${business.id}`)}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="p-2 rounded-full bg-gray-100 mr-3">
                        <Building className="h-6 w-6 text-brand-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{business.name}</p>
                        <p className="text-sm text-gray-500">{business.type}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">€{business.revenue.toFixed(2)}</p>
                      <p className="text-sm text-gray-500">{business.bookings} prenotazioni</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Quick Stats */}
        <div className="bg-white rounded-lg shadow p-4 mb-6">
          <h2 className="text-lg font-semibold mb-4">Performance Complessiva</h2>
          {isLoading ? (
            <div className="h-40 bg-gray-100 animate-pulse rounded"></div>
          ) : (
            <div className="flex flex-col space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-600">Prenotazioni totali</span>
                </div>
                <span className="font-semibold">74</span>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-600">Ricavi totali</span>
                </div>
                <span className="font-semibold">€5,331.25</span>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <Tag className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-600">Offerte attive</span>
                </div>
                <span className="font-semibold">12</span>
              </div>
            </div>
          )}
        </div>
      </>
    );
  };

  // Render a specific business dashboard when a businessId is provided
  const renderBusinessDashboard = () => {
    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Dashboard Aziendale</h1>
          <button 
            onClick={() => navigate('/business-settings')}
            className="p-2 rounded-full bg-gray-100 hover:bg-gray-200"
          >
            <Clock className="h-5 w-5 text-gray-600" />
          </button>
        </div>
        
        {/* Key Metrics Cards */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <DashboardCard 
            title="Prenotazioni"
            value={metrics.totalBookings.toString()}
            icon={<Calendar className="h-5 w-5 text-blue-500" />}
            isLoading={isLoading}
          />
          <DashboardCard 
            title="Clienti"
            value={metrics.totalCustomers.toString()}
            icon={<Users className="h-5 w-5 text-green-500" />}
            isLoading={isLoading}
          />
          <DashboardCard 
            title="Offerte Attive"
            value={metrics.activeDeals.toString()}
            icon={<Layers className="h-5 w-5 text-purple-500" />}
            isLoading={isLoading}
          />
          <DashboardCard 
            title="Ricavi"
            value={`€${metrics.revenue.toFixed(2)}`}
            icon={<DollarSign className="h-5 w-5 text-yellow-500" />}
            isLoading={isLoading}
          />
        </div>
        
        {/* Performance Summary */}
        <div className="bg-white rounded-lg shadow p-4 mb-6">
          <h2 className="text-lg font-semibold mb-4">Performance</h2>
          {isLoading ? (
            <div className="h-40 bg-gray-100 animate-pulse rounded"></div>
          ) : (
            <div className="flex flex-col space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <ShoppingBag className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-600">Prenotazioni pendenti</span>
                </div>
                <span className="font-semibold">{metrics.pendingBookings}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <Star className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-600">Valutazione media</span>
                </div>
                <div className="flex items-center">
                  <span className="font-semibold mr-1">{metrics.avgRating.toFixed(1)}</span>
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <TrendingUp className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-600">Prenotazioni questo mese</span>
                </div>
                <div className="flex items-center">
                  <span className="font-semibold mr-1">+12%</span>
                  <div className="text-green-500">▲</div>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Recent Bookings */}
        <div className="bg-white rounded-lg shadow p-4 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Prenotazioni Recenti</h2>
            <button 
              onClick={() => navigate(`/business/${businessId}/bookings`)}
              className="text-sm text-brand-primary"
            >
              Vedi tutte
            </button>
          </div>
          
          {isLoading ? (
            <div className="space-y-3">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-100 animate-pulse rounded"></div>
              ))}
            </div>
          ) : (
            <div className="space-y-3">
              {recentBookings.map((booking) => (
                <div key={booking.id} className="p-3 border rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">{booking.customer}</p>
                      <p className="text-sm text-gray-500">{booking.service}</p>
                    </div>
                    <div className="flex flex-col items-end">
                      <p className="text-sm">{new Date(booking.date).toLocaleDateString()}</p>
                      <span className={`text-xs px-2 py-1 rounded-full mt-1 ${getStatusColor(booking.status)}`}>
                        {getStatusLabel(booking.status)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow p-4 mb-6">
          <h2 className="text-lg font-semibold mb-4">Azioni Rapide</h2>
          <div className="grid grid-cols-2 gap-3">
            <button 
              onClick={() => navigate(`/business/${businessId}/create-deal`)}
              className="p-3 border border-brand-primary text-brand-primary rounded-lg flex items-center justify-center hover:bg-brand-primary hover:text-white transition-colors"
            >
              <Tag className="h-5 w-5 mr-2" />
              Crea Offerta
            </button>
            <button 
              onClick={() => navigate(`/business/${businessId}/manage-availability`)}
              className="p-3 border border-brand-primary text-brand-primary rounded-lg flex items-center justify-center hover:bg-brand-primary hover:text-white transition-colors"
            >
              <Clock className="h-5 w-5 mr-2" />
              Gestisci Disponibilità
            </button>
          </div>
        </div>
      </>
    );
  };

  return (
    <div className="pb-20 pt-4 px-4 bg-gray-50 min-h-screen">
      {businessId ? renderBusinessDashboard() : renderOrganizationDashboard()}
      <BottomNavigationBar isBusiness={true} businessId={businessId} />
    </div>
  );
};

/**
 * DashboardCard - Reusable card component for dashboard metrics
 */
interface DashboardCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  isLoading?: boolean;
}

const DashboardCard = ({ title, value, icon, isLoading = false }: DashboardCardProps) => {
  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex items-center gap-3">
        <div className="p-2 rounded-full bg-gray-100">
          {icon}
        </div>
        <div>
          <p className="text-sm text-gray-500">{title}</p>
          {isLoading ? (
            <div className="h-6 w-16 bg-gray-200 animate-pulse rounded"></div>
          ) : (
            <p className="text-xl font-semibold">{value}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashboardBusiness; 
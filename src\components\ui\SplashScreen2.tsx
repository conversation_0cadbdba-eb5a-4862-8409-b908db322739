import React, { useEffect, useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useAppVersion } from "@/hooks/useAppVersion";

/**
 * SplashScreen2 Component
 * Displays an animated splash screen with CatchUP logo fade-in and text writing animation.
 *
 * @component
 * @param {Object} props - Component props
 * @param {Function} props.onFinish - Callback function to execute when splash screen animation completes
 */
interface SplashScreen2Props {
  onFinish: () => void;
}

const SplashScreen2 = ({ onFinish }: SplashScreen2Props) => {
  const { data: appVersion, isLoading: isVersionLoading } = useAppVersion();
  const [displayedText, setDisplayedText] = useState("");
  const fullText = "CatchUp";
  const textRef = useRef<HTMLDivElement>(null);

  // Timer to finish the splash screen
  useEffect(() => {
    const timer = setTimeout(() => {
      onFinish();
    }, 5000); // 5 seconds total for the splash screen

    return () => clearTimeout(timer);
  }, [onFinish]);

  // Text writing animation
  useEffect(() => {
    let currentIndex = 0;
    const textInterval = setInterval(() => {
      if (currentIndex < fullText.length) {
        setDisplayedText(fullText.substring(0, currentIndex + 1));
        currentIndex++;
      } else {
        clearInterval(textInterval);
      }
    }, 200); // Speed of typing animation

    return () => clearInterval(textInterval);
  }, []);

  return (
    <div className="fixed inset-0 flex flex-col justify-between bg-white">
      {/* Top Gradient Area - Brand light fading to white */}
      <div className="w-full h-12 bg-gradient-to-b from-brand-light via-brand-light/50 to-white"></div>

      {/* Content Container - All content centered between gradient areas */}
      <div className="flex-1 flex flex-col items-center justify-center py-4">
        {/* Logo Animation */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1.5, ease: "easeOut" }}
          className="mb-8"
        >
          <img
            src="/icon-512x512.png"
            alt="CatchUp Logo"
            className="w-32 h-32 object-contain"
          />
        </motion.div>

        {/* Text Writing Animation */}
        <div
          ref={textRef}
          className="text-5xl font-bold bg-gradient-to-r from-brand-secondary to-brand-primary bg-clip-text text-transparent"
        >
          {displayedText}
          <AnimatePresence>
            {displayedText.length < fullText.length && (
              <motion.span
                initial={{ opacity: 1 }}
                animate={{ opacity: 0 }}
                exit={{ opacity: 0 }}
                transition={{ repeat: Infinity, duration: 0.5 }}
                className="inline-block ml-1 bg-gradient-to-r from-brand-secondary to-brand-primary bg-clip-text text-transparent"
              >
                |
              </motion.span>
            )}
          </AnimatePresence>
        </div>

        {/* Tagline */}
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 2.5, duration: 0.8 }}
          className="text-gray-600 mt-4 text-center px-6"
        >
          Connetti & Risparmia
        </motion.p>
      </div>

      {/* Bottom Gradient Area - White fading to brand light */}
      <div className="w-full h-12 bg-gradient-to-t from-brand-light via-brand-light/50 to-white relative">
        {/* App Version */}
        <div className="absolute bottom-1 right-3 text-xs text-gray-500">
          {appVersion ? `Versione ${appVersion}` : ""}
        </div>
      </div>
    </div>
  );
};

export default SplashScreen2;

import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Deal } from '@/types/deals';
import { toast } from 'sonner';

export const useBusinessDeals = (businessId: string | undefined) => {
  const [deals, setDeals] = useState<Deal[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchBusinessDeals = async () => {
      if (!businessId) {
        setDeals([]);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const { data, error: fetchError } = await supabase
          .from('deals')
          .select('*, businesses(*)')
          .eq('business_id', businessId)
          .order('created_at', { ascending: false });

        if (fetchError) {
          throw new Error(fetchError.message);
        }

        setDeals(data as Deal[] || []);
      } catch (err: any) {
        console.error('Error fetching business deals:', err);
        setError(err);
        toast.error("Errore nel caricamento delle offerte");
      } finally {
        setIsLoading(false);
      }
    };

    fetchBusinessDeals();
  }, [businessId]);

  return {
    deals,
    isLoading,
    error,
  };
};
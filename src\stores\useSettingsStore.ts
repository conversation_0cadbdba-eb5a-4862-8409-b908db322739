
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type EchoSettings = {
  autoStartConversation: boolean;
  showTranscript: boolean;
  voiceVolume: number;
  aiModel: string;
};

type SettingsState = {
  settings: EchoSettings;
  updateSettings: (settings: Partial<EchoSettings>) => void;
};

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set) => ({
      settings: {
        autoStartConversation: true,
        showTranscript: true,
        voiceVolume: 80,
        aiModel: 'gpt-4o',
      },
      updateSettings: (newSettings) =>
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        })),
    }),
    {
      name: 'echo-settings',
    }
  )
);

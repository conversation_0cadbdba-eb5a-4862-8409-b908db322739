import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/auth/useAuth";

export const useUserPreferencesCheck = () => {
  const { user } = useAuth();
  const [hasPreferences, setHasPreferences] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      setHasPreferences(null);
      setIsLoading(false);
      return;
    }
    
    const checkUserPreferences = async () => {
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('user_preferences')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();
        
        if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
          console.error('Error checking user preferences:', error);
          setHasPreferences(null);
          return;
        }
        
        setHasPreferences(!!data);
      } catch (error) {
        console.error('Error in checkUserPreferences:', error);
        setHasPreferences(null);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkUserPreferences();
  }, [user]);

  return {
    hasPreferences,
    isLoading
  };
};
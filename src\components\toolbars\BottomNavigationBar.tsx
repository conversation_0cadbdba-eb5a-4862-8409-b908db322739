import {
  Home,
  Tag,
  CalendarCheck,
  User,
  MessageCircle,
  Mic,
  CalendarClock,
} from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import { useMemo, useEffect } from "react";
import { useVoiceDialog } from "@/contexts/VoiceDialogContext";


interface MenuItem {
  Icon: any;
  label: string;
  path: string;
  isVoiceButton?: boolean;
}

interface BottomNavigationBarProps {
  isBusiness: boolean;
  showVoiceButton?: boolean;
  businessId?: string;
  forceOpenVoiceDialog?: boolean;
  onVoiceDialogClose?: () => void;
}

const BottomNavigationBar = ({
  isBusiness,
  showVoiceButton = true,
  businessId,
  forceOpenVoiceDialog = false,
  onVoiceDialogClose,
}: BottomNavigationBarProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isVoiceDialogOpen, openVoiceDialog, closeVoiceDialog, setBusiness } = useVoiceDialog();

  // Handle forced opening of voice dialog
  useEffect(() => {
    if (forceOpenVoiceDialog) {
      openVoiceDialog();
    }
  }, [forceOpenVoiceDialog, openVoiceDialog]);

  // Set business ID when it changes
  useEffect(() => {
    setBusiness(businessId);
  }, [businessId, setBusiness]);

  const handleVoiceDialogClose = () => {
    closeVoiceDialog();
    if (onVoiceDialogClose) {
      onVoiceDialogClose();
    }
  };

  const menuItems = useMemo((): MenuItem[] => {
    const baseItems: MenuItem[] = isBusiness ? [
      { Icon: Home, label: "Home", path: "/" },
      { Icon: Tag, label: "Offerte", path: "/deals" },
    ] : [
      { Icon: Home, label: "Home", path: "/" },
      { Icon: Tag, label: "Offerte", path: "/deals" },
    ];

    const endItems: MenuItem[] = [
      
      { Icon: CalendarClock, label: "Prenotazioni", path: "/mybookings" },
      { Icon: User, label: "Profilo", path: "/profile" },
    ];
// {
//   icon: CalendarClock,
//   label: "Le mie prenotazioni",
//   count: "3",
//   path: "/le-mie-prenotazioni",
//   authenticationRequired: true,
// },
    // Add voice button as center item if enabled
    if (showVoiceButton) {
      return [
        ...baseItems,
        { 
          Icon: Mic, 
          label: "Assistente", 
          path: "", // Empty path since it opens dialog
          isVoiceButton: true 
        },
        ...endItems,
      ];
    }

    return [...baseItems, ...endItems];
  }, [isBusiness, businessId, showVoiceButton]);

  const handleItemClick = (item: MenuItem) => {
    if (item.isVoiceButton) {
      openVoiceDialog();
    } else {
      navigate(item.path);
    }
  };

  return (
    <>
      <nav className="fixed bottom-0 w-full bg-white border-t border-gray-200 px-4 py-2">
        <div className="flex justify-between items-center relative max-w-md mx-auto">
          {menuItems.map((item, index) => (
            <div key={item.label} className="flex-1 flex justify-center">
              <button
                onClick={() => handleItemClick(item)}
                className={`flex flex-col items-center justify-center relative ${
                  item.isVoiceButton
                    ? "text-white" 
                    : location.pathname === item.path
                    ? "text-brand-primary"
                    : "text-gray-400"
                } hover:text-brand-primary transition-all duration-300 ${
                  item.isVoiceButton ? "hover:scale-105" : ""
                }`}
              >
                {item.isVoiceButton ? (
                  <>
                    {/* Elevated Voice Button - Floats above toolbar */}
                    <div className="relative group -mt-8 flex flex-col items-center">
                      {/* Main button with gradient and sophisticated shadows */}
                      <div className="w-16 h-16 bg-gradient-to-br from-brand-primary via-purple-500 to-pink-500 rounded-full shadow-full relative overflow-hidden">
                        {/* Glass morphism overlay */}
                        <div className="absolute inset-0 bg-white/20 backdrop-blur-sm rounded-full"></div>
                        
                        {/* Icon container */}
                        <div className="absolute inset-0 flex items-center justify-center">
                          <item.Icon className="h-6 w-6 text-white relative z-10" />
                        </div>
                        
                        {/* Animated glow effect */}
                        <div className="absolute inset-0 bg-gradient-to-br from-brand-primary/50 to-purple-500/50 rounded-full animate-pulse opacity-60"></div>
                        
                        {/* Interactive ripple effect on hover */}
                        <div className="absolute inset-0 bg-white/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                      
                      {/* Enhanced floating shadow beneath */}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-10 h-4 bg-brand-primary/20 rounded-full blur-lg"></div>
                      
                      {/* Active indicator dots */}
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white shadow-lg animate-pulse"></div>
                    </div>
                  </>
                ) : (
                  <>
                    <item.Icon className="h-6 w-6" />
                    <span className="text-xs mt-1">{item.label}</span>
                  </>
                )}
              </button>
            </div>
          ))}
        </div>
        
        {/* Enhanced nav bar background with subtle gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-gray-50/50 to-transparent pointer-events-none"></div>
      </nav>
    </>
  );
};

export default BottomNavigationBar;

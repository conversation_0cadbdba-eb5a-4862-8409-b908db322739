import { PencilLine, Save, X } from "lucide-react";
import { PhotosSection } from "./PhotosSection";
import { BusinessFormFields } from "./BusinessFormFields";
import { BusinessDetails } from "./BusinessDetails";
import { Business } from "@/hooks/useBusinesses";

interface BusinessInfoFormProps {
  business: Business;
  isEditing: boolean;
  isSaving: boolean;
  editForm: Partial<Business>;
  onEdit: () => void;
  onCancel: () => void;
  onSave: () => void;
  onFormChange: (field: keyof Business, value: any) => void;
}

export const BusinessInfoForm = ({
  business,
  isEditing,
  isSaving,
  editForm,
  onEdit,
  onCancel,
  onSave,
  onFormChange,
}: BusinessInfoFormProps) => {
  return (
    <div className="bg-white p-4 rounded-xl shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Anagrafica</h2>
        {!isEditing ? (
          <button
            onClick={onEdit}
            className="flex items-center gap-1 text-sm text-brand-primary"
          >
            <PencilLine className="h-4 w-4" />
            <span>Modifica</span>
          </button>
        ) : (
          <div className="flex items-center gap-2">
            <button
              onClick={onCancel}
              className="flex items-center gap-1 text-sm text-gray-500"
              disabled={isSaving}
            >
              <X className="h-4 w-4" />
              <span>Annulla</span>
            </button>
            <button
              onClick={onSave}
              className="flex items-center gap-1 text-sm text-brand-primary"
              disabled={isSaving}
            >
              <Save className="h-4 w-4" />
              <span>{isSaving ? "Salvataggio..." : "Salva"}</span>
            </button>
          </div>
        )}
      </div>

      <div className="flex flex-col gap-4">
        {isEditing ? (
          <>
            <PhotosSection
              businessId={business.id}
              photos={editForm.photos}
              onPhotosChange={(photos) => onFormChange('photos', photos)}
              onSave={onSave}
            />
            <BusinessFormFields
              editForm={editForm}
              onFormChange={onFormChange}
            />
          </>
        ) : (
          <BusinessDetails business={business} />
        )}
      </div>
    </div>
  );
};

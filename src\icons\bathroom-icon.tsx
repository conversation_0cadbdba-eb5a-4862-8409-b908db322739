import React from 'react';

export const BathroomIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.00016 11.9999C6.18905 11.9999 6.34739 11.936 6.47516 11.8083C6.60294 11.6805 6.66683 11.5221 6.66683 11.3333C6.66683 11.1444 6.60294 10.986 6.47516 10.8583C6.34739 10.7305 6.18905 10.6666 6.00016 10.6666C5.81127 10.6666 5.65294 10.7305 5.52516 10.8583C5.39739 10.986 5.3335 11.1444 5.3335 11.3333C5.3335 11.5221 5.39739 11.6805 5.52516 11.8083C5.65294 11.936 5.81127 11.9999 6.00016 11.9999ZM8.00016 11.9999C8.18905 11.9999 8.34739 11.936 8.47516 11.8083C8.60294 11.6805 8.66683 11.5221 8.66683 11.3333C8.66683 11.1444 8.60294 10.986 8.47516 10.8583C8.34739 10.7305 8.18905 10.6666 8.00016 10.6666C7.81127 10.6666 7.65294 10.7305 7.52516 10.8583C7.39739 10.986 7.3335 11.1444 7.3335 11.3333C7.3335 11.5221 7.39739 11.6805 7.52516 11.8083C7.65294 11.936 7.81127 11.9999 8.00016 11.9999ZM10.0002 11.9999C10.1891 11.9999 10.3474 11.936 10.4752 11.8083C10.6029 11.6805 10.6668 11.5221 10.6668 11.3333C10.6668 11.1444 10.6029 10.986 10.4752 10.8583C10.3474 10.7305 10.1891 10.6666 10.0002 10.6666C9.81127 10.6666 9.65294 10.7305 9.52516 10.8583C9.39739 10.986 9.3335 11.1444 9.3335 11.3333C9.3335 11.5221 9.39739 11.6805 9.52516 11.8083C9.65294 11.936 9.81127 11.9999 10.0002 11.9999ZM6.00016 9.99992C6.18905 9.99992 6.34739 9.93603 6.47516 9.80825C6.60294 9.68047 6.66683 9.52214 6.66683 9.33325C6.66683 9.14436 6.60294 8.98603 6.47516 8.85825C6.34739 8.73047 6.18905 8.66659 6.00016 8.66659C5.81127 8.66659 5.65294 8.73047 5.52516 8.85825C5.39739 8.98603 5.3335 9.14436 5.3335 9.33325C5.3335 9.52214 5.39739 9.68047 5.52516 9.80825C5.65294 9.93603 5.81127 9.99992 6.00016 9.99992ZM8.00016 9.99992C8.18905 9.99992 8.34739 9.93603 8.47516 9.80825C8.60294 9.68047 8.66683 9.52214 8.66683 9.33325C8.66683 9.14436 8.60294 8.98603 8.47516 8.85825C8.34739 8.73047 8.18905 8.66659 8.00016 8.66659C7.81127 8.66659 7.65294 8.73047 7.52516 8.85825C7.39739 8.98603 7.3335 9.14436 7.3335 9.33325C7.3335 9.52214 7.39739 9.68047 7.52516 9.80825C7.65294 9.93603 7.81127 9.99992 8.00016 9.99992ZM10.0002 9.99992C10.1891 9.99992 10.3474 9.93603 10.4752 9.80825C10.6029 9.68047 10.6668 9.52214 10.6668 9.33325C10.6668 9.14436 10.6029 8.98603 10.4752 8.85825C10.3474 8.73047 10.1891 8.66659 10.0002 8.66659C9.81127 8.66659 9.65294 8.73047 9.52516 8.85825C9.39739 8.98603 9.3335 9.14436 9.3335 9.33325C9.3335 9.52214 9.39739 9.68047 9.52516 9.80825C9.65294 9.93603 9.81127 9.99992 10.0002 9.99992ZM4.66683 7.99992H11.3335V7.33325C11.3335 6.41103 11.0085 5.62492 10.3585 4.97492C9.7085 4.32492 8.92239 3.99992 8.00016 3.99992C7.07794 3.99992 6.29183 4.32492 5.64183 4.97492C4.99183 5.62492 4.66683 6.41103 4.66683 7.33325V7.99992ZM5.70016 6.99992C5.78905 6.43325 6.04739 5.95825 6.47516 5.57492C6.90294 5.19159 7.41127 4.99992 8.00016 4.99992C8.58905 4.99992 9.09739 5.19159 9.52516 5.57492C9.95294 5.95825 10.2113 6.43325 10.3002 6.99992H5.70016ZM2.66683 14.6666C2.30016 14.6666 1.98627 14.536 1.72516 14.2749C1.46405 14.0138 1.3335 13.6999 1.3335 13.3333V2.66659C1.3335 2.29992 1.46405 1.98603 1.72516 1.72492C1.98627 1.46381 2.30016 1.33325 2.66683 1.33325H13.3335C13.7002 1.33325 14.0141 1.46381 14.2752 1.72492C14.5363 1.98603 14.6668 2.29992 14.6668 2.66659V13.3333C14.6668 13.6999 14.5363 14.0138 14.2752 14.2749C14.0141 14.536 13.7002 14.6666 13.3335 14.6666H2.66683ZM2.66683 13.3333H13.3335V2.66659H2.66683V13.3333Z"
      fill="currentColor"
    />
  </svg>
);

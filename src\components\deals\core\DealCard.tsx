/**
 * @fileoverview DealCard component for displaying deal information in either full or compact format
 * Supports interactive features like favoriting and displays real-time availability.
 */

import { motion } from "framer-motion";
import {
  MapPin,
  Clock,
  Heart,
  Users,
  PencilIcon,
  Trash2,
  Copy,
  Star,
  Zap,
  Store, // Better icon for business visit
} from "lucide-react";
import {
  formatDistanceToNow,
  isSameDay,
  isToday as isDateToday,
  parseISO,
} from "date-fns";
import { it } from "date-fns/locale";

import { useTimeSlots } from "@/hooks/useTimeSlots";
import { Deal } from "@/types/deals";
import DealTimeSlots from "../booking/DealTimeSlots";
import { useAuth } from "@/hooks/auth/useAuth";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import BusinessAddress from "@/components/business/BusinessAddress";

import Hint from "@/components/Hint";
import { DealVisitBusinessButton } from "../ui/DealVisitBusinessButton";
import { ShareButton } from "@/components/sharing/ShareButton";

/**
 * Props interface for the DealCard component
 * @interface DealCardProps
 * @property {Deal} deal - The deal object containing all deal information
 * @property {'full' | 'compact'} [variant='full'] - Display variant of the card
 * @property {() => void} [onFavoriteClick] - Callback function when favorite button is clicked
 * @property {boolean} [isFavorite=false] - Whether the deal is marked as favorite
 * @property {() => void} [onClick] - Callback function when the card is clicked
 * @property {boolean} [showFavoriteButton=false] - Whether to show the favorite button
 * @property {boolean} [showDetailedTimeSlots=false] - Whether to show detailed time slots
 * @property {boolean} [isBusinessOwner=false] - Whether the current user is the business owner
 * @property {() => void} [onEditClick] - Callback function when the edit button is clicked
 * @property {boolean} [hideTimeSlots=false] - Whether to hide the time slots section
 * @property {string} [selectedTime] - Selected time for nearby deals filtering
 * @property {string} [selectedDate] - Selected date for nearby deals filtering

 */
interface DealCardProps {
  deal: Deal;
  variant?: "full" | "compact";
  onFavoriteClick?: () => void;
  isFavorite?: boolean;
  onClick?: () => void;
  showFavoriteButton?: boolean;
  showDetailedTimeSlots?: boolean;
  isBusinessOwner?: boolean;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onDuplicateClick?: () => void;
  hideTimeSlots?: boolean;
  selectedTime?: string;
  selectedDate?: string;
  showVisitBusiness: boolean;
  showAddress?: boolean;
  onShareClick?: () => void;
  showShareButton?: boolean;
}

/**
 * DealCard component displays a deal with its details, pricing, and availability
 * Features:
 * - Responsive image display with discount badge
 * - Business information with location
 * - Time slot availability visualization
 * - Interactive favorite button
 * - Pricing information with original and discounted prices
 * - Availability indicators and warnings
 *
 * @param {DealCardProps} props - Component props
 * @returns {JSX.Element} Rendered DealCard component
 */
const DealCard = ({
  deal,
  variant = "full",
  onFavoriteClick,
  isFavorite = false,
  onClick,
  showFavoriteButton = false,
  showDetailedTimeSlots = false,
  isBusinessOwner = false,
  onEditClick,
  onDeleteClick,
  onDuplicateClick,
  hideTimeSlots = false,
  selectedTime,
  selectedDate,
  showVisitBusiness,
  showAddress = true,
  onShareClick,
  showShareButton = false,
}: DealCardProps) => {
  const {
    parsedTimeSlots,
    formatTimeRange,
    getAvailableDaysText,
    hasAvailableSeats,
    getAvailableSeatsText,
    isLowAvailability,
    getTimeSlotsForDay,
    getAvailableSeatsForTimeSlot,
    getSpecificTimeSlotInfo,
  } = useTimeSlots(deal.time_slots);

  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  const handleBookNow = (e) => {
    e.stopPropagation();

    if (!isAuthenticated) {
      toast.error("Devi accedere per prenotare");
      navigate("/login");
      return;
    }

    navigate(`/book/${deal.id}`);
  };

  /**
   * Handles the favorite button click event
   * Prevents event propagation to parent elements
   * @param {React.MouseEvent} e - Click event
   */
  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onFavoriteClick?.();
  };

  const startDate = parseISO(deal.start_date);
  const endDate = parseISO(deal.end_date);
  const isDealToday = isSameDay(startDate, endDate) && isDateToday(startDate);
  // Se abbiamo un orario e una data selezionati (per offerte nelle vicinanze),
  // otteniamo le informazioni specifiche per quello slot orario
  const specificTimeSlotInfo =
    selectedTime && selectedDate
      ? getSpecificTimeSlotInfo(selectedDate, selectedTime)
      : null;
  /**
   * Generates expiration text and color based on deal's end date
   * @returns {{ text: string, color: string }} Object containing display text and color
   */
  const getOnlyTodayText = () => {
    if (specificTimeSlotInfo !== null)
      return { text: "", color: "text-brand-primary" };
    if (isDealToday) {
      return { text: "Solo oggi", color: "text-brand-primary" };
    }

    const timeToExpiry = formatDistanceToNow(endDate, { locale: it });
    if (timeToExpiry.includes("ora")) {
      return { text: "Ultime ore", color: "text-red-500" };
    }
    if (timeToExpiry.includes("giorno")) {
      return { text: `Termina tra ${timeToExpiry}`, color: "text-orange-500" };
    }
    return { text: `Scade tra ${timeToExpiry}`, color: "text-gray-600" };
  };

  const expirationInfo = getOnlyTodayText();
  const availableDaysText = !isDealToday ? getAvailableDaysText : null;

  return (
    <motion.div
      whileHover={{
        scale: 1.01,
        y: -4,
        transition: { duration: 0.2, ease: "easeOut" },
      }}
      className="group relative bg-gradient-to-br from-white via-gray-50/50 to-white rounded-3xl overflow-hidden cursor-pointer border border-gray-100/60 shadow-xl shadow-gray-200/40 backdrop-blur-sm"
      onClick={onClick}
    >
      {/* Animation keyframes */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
          @keyframes gradientSlide {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
          }
        `,
        }}
      />

      {/* Floating Price Badge - Top Right */}
      <div className="absolute -top-2 -right-2 z-20">
        <div className="bg-gradient-to-r from-brand-primary to-purple-600 text-white px-6 py-3 rounded-2xl shadow-lg transform rotate-3 group-hover:rotate-6 transition-transform duration-300">
          {deal.discounted_price === 0 ? (
            <div className="text-center">
              <div className="text-xs font-medium opacity-90">Sconto</div>
              <div className="text-lg font-bold">
                -{deal.discount_percentage}%
              </div>
            </div>
          ) : (
            <div className="text-center">
              <div className="text-xs font-medium opacity-90 line-through">
                €{deal.original_price}
              </div>
              <div className="text-lg font-bold">€{deal.discounted_price}</div>
            </div>
          )}
        </div>
      </div>

      {/* Image Section with Creative Overlay */}
      <div className="relative h-56 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent z-10"></div>
        <img
          className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-700"
          src={
            deal.images && deal.images.length > 0
              ? deal.images[0]
              : `https://picsum.photos/400/300?random=${deal.id}`
          }
          alt={deal.title}
        />

        {/* Floating Business Name Badge */}
        {deal.businesses && showAddress ? (
          <div className="absolute bottom-4 left-4 right-4 z-20">
            <div className="bg-white/95 backdrop-blur-md rounded-2xl p-4 shadow-xl border border-white/40">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="font-bold text-gray-900 text-lg leading-tight mb-1 truncate">
                    {deal.businesses.name}
                  </h3>
                  <div className="flex items-center gap-1 text-gray-600">
                    <BusinessAddress
                      address={deal.businesses.address}
                      zipCode={deal.businesses.zip_code}
                      city={deal.businesses.city}
                      state={deal.businesses.state}
                      className="text-xs truncate"
                      showIcon={true}
                    />
                  </div>
                  
                  <div className="flex items-center gap-1 text-gray-500 mt-1">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    <span className="text-xs">
                      {deal.businesses?.score
                        ? Number(deal.businesses.score).toFixed(1)
                        : "0"}
                      {` (${deal.businesses?.review_count || 0} ${
                        (deal.businesses?.review_count || 0) === 1
                          ? "recensione"
                          : "recensioni"
                      })`}
                    </span>
                  </div>
                 </div>
                 {/* Button container with proper spacing */}
                 <div className="flex items-center gap-2 ml-3">
                    {showFavoriteButton && (
                      <button
                        onClick={handleFavoriteClick}
                        className="absolute top-4 right-4 p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow z-20"
                        aria-label={
                         isFavorite
                           ? "Remove from favorites"
                           : "Add to favorites"
                       }
                     >
                       <Heart
                         className={`h-4 w-4 ${
                           isFavorite
                             ? "text-brand-primary fill-current"
                             : "text-gray-500"
                         }`}
                       />
                     </button>
                   )}

                   {showShareButton && (
                     <ShareButton
                       onClick={(e) => {
                         e.stopPropagation();
                         onShareClick?.();
                       }}
                       size="icon"
                       variant="outline"
                       className="bg-white/95 backdrop-blur-sm border-white/40 hover:bg-white shadow-lg"
                     />
                   )}

                   {showVisitBusiness ? (
                     <DealVisitBusinessButton deal={deal} navigate={navigate} />
                   ) : null}
                 </div>
              </div>
            </div>
          </div>
        ) : null}

        {/* Status and Action Badges */}
        <div className="absolute top-4 left-4 flex flex-wrap gap-2 z-20">
          {deal.status === "draft" && (
            <div className="bg-gray-900/80 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-xs font-medium">
              Bozza
            </div>
          )}
          {deal.status === "expired" && (
            <div className="bg-red-600/90 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-xs font-medium">
              Scaduta
            </div>
          )}
          {isLowAvailability && (
            <div className="bg-amber-500/90 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-xs font-medium flex items-center gap-1">
              <Zap className="h-3 w-3" />
              Ultimi posti
            </div>
          )}
          {expirationInfo.text && (
            <div className="bg-white/90 backdrop-blur-sm text-gray-900 px-3 py-1.5 rounded-full text-xs font-medium border border-white/40">
              {expirationInfo.text}
            </div>
          )}
        </div>

        {/* Business Owner Actions */}
        {isBusinessOwner && (
          <div className="absolute top-4 right-4 flex gap-2 z-20">
            {[
              {
                icon: PencilIcon,
                onClick: onEditClick,
                label: "Modifica",
                color: "bg-blue-500",
              },
              {
                icon: Copy,
                onClick: onDuplicateClick,
                label: "Duplica",
                color: "bg-green-500",
              },
              {
                icon: Trash2,
                onClick: onDeleteClick,
                label: "Elimina",
                color: "bg-red-500",
              },
            ].map((action, index) => (
              <motion.button
                key={index}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                onClick={(e) => {
                  e.stopPropagation();
                  action.onClick?.();
                }}
                className={`${action.color} text-white p-2.5 rounded-full shadow-lg hover:shadow-xl transition-all backdrop-blur-sm`}
                aria-label={action.label}
              >
                <action.icon className="h-4 w-4" />
              </motion.button>
            ))}
          </div>
        )}
      </div>

      {/* Content Section with Asymmetric Layout */}
      <div className="p-6 space-y-4">
        {/* Deal Title with Creative Typography */}
        <div className="space-y-2">
          <div className="flex items-start justify-between gap-3">
            <h4 className="font-bold text-xl text-gray-900 leading-tight flex-1">
              {deal.title}
            </h4>
            <div className="flex items-center gap-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold">
              <Star className="h-3 w-3 fill-current" />
              HOT
            </div>
          </div>

          {variant === "full" && deal.description && (
            <p className="text-gray-600 text-sm leading-relaxed line-clamp-2">
              {deal.description}
            </p>
          )}
        </div>

        {/* Time and Availability in Modern Pills */}
        <div className="flex items-center justify-between gap-4">
          <div className="flex flex-wrap gap-2">
            {specificTimeSlotInfo ? (
              <div className="bg-gradient-to-r from-brand-primary/10 to-purple-600/10 border border-brand-primary/20 rounded-xl px-4 py-2 flex items-center gap-2">
                <Clock className="h-4 w-4 text-brand-primary" />
                <span className="font-semibold text-brand-primary text-sm">
                  {`${specificTimeSlotInfo.start_time} - ${specificTimeSlotInfo.end_time}`}
                </span>
              </div>
            ) : formatTimeRange ? (
              <div className="bg-gradient-to-r from-brand-primary/10 to-purple-600/10 border border-brand-primary/20 rounded-xl px-4 py-2 flex items-center gap-2">
                <Clock className="h-4 w-4 text-brand-primary" />
                <span className="font-semibold text-brand-primary text-sm">
                  {formatTimeRange}
                </span>
              </div>
            ) : null}

            {!specificTimeSlotInfo && availableDaysText && (
              <div className="bg-indigo-50 border border-indigo-200 rounded-xl px-4 py-2">
                <span className="text-indigo-700 font-medium text-sm">
                  {availableDaysText}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Availability Indicator with Modern Design */}
        {deal.time_slots && (
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/60 rounded-2xl p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <div className="bg-green-500 rounded-full p-1.5">
                  <Users className="h-3 w-3 text-white" />
                </div>
                <span className="font-semibold text-green-800 text-sm">
                  {specificTimeSlotInfo
                    ? `${specificTimeSlotInfo.available_seats} posti disponibili`
                    : getAvailableSeatsText}
                </span>
              </div>
              {hasAvailableSeats && (
                <div className="text-right">
                  <div className="text-xs text-green-600 font-medium">
                    Disponibilità
                  </div>
                  <div
                    className={`text-sm font-bold ${
                      isLowAvailability ? "text-amber-600" : "text-green-600"
                    }`}
                  >
                    {isLowAvailability ? "Limitata" : "Buona"}
                  </div>
                </div>
              )}
            </div>

            {hasAvailableSeats && (
              <div className="relative w-full bg-white rounded-full h-3 overflow-hidden shadow-inner">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{
                    width: `${Math.min(100, isLowAvailability ? 25 : 85)}%`,
                  }}
                  transition={{ duration: 1, ease: "easeOut" }}
                  className={`h-full rounded-full ${
                    isLowAvailability
                      ? "bg-gradient-to-r from-amber-400 to-orange-500"
                      : "bg-gradient-to-r from-green-400 to-emerald-500"
                  }`}
                />
              </div>
            )}
          </div>
        )}

        {/* Time Slots Section with Modern Styling */}
        {(variant === "full" || showDetailedTimeSlots) &&
          deal.time_slots &&
          !hideTimeSlots && (
            <div className="bg-gray-50/60 rounded-2xl p-4 border border-gray-100">
              <DealTimeSlots
                timeSlots={deal.time_slots}
                isShort={variant === "compact"}
                hide={hideTimeSlots}
              />
            </div>
          )}
      </div>

      {/* Bottom Gradient Accent */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-brand-primary via-purple-500 to-pink-500"></div>
    </motion.div>
  );
};

export default DealCard;

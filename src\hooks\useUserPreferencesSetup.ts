import { useEffect } from 'react';
import { useAuth } from '@/hooks/auth/useAuth';
import { useUserPreferencesManager } from '@/hooks/useUserPreferencesManager';
import { useFilters, useFilterActions } from '@/stores/useFilterStore';

/**
 * Hook that automatically sets up filter categories based on user preferences
 * when they first access the application. Uses Zustand store's applicationAccess
 * flag to ensure preferences are only applied once on first load.
 * 
 * Simple logic:
 * - applicationAccess = false: Load user preferences and set applicationAccess = true
 * - applicationAccess = true: Do nothing, keep current filters
 */
export const useUserPreferencesSetup = () => {
  const { user } = useAuth();
  const { preferences, isLoading } = useUserPreferencesManager();
  const filters = useFilters();
  const { setSelectedCategories, setApplicationAccess } = useFilterActions();

  useEffect(() => {
    // Only run setup if user is authenticated and preferences are loaded
    if (!user || isLoading) {
      console.log('🔄 Skipping setup: user authenticated?', !!user, 'preferences loaded?', !isLoading);
      return;
    }

    // Simple logic: if applicationAccess is false, load preferences and set it to true
    if (!filters.applicationAccess) {
      console.log('🎯 First app access - loading user preferences');
      
      if (preferences && preferences.categories && preferences.categories.length > 0) {
        // Apply user preferences
        setSelectedCategories(preferences.categories);
        console.log('✅ User preferences applied:', preferences.categories);
      } else {
        console.log('ℹ️ No user preferences found - keeping defaults');
      }
      
      // Mark that user has accessed the application
      setApplicationAccess(true);
      console.log('🏁 Application access marked as true');
    } else {
      console.log('✨ User has already accessed app - keeping current filters');
    }
  }, [user, preferences, isLoading, filters.applicationAccess, setSelectedCategories, setApplicationAccess]);

  // Reset applicationAccess when user changes (login/logout)
  useEffect(() => {
    setApplicationAccess(false);
    console.log('🔄 User changed - resetting applicationAccess to false');
  }, [user?.id, setApplicationAccess]);

  return {
    applicationAccess: filters.applicationAccess,
    hasPreferences: !!preferences,
    preferencesLoaded: !isLoading
  };
}; 
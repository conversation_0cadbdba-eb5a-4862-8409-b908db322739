
import { useRef, useState } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

export const useTextToSpeech = () => {
  const [isSpeaking, setIsSpeaking] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const speak = async (text: string) => {
    try {
      setIsSpeaking(true);
      const { data, error } = await supabase.functions.invoke('voice-agent', {
        body: { 
          text,
          voiceId: "pFZP5JQG7iQjIQuC4Bku"
        }
      });

      if (error) throw error;

      if (data.audio && audioRef.current) {
        audioRef.current.src = `data:audio/mp3;base64,${data.audio}`;
        await audioRef.current.play();
      }
    } catch (error) {
      console.error('Error speaking:', error);
      toast.error("Errore durante la sintesi vocale");
    } finally {
      setIsSpeaking(false);
    }
  };

  const cleanup = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.src = '';
    }
  };

  return {
    isSpeaking,
    speak,
    cleanup,
    audioRef
  };
};

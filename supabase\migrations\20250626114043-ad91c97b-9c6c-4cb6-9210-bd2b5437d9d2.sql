
-- Fix RLS policies for groups functionality (corrected version)
-- This migration creates proper RLS policies for groups, group_members, and related tables

-- Enable RLS on groups table
ALTER TABLE groups ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Groups are viewable by members" ON groups;
DROP POLICY IF EXISTS "Users can create groups" ON groups;
DROP POLICY IF EXISTS "Group creators can update their groups" ON groups;
DROP POLICY IF EXISTS "Group creators can delete their groups" ON groups;
DROP POLICY IF EXISTS "Users can view groups they are members of" ON groups;
DROP POLICY IF EXISTS "Authenticated users can create groups" ON groups;
DROP POLICY IF EXISTS "Group creators and admins can update groups" ON groups;
DROP POLICY IF EXISTS "Group creators can delete groups" ON groups;

-- Create groups policies
CREATE POLICY "Groups are viewable by members" ON groups
    FOR SELECT
    USING (
        auth.uid() IS NOT NULL AND (
            created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM group_members 
                WHERE group_members.group_id = groups.id 
                AND group_members.user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can create groups" ON groups
    FOR INSERT
    WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Group admins can update groups" ON groups
    FOR UPDATE
    USING (
        auth.uid() IS NOT NULL AND (
            created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM group_members 
                WHERE group_members.group_id = groups.id 
                AND group_members.user_id = auth.uid() 
                AND group_members.role = 'admin'
            )
        )
    );

CREATE POLICY "Group admins can delete groups" ON groups
    FOR DELETE
    USING (
        auth.uid() IS NOT NULL AND (
            created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM group_members 
                WHERE group_members.group_id = groups.id 
                AND group_members.user_id = auth.uid() 
                AND group_members.role = 'admin'
            )
        )
    );

-- Enable RLS on group_members table
ALTER TABLE group_members ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Group members are viewable by group members" ON group_members;
DROP POLICY IF EXISTS "Group admins can add members" ON group_members;
DROP POLICY IF EXISTS "Group admins can update members" ON group_members;
DROP POLICY IF EXISTS "Group admins and members can leave" ON group_members;
DROP POLICY IF EXISTS "Users can view members of their groups" ON group_members;
DROP POLICY IF EXISTS "Group admins and users can remove members" ON group_members;

-- Create group_members policies (simplified to avoid recursion)
CREATE POLICY "Group members are viewable by group members" ON group_members
    FOR SELECT
    USING (
        auth.uid() IS NOT NULL AND (
            user_id = auth.uid() OR
            group_id IN (
                SELECT id FROM groups WHERE created_by = auth.uid()
            ) OR
            group_id IN (
                SELECT gm.group_id FROM group_members gm 
                WHERE gm.user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Group admins can add members" ON group_members
    FOR INSERT
    WITH CHECK (
        auth.uid() IS NOT NULL AND (
            group_id IN (
                SELECT id FROM groups WHERE created_by = auth.uid()
            ) OR
            EXISTS (
                SELECT 1 FROM group_members gm 
                WHERE gm.group_id = group_members.group_id 
                AND gm.user_id = auth.uid() 
                AND gm.role = 'admin'
            )
        )
    );

CREATE POLICY "Group admins can update members" ON group_members
    FOR UPDATE
    USING (
        auth.uid() IS NOT NULL AND (
            group_id IN (
                SELECT id FROM groups WHERE created_by = auth.uid()
            ) OR
            EXISTS (
                SELECT 1 FROM group_members gm 
                WHERE gm.group_id = group_members.group_id 
                AND gm.user_id = auth.uid() 
                AND gm.role = 'admin'
            )
        )
    );

CREATE POLICY "Members can leave groups" ON group_members
    FOR DELETE
    USING (
        auth.uid() IS NOT NULL AND (
            user_id = auth.uid() OR
            group_id IN (
                SELECT id FROM groups WHERE created_by = auth.uid()
            ) OR
            EXISTS (
                SELECT 1 FROM group_members gm 
                WHERE gm.group_id = group_members.group_id 
                AND gm.user_id = auth.uid() 
                AND gm.role = 'admin'
            )
        )
    );

-- Create function to automatically add group creator as admin member
CREATE OR REPLACE FUNCTION add_group_creator_as_admin()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO group_members (group_id, user_id, role)
    VALUES (NEW.id, NEW.created_by, 'admin');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically add group creator as admin
DROP TRIGGER IF EXISTS add_group_creator_as_admin_trigger ON groups;
CREATE TRIGGER add_group_creator_as_admin_trigger
    AFTER INSERT ON groups
    FOR EACH ROW
    EXECUTE FUNCTION add_group_creator_as_admin();

-- Enable RLS on group_deal_shares table
ALTER TABLE group_deal_shares ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Group members can view deal shares" ON group_deal_shares;
DROP POLICY IF EXISTS "Group members can share deals" ON group_deal_shares;

CREATE POLICY "Group deal shares are viewable by group members" ON group_deal_shares
    FOR SELECT
    USING (
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM group_members 
            WHERE group_members.group_id = group_deal_shares.group_id 
            AND group_members.user_id = auth.uid()
        )
    );

CREATE POLICY "Group members can share deals" ON group_deal_shares
    FOR INSERT
    WITH CHECK (
        auth.uid() = shared_by AND
        EXISTS (
            SELECT 1 FROM group_members 
            WHERE group_members.group_id = group_deal_shares.group_id 
            AND group_members.user_id = auth.uid()
        )
    );

-- Enable RLS on group_booking_shares table
ALTER TABLE group_booking_shares ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Group members can view booking shares" ON group_booking_shares;
DROP POLICY IF EXISTS "Group members can share bookings" ON group_booking_shares;

CREATE POLICY "Group booking shares are viewable by group members" ON group_booking_shares
    FOR SELECT
    USING (
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM group_members 
            WHERE group_members.group_id = group_booking_shares.group_id 
            AND group_members.user_id = auth.uid()
        )
    );

CREATE POLICY "Group members can share bookings" ON group_booking_shares
    FOR INSERT
    WITH CHECK (
        auth.uid() = shared_by AND
        EXISTS (
            SELECT 1 FROM group_members 
            WHERE group_members.group_id = group_booking_shares.group_id 
            AND group_members.user_id = auth.uid()
        )
    );

import { motion } from "framer-motion";
import { useMemo } from "react";

interface ConversationVisualizerProps {
  isActive: boolean;
  visualizerType?: 'circle' | 'ring' | 'wave' | 'pulse';
  size?: number;
  label?: string;
  className?: string;
}

// Colors based on the screenshot - defined outside component to prevent recreation
const VISUALIZER_COLORS = [
  'from-cyan-400 to-blue-500', // Blue
  'from-purple-400 to-pink-500', // Purple/Pink
  'from-green-400 to-teal-500', // Green/Teal
  'from-brand-primary to-brand-secondary', // Brand colors
  'from-indigo-400 to-blue-500', // Indigo/Blue
];

/**
 * ConversationVisualizer Component
 *
 * Displays a single animated circular effect when a conversation is active,
 * inspired by audio visualizer designs with glowing edges and different colors.
 * Can be used to replace the microphone icon during active conversations.
 */
const ConversationVisualizer: React.FC<ConversationVisualizerProps> = ({
  isActive,
  visualizerType = 'wave',
  size = 80,
  label,
  className = ''
}) => {
  // Use a memoized color to prevent re-renders
  const color = useMemo(() => {
    const colorIndex = Math.floor(Math.random() * VISUALIZER_COLORS.length);
    return VISUALIZER_COLORS[colorIndex];
  }, []);

  if (!isActive) return null;

  return (
    <div className={`relative flex items-center justify-center ${className}`}>
      {/* Main visualizer circle with glow effect */}
      <motion.div
        className={`rounded-full bg-gradient-to-r ${color} shadow-lg flex items-center justify-center`}
        style={{
          width: size,
          height: size,
          boxShadow: `0 0 15px rgba(0, 255, 255, 0.5), 0 0 30px rgba(0, 200, 255, 0.3)`,
        }}
        
        initial={{ scale: 0.5, opacity: 0.9 }}
        // animate={{
        //   scale: [0.95, 1.05, 0.95],
        //   opacity: 0.9,
        // }}
        // transition={{
        //   duration: 3,
        //   repeat: Infinity,
        //   ease: "easeInOut"
        // }}
      >
        {/* Inner content based on visualizer type */}
        {visualizerType === 'circle' && (
          <motion.div
            className="w-3/4 h-3/4 rounded-full bg-cyan-400/50 backdrop-blur-sm flex items-center justify-center"
            animate={{
              scale: [0.9, 1.1, 0.9],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            {label && (
              <span className="text-white text-sm font-medium">{label}</span>
            )}
          </motion.div>
        )}

        {visualizerType === 'ring' && (
          <>
            <motion.div
              className="absolute inset-0 rounded-full border-4 border-cyan-300"
              animate={{
                scale: [0.9, 1.1, 0.9],
                opacity: [0.7, 0.3, 0.7],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <div className="w-3/4 h-3/4 rounded-full bg-cyan-900/30 backdrop-blur-sm flex items-center justify-center">
              {label && (
                <span className="text-white text-sm font-medium">{label}</span>
              )}
            </div>
          </>
        )}

        {visualizerType === 'wave' && (
          <>
            {/* Animated wave lines */}
            <div className="absolute inset-0 flex items-center justify-center">
              {[...Array(12)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 bg-cyan-300/70"
                  style={{
                    height: '60%',
                    transform: `rotate(${i * 30}deg)`,
                    transformOrigin: "50% 50%",
                  }}
                  animate={{
                    height: ['60%', '30%', '60%'],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.1,
                  }}
                />
              ))}
            </div>
            <div className="w-1/2 h-1/2 rounded-full bg-cyan-400/50 backdrop-blur-sm z-10 flex items-center justify-center">
              {label && (
                <span className="text-white text-xs font-medium">{label}</span>
              )}
            </div>
          </>
        )}

        {visualizerType === 'pulse' && (
          <>
            {/* Pulsing rings */}
            {[1, 2, 3].map((ring) => (
              <motion.div
                key={ring}
                className="absolute inset-0 rounded-full border-2 border-green-300/70"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 0, 0.7],
                }}
                transition={{
                  duration: 2,
                  delay: ring * 0.2,
                  repeat: Infinity,
                }}
              />
            ))}
            <div className="w-3/5 h-3/5 rounded-full bg-gradient-to-b from-green-400/60 to-cyan-400/60 backdrop-blur-sm flex items-center justify-center">
              {label && (
                <span className="text-white text-xs font-medium">{label}</span>
              )}
            </div>
          </>
        )}
      </motion.div>
    </div>
  );
};

export default ConversationVisualizer;

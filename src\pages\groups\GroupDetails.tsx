
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { ArrowLeft, Users, Settings, Share2, UserPlus, X, LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import BottomNavigationBar from '@/components/toolbars/BottomNavigationBar';
import { useBusinessMode } from '@/hooks/useBusinessMode';
import { useGroupDetails } from '@/hooks/group/useGroupDetails';
import { useGroupInvites, useRevokeInvite } from '@/hooks/group/useGroupInvites';
import { useLeaveGroup } from '@/hooks/group/useGroups';
import { InviteGroupMembersDialog } from '@/components/groups/InviteGroupMembersDialog';
import { useState } from 'react';
import UnifiedHeader from '@/components/toolbars/UnifiedHeader';

const GroupDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { isBusinessMode } = useBusinessMode();
  const [showInviteDialog, setShowInviteDialog] = useState(false);

  const { data: group, isLoading, error } = useGroupDetails(id!);
  const { data: pendingInvites } = useGroupInvites(id!);
  const { mutate: revokeInvite, isPending: isRevoking } = useRevokeInvite();
  const { mutate: leaveGroup, isPending: isLeaving } = useLeaveGroup();

  const handleBack = () => {
    navigate('/groups');
  };

  const handleInviteMembers = () => {
    setShowInviteDialog(true);
  };

  const handleRevokeInvite = (inviteId: string) => {
    revokeInvite(inviteId);
  };

  const handleLeaveGroup = () => {
    if (confirm('Sei sicuro di voler lasciare questo gruppo?')) {
      leaveGroup(id!, {
        onSuccess: () => {
          navigate('/groups');
        }
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <UnifiedHeader title="Caricamento..." isBusiness={isBusinessMode} />
        <main className="pt-16 pb-20 px-4">
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-500">Caricamento dettagli gruppo...</div>
          </div>
        </main>
        <BottomNavigationBar isBusiness={isBusinessMode} />
      </div>
    );
  }

  if (error || !group) {
    return (
      <div className="min-h-screen bg-gray-50">
        <UnifiedHeader title="Errore" isBusiness={isBusinessMode} />
        <main className="pt-16 pb-20 px-4">
          <div className="flex items-center justify-center h-64">
            <div className="text-red-500">Errore nel caricamento del gruppo</div>
          </div>
        </main>
        <BottomNavigationBar isBusiness={isBusinessMode} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <UnifiedHeader title={group.name} isBusiness={isBusinessMode} />

      <main className="pt-16 pb-20 px-4">
        {/* Header con pulsante indietro */}
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="sm" onClick={handleBack} className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Indietro
          </Button>
        </div>

        {/* Informazioni gruppo */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 rounded-full bg-brand-light flex items-center justify-center overflow-hidden">
                {group.avatar_url ? (
                  <img 
                    src={group.avatar_url} 
                    alt={group.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <Users className="h-8 w-8 text-brand-primary" />
                )}
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <CardTitle className="text-xl">{group.name}</CardTitle>
                  {group.user_role === 'admin' && (
                    <Badge variant="secondary" className="bg-brand-light text-brand-primary">
                      Admin
                    </Badge>
                  )}
                </div>
                <p className="text-gray-600 text-sm">
                  {group.member_count} membri
                </p>
              </div>
            </div>
            {group.description && (
              <p className="text-gray-700 mt-4">{group.description}</p>
            )}
          </CardHeader>
        </Card>

        {/* Azioni gruppo */}
        {group.user_role === 'admin' && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Azioni Gruppo</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={handleInviteMembers}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Invita Membri
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Share2 className="h-4 w-4 mr-2" />
                Condividi Gruppo
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Settings className="h-4 w-4 mr-2" />
                Impostazioni Gruppo
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Azioni per membri non-admin */}
        {group.user_role === 'member' && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Azioni</CardTitle>
            </CardHeader>
            <CardContent>
              <Button 
                variant="outline" 
                className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                onClick={handleLeaveGroup}
                disabled={isLeaving}
              >
                <LogOut className="h-4 w-4 mr-2" />
                {isLeaving ? 'Uscendo...' : 'Lascia Gruppo'}
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Inviti in sospeso */}
        {group.user_role === 'admin' && pendingInvites && pendingInvites.length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Inviti in Sospeso</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {pendingInvites.map((invite) => (
                  <div key={invite.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={invite.invited_user.avatar_url || undefined} />
                        <AvatarFallback>
                          {invite.invited_user.first_name.charAt(0)}{invite.invited_user.last_name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-gray-900">
                          {invite.invited_user.first_name} {invite.invited_user.last_name}
                        </p>
                        <p className="text-sm text-gray-500">{invite.invited_user.email}</p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRevokeInvite(invite.id)}
                      disabled={isRevoking}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Lista membri */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Membri del Gruppo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {group.members.map((member) => (
                <div key={member.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={member.avatar_url || undefined} />
                      <AvatarFallback>
                        {member.first_name.charAt(0)}{member.last_name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-gray-900">
                        {member.first_name} {member.last_name}
                      </p>
                    </div>
                  </div>
                  {member.role === 'admin' && (
                    <Badge variant="secondary" className="bg-brand-light text-brand-primary text-xs">
                      Admin
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </main>

      <BottomNavigationBar isBusiness={isBusinessMode} />
      
      {/* Dialogo di invito */}
      <InviteGroupMembersDialog
        open={showInviteDialog}
        onOpenChange={setShowInviteDialog}
        groupId={id!}
        groupName={group.name}
      />
    </div>
  );
};

export default GroupDetails;

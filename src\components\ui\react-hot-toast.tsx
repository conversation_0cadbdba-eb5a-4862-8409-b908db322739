import { useTheme } from "next-themes";
import { Toaster } from "react-hot-toast";

type ReactHotToasterProps = {
  position?: 
    | "top-left" 
    | "top-center" 
    | "top-right" 
    | "bottom-left" 
    | "bottom-center" 
    | "bottom-right";
  duration?: number;
  maxToasts?: number;
  reverseOrder?: boolean;
  gutter?: number;
  containerClassName?: string;
  containerStyle?: React.CSSProperties;
  toastOptions?: {
    duration?: number;
    style?: React.CSSProperties;
    className?: string;
    success?: {
      duration?: number;
      style?: React.CSSProperties;
      iconTheme?: {
        primary?: string;
        secondary?: string;
      };
    };
    error?: {
      duration?: number;
      style?: React.CSSProperties;
      iconTheme?: {
        primary?: string;
        secondary?: string;
      };
    };
    loading?: {
      duration?: number;
      style?: React.CSSProperties;
    };
  };
};

const ReactHotToaster = ({ 
  position = "top-center",
  duration = 4000,
  maxToasts = 5,
  reverseOrder = false,
  gutter = 8,
  containerClassName = "",
  containerStyle = {},
  toastOptions = {},
  ...props 
}: ReactHotToasterProps) => {
  const { theme = "system" } = useTheme();
  
  // Determine if dark mode
  const isDark = theme === "dark" || 
    (theme === "system" && window.matchMedia("(prefers-color-scheme: dark)").matches);

  // Default styles based on theme
  const defaultStyle = {
    background: isDark ? "#363636" : "#ffffff",
    color: isDark ? "#ffffff" : "#000000",
    border: `1px solid ${isDark ? "#4a5568" : "#e2e8f0"}`,
    borderRadius: "8px",
    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
    fontSize: "14px",
    fontWeight: "500",
    ...toastOptions.style,
  };

  const mergedToastOptions = {
    duration: toastOptions.duration || duration,
    style: defaultStyle,
    className: toastOptions.className || "",
    
    // Success toast styling
    success: {
      duration: toastOptions.success?.duration || 3000,
      style: {
        ...defaultStyle,
        ...toastOptions.success?.style,
      },
      iconTheme: {
        primary: toastOptions.success?.iconTheme?.primary || "#10b981",
        secondary: toastOptions.success?.iconTheme?.secondary || (isDark ? "#363636" : "#ffffff"),
      },
    },
    
    // Error toast styling  
    error: {
      duration: toastOptions.error?.duration || 4000,
      style: {
        ...defaultStyle,
        ...toastOptions.error?.style,
      },
      iconTheme: {
        primary: toastOptions.error?.iconTheme?.primary || "#ef4444",
        secondary: toastOptions.error?.iconTheme?.secondary || (isDark ? "#363636" : "#ffffff"),
      },
    },
    
    // Loading toast styling
    loading: {
      duration: toastOptions.loading?.duration || Infinity,
      style: {
        ...defaultStyle,
        ...toastOptions.loading?.style,
      },
    },
  };

  return (
    <Toaster
      position={position}
      reverseOrder={reverseOrder}
      gutter={gutter}
      containerClassName={containerClassName}
      containerStyle={containerStyle}
      toastOptions={mergedToastOptions}
      {...props}
    />
  );
};

export { ReactHotToaster }; 
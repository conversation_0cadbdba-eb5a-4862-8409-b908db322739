
import { Users, UserPlus, X, <PERSON> } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { useReceivedInvites, useAcceptInvite, useDeclineInvite } from '@/hooks/group/useReceivedInvites';

const GroupInvitesSection = () => {
  const { data: receivedInvites = [], isLoading } = useReceivedInvites();
  const { mutate: acceptInvite, isPending: isAccepting } = useAcceptInvite();
  const { mutate: declineInvite, isPending: isDeclining } = useDeclineInvite();

  if (isLoading || receivedInvites.length === 0) {
    return null;
  }

  const handleAccept = (inviteId: string) => {
    acceptInvite(inviteId);
  };

  const handleDecline = (inviteId: string) => {
    declineInvite(inviteId);
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <UserPlus className="h-5 w-5 text-brand-primary" />
          Inviti Gruppo ({receivedInvites.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {receivedInvites.map((invite) => (
            <div key={invite.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
              {/* Group info */}
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-12 h-12 rounded-full bg-brand-light flex items-center justify-center overflow-hidden">
                  {invite.group?.avatar_url ? (
                    <img 
                      src={invite.group.avatar_url} 
                      alt={invite.group?.name || 'Gruppo'}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Users className="h-6 w-6 text-brand-primary" />
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{invite.group?.name || 'Gruppo senza nome'}</h3>
                  {invite.group?.description && (
                    <p className="text-sm text-gray-600 mt-1">{invite.group.description}</p>
                  )}
                </div>
              </div>

              {/* Invitation info */}
              <div className="flex items-center space-x-2 mb-4">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={invite.invited_by_user?.avatar_url || undefined} />
                  <AvatarFallback className="text-xs">
                    {invite.invited_by_user?.first_name?.charAt(0) || 'U'}
                    {invite.invited_by_user?.last_name?.charAt(0) || ''}
                  </AvatarFallback>
                </Avatar>
                <p className="text-sm text-gray-600">
                  Invitato da <span className="font-medium">
                    {invite.invited_by_user ? 
                      `${invite.invited_by_user.first_name || ''} ${invite.invited_by_user.last_name || ''}`.trim() || 'Utente sconosciuto'
                      : 'Utente sconosciuto'
                    }
                  </span>
                </p>
              </div>

              {/* Action buttons */}
              <div className="flex gap-2">
                <Button
                  onClick={() => handleAccept(invite.id)}
                  disabled={isAccepting || isDeclining}
                  size="sm"
                  className="flex-1"
                >
                  <Check className="h-4 w-4 mr-1" />
                  {isAccepting ? 'Accettando...' : 'Accetta'}
                </Button>
                <Button
                  onClick={() => handleDecline(invite.id)}
                  disabled={isAccepting || isDeclining}
                  variant="outline"
                  size="sm"
                  className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                >
                  <X className="h-4 w-4 mr-1" />
                  {isDeclining ? 'Rifiutando...' : 'Rifiuta'}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default GroupInvitesSection;


import React, { FunctionComponent, useState } from "react";
import { AdvancedMarker } from "@vis.gl/react-google-maps";
import { cn } from "@/lib/utils";
import { Tag, MapPin, Store, Star } from "lucide-react";

import "./custom-business-marker.css";

/**
 * CustomBusinessMarker props
 * @interface Props
 * @property {Business} business - The business data to display in the marker
 * @property {Function} onBusinessClick - Optional callback for when the business marker is clicked
 * @property {Function} onDealClick - Optional callback for when a deal within the business marker is clicked
 */
interface Props {
  business: {
    id: string;
    name: string;
    latitude: number | null;
    longitude: number | null;
    deal_count: number | null;
    deals?: any[];
    score?: number | null;
    review_count?: number | null;
  };
  onBusinessClick?: (business: any) => void;
  onDealClick?: (dealId: string) => void;
}

/**
 * A custom advanced marker component for businesses on the map
 *
 * Features:
 * - Shows business name and deal count in collapsed state
 * - Expands to show more details and deals when clicked
 * - Hover and click animations
 *
 * @example
 * ```tsx
 * <CustomBusinessMarker
 *   business={businessData}
 *   onBusinessClick={handleBusinessClick}
 *   onDealClick={handleDealClick}
 * />
 * ```
 */
export const CustomBusinessMarker: FunctionComponent<Props> = ({
  business,
  onBusinessClick,
  onDealClick,
}) => {
  const [clicked, setClicked] = useState(false);
  const [hovered, setHovered] = useState(false);

  if (!business.latitude || !business.longitude) return null;

  const position = {
    lat: business.latitude,
    lng: business.longitude,
  };

  const handleClick = () => {
    setClicked(!clicked);
    if (onBusinessClick) {
      onBusinessClick(business);
    }
  };

  const handleDealClick = (e: React.MouseEvent, dealId: string) => {
    e.stopPropagation();
    if (onDealClick) {
      onDealClick(dealId);
    }
  };

  const renderCustomPin = () => {
    return (
      <div className="relative">
        {/* Deal count badge - positioned at the top */}
        {business.deal_count && business.deal_count > 0 && (
          <div className="absolute -top-2 -right-2 bg-brand-primary text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center z-10">
            {business.deal_count}
          </div>
        )}
        
        {/* Main pin body */}
        <div className={cn(
          "flex flex-col items-center",
          clicked && "z-30"
        )}>
          {/* Pin head */}
          <div 
            className={cn(
              "rounded-full bg-white border border-gray-300 shadow-md transition-all duration-200 flex items-center justify-center",
              clicked 
                ? "w-14 h-14" 
                : hovered
                  ? "w-12 h-12"
                  : "w-10 h-10",
            )}
          >
            <Store className={cn(
              "text-brand-primary transition-all duration-200",
              clicked 
                ? "h-7 w-7" 
                : hovered
                  ? "h-6 w-6"
                  : "h-5 w-5"
            )} />
          </div>
          
          {/* Pin body - the tail part */}
          <div className="h-3 w-3 bg-white rotate-45 -mt-1.5 shadow-md border-r border-b border-gray-300"></div>
          
          {/* Expanded deal info - only visible when clicked */}
          {clicked && business.deals && business.deals.length > 0 && (
            <div className="absolute top-16 left-1/2 -translate-x-1/2 w-[250px] bg-white rounded-lg shadow-lg p-3 border border-gray-200 z-20 animate-fade-in">
              <button 
                className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
                onClick={(e) => {
                  e.stopPropagation();
                  setClicked(false);
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
              
              <h3 className="font-semibold text-gray-800 mb-2 pr-6">{business.name}</h3>
              
              <div className="mb-2 pb-2 border-b border-gray-200 space-y-1">
                <div className="flex items-center text-brand-primary text-xs font-medium">
                  <Tag className="h-4 w-4 mr-1" />
                  {business.deal_count} offerte disponibili 
                </div>
              
              </div>
              
              <div className="max-h-[200px] overflow-y-auto mt-1">
                {business.deals.map(deal => (
                  <div 
                    key={deal.id} 
                    onClick={(e) => handleDealClick(e, deal.id)}
                    className="p-2 mb-2 bg-gray-50 rounded hover:bg-gray-100 transition-colors cursor-pointer"
                  >
                    <div className="font-semibold text-sm mb-1 text-gray-800">{deal.title}</div>
                    <div className="text-xs text-gray-600">{deal.description}</div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <AdvancedMarker
      position={position}
      title={business.name}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      onClick={handleClick}
      zIndex={clicked ? 1000 : 1}
      className={cn(
        "cursor-pointer",
        clicked || hovered ? "-translate-y-1" : "",
        "transition-transform duration-200"
      )}
    >
      {renderCustomPin()}
    </AdvancedMarker>
  );
};

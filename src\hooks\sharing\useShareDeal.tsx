import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface ShareDealParams {
  dealId: string;
  groupIds: string[];
  message?: string;
}

export const useShareDeal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ dealId, groupIds, message }: ShareDealParams) => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Insert shares for each selected group
      const sharesToInsert = groupIds.map(groupId => ({
        deal_id: dealId,
        group_id: groupId,
        shared_by: user.id,
        message: message || null,
      }));

      const { data, error } = await supabase
        .from('group_deal_shares')
        .insert(sharesToInsert)
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: (data, variables) => {
      // Invalidate social feed to show new shares
      queryClient.invalidateQueries({ queryKey: ['social-feed'] });
      
      const groupCount = variables.groupIds.length;
      toast.success(
        groupCount === 1 
          ? 'Offerta condivisa nel gruppo!'
          : `Offerta condivisa in ${groupCount} gruppi!`
      );
    },
    onError: (error) => {
      console.error('Error sharing deal:', error);
      toast.error('Errore nella condivisione dell\'offerta');
    },
  });
};
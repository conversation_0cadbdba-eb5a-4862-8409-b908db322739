import { LogIn } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";

import { FC } from "react";
import { useNavigate } from "react-router-dom";

const LoginPrompt: FC = () => {
  const navigate = useNavigate();

  return (
    <div className="bg-white rounded-lg shadow p-4 mb-6 text-center">
      <h3 className="text-lg font-semibold mb-2">
        Accedi per vedere i tuoi contenuti personalizzati
      </h3>
      <p className="text-gray-600 mb-4">
        Accedi per visualizzare le tue prenotazioni, offerte preferite e altro ancora.
      </p>
      <Button
        onClick={() => navigate("/login")}
        className="bg-brand-primary hover:bg-brand-primary-dark text-white"
      >
        <LogIn className="h-4 w-4 mr-2" />
        Accedi
      </Button>
    </div>
  );
};

export default LoginPrompt;

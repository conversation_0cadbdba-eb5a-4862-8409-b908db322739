import { useState, useEffect } from 'react';

interface PWAUpdateState {
  updateAvailable: boolean;
  isUpdating: boolean;
  updateAndReload: () => void;
  dismissUpdate: () => void;
}

export const usePWAUpdate = (): PWAUpdateState => {
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [waitingWorker, setWaitingWorker] = useState<ServiceWorker | null>(null);

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      const registration = navigator.serviceWorker.getRegistration();
      
      registration.then((reg) => {
        if (reg) {
          // Check if there's a waiting service worker
          if (reg.waiting) {
            setWaitingWorker(reg.waiting);
            setUpdateAvailable(true);
          }

          // Listen for service worker updates
          reg.addEventListener('updatefound', () => {
            const newWorker = reg.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  setWaitingWorker(newWorker);
                  setUpdateAvailable(true);
                }
              });
            }
          });


        }
      });

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'SKIP_WAITING') {
       
        window.location.reload();
          
        }
      });
    }
  }, []);

  const updateAndReload = () => {
    if (waitingWorker) {
      setIsUpdating(true);
       // Listen for the service worker to become active
    waitingWorker.addEventListener('statechange', () => {
      if (waitingWorker.state === 'activated') {
        setIsUpdating(false);
        // Small delay to show completion state before reload
        setTimeout(() => {
          window.location.reload();
        }, 500);
      }
    });
      waitingWorker.postMessage({ type: 'SKIP_WAITING' });
    }
  };

  const dismissUpdate = () => {
    setUpdateAvailable(false);
    setWaitingWorker(null);
  };

  return {
    updateAvailable,
    isUpdating,
    updateAndReload,
    dismissUpdate,
  };
}; 
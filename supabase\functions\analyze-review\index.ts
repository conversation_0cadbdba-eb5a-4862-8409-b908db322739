import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { comment, rating } = await req.json();

    if (!comment || rating === undefined) {
      return new Response(
        JSON.stringify({ 
          blocked: true,
          error: 'Comment and rating are required' 
        }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    console.log('Analyzing review:', { comment, rating });

    // Step 1: Check for offensive content
    const moderationResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: `You are a content moderator for CatchUp, a business review platform. 
            Analyze the following review for inappropriate content including:
            - Offensive language, profanity, or hate speech
            - Personal attacks or harassment
            - Spam or irrelevant content
            - Discriminatory language
            - Threats or violence
            
            Respond with only "APPROVED" if the content is appropriate, or "BLOCKED" if it violates policies.
            Be strict but fair - legitimate negative reviews should be approved even if critical.`
          },
          {
            role: 'user',
            content: `Review text: "${comment}"\nRating: ${rating}/5`
          }
        ],
        temperature: 0.1,
        max_tokens: 10
      }),
    });

    if (!moderationResponse.ok) {
      throw new Error('Failed to moderate content');
    }

    const moderationData = await moderationResponse.json();
    const moderationResult = moderationData.choices[0].message.content.trim();

    console.log('Moderation result:', moderationResult);

    if (moderationResult === 'BLOCKED') {
      return new Response(
        JSON.stringify({ 
          blocked: true,
          error: 'La recensione contiene contenuti che non rispettano le politiche di CatchUp. Per favore, modifica il testo e riprova.'
        }),
        { 
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Step 2: Perform sentiment analysis
    const sentimentResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: `Analyze the sentiment of the following business review. 
            Consider both the text content and the numerical rating (1-5 stars).
            
            Respond with one of these exact values:
            - very_negative: Extremely dissatisfied, very harsh criticism, rating 1-1.5
            - negative: Dissatisfied, clear problems mentioned, rating 2-2.5  
            - neutral: Balanced review, mixed feelings, rating 3
            - positive: Satisfied, mostly positive feedback, rating 4-4.5
            - very_positive: Extremely satisfied, glowing review, rating 5
            - mixed: Conflicting sentiments in text vs rating, or very contradictory content
            
            Focus on the overall tone and satisfaction level expressed.`
          },
          {
            role: 'user',
            content: `Review text: "${comment}"\nRating: ${rating}/5`
          }
        ],
        temperature: 0.1,
        max_tokens: 20
      }),
    });

    if (!sentimentResponse.ok) {
      throw new Error('Failed to analyze sentiment');
    }

    const sentimentData = await sentimentResponse.json();
    const sentiment = sentimentData.choices[0].message.content.trim();

    console.log('Sentiment analysis result:', sentiment);

    // Validate sentiment value
    const validSentiments = ['very_negative', 'negative', 'neutral', 'positive', 'very_positive', 'mixed'];
    const finalSentiment = validSentiments.includes(sentiment) ? sentiment : 'neutral';

    return new Response(
      JSON.stringify({ 
        approved: true,
        sentiment: finalSentiment 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error in analyze-review function:', error);
    return new Response(
      JSON.stringify({ error: 'Errore interno del server. Riprova più tardi.' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
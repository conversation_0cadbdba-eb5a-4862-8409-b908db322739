
import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface Business {
  id: string;
  name: string;
  address: string;
  formatted_address?: string;
  zip_code: string;
  city: string;
  state: string;
  country: string;
  description: string | null;
  created_at: string;
  updated_at: string | null;
  latitude: number | null;
  longitude: number | null;
  photos: string[] | null;
  owner_id: string;
  category_id: string | null;
  email?: string | null;
  phone?: string | null;
  website?: string | null;
  deal_count?: number | null;
  booking_count?: number | null;
  pending_booking_count?: number | null;
  score?: number | null;
  review_count?: number | null;
}

export const useBusinesses = (userId?: string, enable = true) => {
  const [userBusinesses, setUserBusinesses] = useState<Business[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);



  const fetchBusinesses = async () => {
    if (!userId || !enable) {
      setUserBusinesses([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Utilizziamo businesses che è la tabella principale
      const { data, error: fetchError } = await supabase
        .from('businesses')
        .select(`
          id, name, address, description, created_at, updated_at, 
          latitude, longitude, photos, owner_id, category_id,
          email, phone, website, score, review_count
        `)
        .eq('owner_id', userId);

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      setUserBusinesses(data as Business[] || []);
    } catch (err: any) {
      console.error('Error fetching businesses:', err);
      setError(err);
      toast.error("Errore nel caricamento delle attività");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBusinesses();
  }, [userId, enable]);

  return {
    userBusinesses,
    isLoading,
    error,
    refreshBusinesses: fetchBusinesses
  };
};

export const useBusinessById = (businessId: string) => {
  const {
    data: business,
    isLoading,
    error: queryError,
  } = useQuery({
    queryKey: ['business', businessId],
    queryFn: async (): Promise<Business | null> => {
      const { data, error: fetchError } = await supabase
        .from('businesses')
        .select('*')
        .eq('id', businessId)
        .single();
      
      if (fetchError) {
        throw new Error(fetchError.message);
      }
      
      return data as Business || null;
    },
    enabled: !!businessId,
   // staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1,
  });

  // Convert React Query error to Error type for compatibility
  const error = queryError ? new Error(queryError.message || 'Unknown error') : null;

  return {
    business: business || null,
    isLoading,
    error,
  };
};
Stack trace:
Frame         Function      Args
0007FFFF8B70  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF7A70) msys-2.0.dll+0x2118E
0007FFFF8B70  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF8B70  0002100469F2 (00021028DF99, 0007FFFF8A28, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8B70  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF8B70  00021006A545 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8580A0000 ntdll.dll
7FF857EE0000 KERNEL32.DLL
7FF855A20000 KERNELBASE.dll
7FF857BC0000 USER32.dll
7FF855870000 win32u.dll
7FF857400000 GDI32.dll
7FF8553D0000 gdi32full.dll
000210040000 msys-2.0.dll
7FF855DF0000 msvcp_win.dll
7FF855280000 ucrtbase.dll
7FF856090000 advapi32.dll
7FF857A30000 msvcrt.dll
7FF857FB0000 sechost.dll
7FF857910000 RPCRT4.dll
7FF854780000 CRYPTBASE.DLL
7FF855510000 bcryptPrimitives.dll
7FF857440000 IMM32.DLL

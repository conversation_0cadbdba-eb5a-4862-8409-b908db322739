Stack trace:
Frame         Function      Args
0007FFFF9DA0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8CA0) msys-2.0.dll+0x2118E
0007FFFF9DA0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9DA0  0002100469F2 (00021028DF99, 0007FFFF9C58, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9DA0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9DA0  00021006A545 (0007FFFF9DB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9DB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFDA2A0000 ntdll.dll
7FFFD9880000 KERNEL32.DLL
7FFFD7760000 KERNELBASE.dll
7FFFD8A50000 USER32.dll
7FFFD7540000 win32u.dll
000210040000 msys-2.0.dll
7FFFD8590000 GDI32.dll
7FFFD7620000 gdi32full.dll
7FFFD7570000 msvcp_win.dll
7FFFD73F0000 ucrtbase.dll
7FFFD8450000 advapi32.dll
7FFFD9B90000 msvcrt.dll
7FFFD9F30000 sechost.dll
7FFFD9FE0000 RPCRT4.dll
7FFFD69F0000 CRYPTBASE.DLL
7FFFD7C10000 bcryptPrimitives.dll
7FFFDA220000 IMM32.DLL

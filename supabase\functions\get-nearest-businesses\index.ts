
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";
import { corsHeaders } from "../_shared/cors.ts";

// Creiamo una cache semplice per evitare chiamate ripetute al database
interface CacheItem {
  timestamp: number;
  data: any;
}

const cache: Record<string, CacheItem> = {};
const CACHE_TTL = 10 * 60 * 1000; // 10 minuti in millisecondi
const DEFAULT_LIMIT = 5;

serve(async (req) => {
  // Gestiamo le richieste CORS preflight
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const url = new URL(req.url);
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Estrai i parametri dalla richiesta
    let params;
    try {
      params = await req.json();
    } catch (e) {
      // Se la richiesta non contiene un JSON valido, usiamo i parametri URL
      params = Object.fromEntries(url.searchParams);
    }
    console.log("params", params);
    // Valori predefiniti se i parametri non sono forniti
    const {
      latitude,
      longitude,
      limit = DEFAULT_LIMIT,
      transportMode = "DRIVING",
      useDirections = false,
      maxDistance_meters = 5000,
     // deal_count = false,
      require_deals = false,
    } = params;

    if (!latitude || !longitude) {
      return new Response(
        JSON.stringify({
          error: "Latitudine e longitudine sono obbligatorie",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // Crea una chiave di cache basata sui parametri
    const cacheKey = `${latitude}_${longitude}_${limit}_${transportMode}_${useDirections}_${require_deals}`;

    // Controlla se abbiamo dati in cache
    const cached = cache[cacheKey];
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      console.log("Dati restituiti dalla cache");
      return new Response(JSON.stringify(cached.data), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    try {
      // Utilizziamo la funzione PostGIS per ottenere i business più vicini
      const { data: businesses, error } = await supabase.rpc(
        "get_nearest_businesses_postgis",
        {
          user_lat: latitude,
          user_lon: longitude,
          max_distance_km: maxDistance_meters/1000, // in KM
          max_results: limit,
          require_deals: require_deals,
        }
      );
     
      if (error) {
        console.error("Errore nella query PostGIS:", error);
        throw new Error(`Errore nella query PostGIS: ${JSON.stringify(error)}`);
      }

      // let businesses_filtered = businesses;
      // if (deal_count && !require_deals) {
      //   businesses_filtered = businesses.filter((business: any) => business.deal_count > 0);
      // }
      
      // Formatta i dati per l'output
      const formattedBusinesses = businesses.map((business: any) => {
        // Calcola il testo della distanza in un formato leggibile
        let distanceText = "";
        if (business.distance < 1000) {
          distanceText = `${Math.round(business.distance)} m`;
        } else {
          distanceText = `${(business.distance / 1000).toFixed(1)} km`;
        }

        return {
          id: business.id,
          name: business.name,
          latitude: business.latitude,
          longitude: business.longitude,
          deal_count: business.deal_count,
          address: business.address,
          distance: business.distance,
          distanceText,
          photos: business.photos || [],
        };
      });

      // Aggiungi dettagli di routing da Google Maps API se richiesto
      if (useDirections && formattedBusinesses.length > 0) {
        try {
          // In futuro, qui si potrebbe implementare una chiamata a Google Maps Directions API
          console.log("Calcolo dettagli routing con Google Maps API richiesto ma non implementato");
        } catch (mapsError) {
          console.error("Errore durante il calcolo delle direzioni:", mapsError);
          // Continua senza dettagli di routing
        }
      }

      // Prepara la risposta
      const response = {
        businesses: formattedBusinesses,
        nearestBusiness: formattedBusinesses.length > 0 ? formattedBusinesses[0] : null,
      };

      // Salva nella cache
      cache[cacheKey] = {
        timestamp: Date.now(),
        data: response,
      };

      // Restituisci la risposta
      return new Response(JSON.stringify(response), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    } catch (postgisError) {
      console.error("Errore PostGIS:", postgisError);

      // Modello di risposta vuota
      const emptyResponse = {
        businesses: [],
        nearestBusiness: null,
      };

      return new Response(JSON.stringify(emptyResponse), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      });
    }
  } catch (e) {
    console.error("Errore imprevisto:", e);
    return new Response(
      JSON.stringify({ error: "Errore imprevisto", details: e.message }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});

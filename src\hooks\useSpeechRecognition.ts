import { useRef, useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';

export const useSpeechRecognition = (onTranscript: (text: string, isFinal: boolean) => void) => {
  const [isListening, setIsListening] = useState(false);
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const restartTimeoutRef = useRef<number | null>(null);

  const cleanup = useCallback(() => {
    if (restartTimeoutRef.current) {
      window.clearTimeout(restartTimeoutRef.current);
      restartTimeoutRef.current = null;
    }
    if (recognitionRef.current) {
      recognitionRef.current.onresult = null;
      recognitionRef.current.onstart = null;
      recognitionRef.current.onend = null;
      recognitionRef.current.onerror = null;
      try {
        recognitionRef.current.stop();
      } catch (e) {
        // Ignora errori durante lo stop
      }
      recognitionRef.current = null;
    }
    setIsListening(false);
  }, []);

  const initializeRecognition = useCallback(() => {
    if (recognitionRef.current) return;

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    recognitionRef.current = new SpeechRecognition();
    recognitionRef.current.lang = 'it-IT';
    recognitionRef.current.continuous = true;
    recognitionRef.current.interimResults = true;

    recognitionRef.current.onresult = (event) => {
      try {
        const lastResult = event.results[event.results.length - 1];
        const transcript = lastResult[0].transcript;
        
        console.log('Transcript corrente:', transcript);
        console.log('Confidence:', lastResult[0].confidence);
        
        onTranscript(transcript, lastResult.isFinal);
        
        if (lastResult.isFinal) {
          console.log('Transcript finale:', transcript);
        }
      } catch (error) {
        console.error('Errore durante elaborazione risultato:', error);
      }
    };

    recognitionRef.current.onstart = () => {
      console.log('Riconoscimento vocale avviato');
      setIsListening(true);
    };

    recognitionRef.current.onend = () => {
      console.log('Riconoscimento vocale terminato');
      setIsListening(false);
      
      // Riavvia automaticamente il riconoscimento se era attivo
      if (isListening) {
        console.log('Tentativo di riavvio automatico...');
        restartTimeoutRef.current = window.setTimeout(() => {
          try {
            recognitionRef.current?.start();
          } catch (error) {
            console.error('Errore durante il riavvio:', error);
            cleanup();
          }
        }, 1000);
      }
    };

    recognitionRef.current.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      console.error('Error details:', event);
      
      if (event.error === 'not-allowed') {
        toast.error("Permesso microfono negato. Controlla le impostazioni del browser.");
        cleanup();
      } else if (event.error === 'network') {
        toast.error("Errore di rete. Verificare la connessione.");
        setTimeout(() => startListening(), 3000);
      } else {
        toast.error("Errore nel riconoscimento vocale");
        setTimeout(() => startListening(), 1000);
      }
    };
  }, [isListening, cleanup, onTranscript]);

  const requestMicrophonePermission = async () => {
    try {
      await navigator.mediaDevices.getUserMedia({ audio: true });
      return true;
    } catch (error) {
      console.error('Errore accesso microfono:', error);
      toast.error("Per favore concedi l'accesso al microfono nelle impostazioni del browser");
      return false;
    }
  };

  const startListening = async () => {
    cleanup(); // Pulisci eventuali istanze precedenti
    
    const hasPermission = await requestMicrophonePermission();
    if (!hasPermission) return;

    try {
      initializeRecognition();
      recognitionRef.current?.start();
    } catch (error) {
      console.error('Error starting recognition:', error);
      toast.error("Impossibile avviare il riconoscimento vocale");
      cleanup();
    }
  };

  const stopListening = useCallback(() => {
    cleanup();
  }, [cleanup]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    isListening,
    startListening,
    stopListening
  };
};

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { availabilityService } from '../services/availabilityService';
import { formatDate } from '../utils/dateUtils';

interface DateAvailabilitySelectorProps {
  dealId: string;
  onSelectDate?: (date: string, availability: { available: number; capacity: number; booked: number }) => void;
  initialDate?: string;
  showBookButton?: boolean;
}

/**
 * Component for selecting dates and viewing availability for a deal
 * 
 * @param dealId - The ID of the deal
 * @param onSelectDate - Callback function when a date is selected
 * @param initialDate - Initial date to select
 * @param showBookButton - Whether to show the "Book Now" button
 */
export function DateAvailabilitySelector({
  dealId,
  onSelectDate,
  initialDate,
  showBookButton = false
}: DateAvailabilitySelectorProps) {
  const [availableDates, setAvailableDates] = useState<string[]>([]);
  const [selectedDate, setSelectedDate] = useState<string | null>(initialDate || null);
  const [availability, setAvailability] = useState<{ available: number; capacity: number; booked: number } | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  
  // Fetch available dates
  useEffect(() => {
    async function fetchDates() {
      setLoading(true);
      const dates = await availabilityService.getAvailableDates(dealId);
      setAvailableDates(dates);
      
      // If initialDate is provided, fetch its availability
      if (initialDate && dates.includes(initialDate)) {
        setSelectedDate(initialDate);
        const avail = await availabilityService.getAvailabilityForDate(dealId, initialDate);
        setAvailability(avail);
      }
      
      setLoading(false);
    }
    
    fetchDates();
  }, [dealId, initialDate]);
  
  // Fetch availability when date changes
  useEffect(() => {
    if (!selectedDate) {
      setAvailability(null);
      return;
    }
    
    async function fetchAvailability() {
      const avail = await availabilityService.getAvailabilityForDate(dealId, selectedDate);
      setAvailability(avail);
      
      if (onSelectDate) {
        onSelectDate(selectedDate, avail);
      }
    }
    
    fetchAvailability();
  }, [dealId, selectedDate, onSelectDate]);
  
  const handleDateChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const date = e.target.value;
    setSelectedDate(date || null);
  };
  
  const handleBookNow = () => {
    navigate(`/book/${dealId}?date=${selectedDate}`);
  };
  
  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-10 bg-gray-200 rounded mb-4"></div>
        <div className="h-20 bg-gray-200 rounded"></div>
      </div>
    );
  }
  
  if (availableDates.length === 0) {
    return (
      <div className="p-4 bg-red-50 text-red-800 rounded-md">
        No available dates for this deal.
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      <div>
        <label htmlFor="date-select" className="block text-sm font-medium text-gray-700 mb-1">
          Select a Date
        </label>
        <select
          id="date-select"
          className="w-full p-2 border rounded-md"
          value={selectedDate || ''}
          onChange={handleDateChange}
        >
          <option value="">Select a date</option>
          {availableDates.map(date => (
            <option key={date} value={date}>{formatDate(date)}</option>
          ))}
        </select>
      </div>
      
      {selectedDate && availability && (
        <div className="p-4 bg-gray-50 rounded-md">
          <div className="flex justify-between items-center mb-2">
            <span className="font-medium">Available Seats:</span>
            <span className={`font-bold text-lg ${
              availability.available === 0 ? 'text-red-600' : 
              availability.available <= availability.capacity * 0.2 ? 'text-yellow-600' : 
              'text-green-600'
            }`}>
              {availability.available}
            </span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
            <div 
              className={`h-2.5 rounded-full ${
                availability.available === 0 ? 'bg-red-600' : 
                availability.available <= availability.capacity * 0.2 ? 'bg-yellow-500' : 
                'bg-green-600'
              }`}
              style={{ width: `${(availability.booked / availability.capacity) * 100}%` }}
            ></div>
          </div>
          
          <div className="text-sm text-gray-500">
            {availability.booked} of {availability.capacity} seats booked
          </div>
          
          {showBookButton && availability.available > 0 && (
            <button
              className="w-full mt-4 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md font-medium transition-colors"
              onClick={handleBookNow}
            >
              Book Now
            </button>
          )}
          
          {availability.available === 0 && (
            <div className="mt-3 text-sm text-red-600">
              This date is fully booked. Please select another date.
            </div>
          )}
        </div>
      )}
    </div>
  );
} 
import { useEffect, useState } from "react";
import { Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

interface AddressAutocompleteProps {
  value: string;
  onChange: (addressComponents?: AddressComponents) => void;
}

export interface AddressComponents {
  formatted_address: string;
  latitude: number;
  longitude: number;
  street_number: string;
  route: string;
  locality: string; // city
  administrative_area_level_1: string; // Regione
  administrative_area_level_2: string; // Provinza
  postal_code: string; // zip
  country: string; // country
}

export const AddressAutocomplete = ({
  value,
  onChange,
}: AddressAutocompleteProps) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [autocomplete, setAutocomplete] =
    useState<google.maps.places.Autocomplete | null>(null);
  const [inputValue, setInputValue] = useState(value);

  // Sync inputValue with value prop
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  useEffect(() => {
    let script: HTMLScriptElement;

    const loadGoogleMaps = async () => {
      try {
        const {
          data: { GOOGLE_MAPS_API_KEY },
          error,
        } = await supabase.functions.invoke("get-google-maps-key");

        if (error) throw error;

        script = document.createElement("script");
        script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places`;
        script.async = true;
        script.onload = () => {
          setIsLoaded(true);
        };
        document.head.appendChild(script);
      } catch (error) {
        console.error("Errore nel caricamento di Google Maps:", error);
      }
    };

    loadGoogleMaps();

    return () => {
      if (script) {
        document.head.removeChild(script);
      }
    };
  }, []);

  useEffect(() => {
    if (!isLoaded) return;

    const input = document.getElementById("address-input") as HTMLInputElement;
    const options = {
      // componentRestrictions: { country: 'IT' },
      fields: [
        "formatted_address",
        "geometry",
        "address_components",
        "place_id",
        "name",
      ],
    };

    const newAutocomplete = new google.maps.places.Autocomplete(input, options);
    setAutocomplete(newAutocomplete);

    newAutocomplete.addListener("place_changed", () => {
      const place = newAutocomplete.getPlace();

      // Get each component of the address from the place details,
      // and then fill-in the corresponding field on the form.
      // place.address_components are google.maps.GeocoderAddressComponent objects
      // which are documented at http://goo.gle/3l5i5Mr

      // Create a new object to collect all components
      const newAddressComponents: AddressComponents = {
        formatted_address: place.formatted_address || "",
        latitude: place.geometry?.location?.lat() || 0,
        longitude: place.geometry?.location?.lng() || 0,
        street_number: "",
        route: "",
        locality: "",
        administrative_area_level_1: "", // Regione
        administrative_area_level_2: "", // Provinza
        postal_code: "",
        country: "",
      };

      for (const component of place.address_components as google.maps.GeocoderAddressComponent[]) {
        const componentType = component.types[0];
        console.log("componentType", componentType, component);

        switch (componentType) {
          case "street_number": {
            //           console.log("street_number", component.long_name);
            newAddressComponents.street_number = component.long_name;
            break;
          }

          case "route": {
            //          console.log("route", component.long_name);
            newAddressComponents.route = component.long_name;
            break;
          }

          case "postal_code": {
            //           console.log("postal_code", component.long_name);
            newAddressComponents.postal_code = component.long_name;
            break;
          }

          case "locality": {
            //  console.log("locality", component.long_name);
            newAddressComponents.locality = component.long_name;
            break;
          }

          case "administrative_area_level_1": { // regione / 
            //         console.log("administrative_area_level_1", component.long_name);
            newAddressComponents.administrative_area_level_1 =
              component.long_name;
            break;
          }
          case "administrative_area_level_2": { // comune
            //         console.log("administrative_area_level_2", component.long_name);
            newAddressComponents.administrative_area_level_2 =
              component.short_name;
            break;
          }
          case "country": {
            //        console.log("country", component.long_name);
            // TODO Handle langiage
            if (component.long_name.toLowerCase() === "italy") { 
              newAddressComponents.country = "Italia";
            } else {
              newAddressComponents.country = component.long_name;
            }
            break;
          }
        }
      }

      // Update state once with all collected components

      if (place.geometry?.location) {
         console.log("addressComponents", newAddressComponents, place);

        onChange(newAddressComponents);
        setInputValue(place.formatted_address);
       // console.log("newAddressComponents", newAddressComponents.formatted_address);
      }
    });

    return () => {
      if (autocomplete) {
        google.maps.event.clearInstanceListeners(autocomplete);
      }
    };
  }, [isLoaded, onChange]);

  return (
    <div className="relative">
      <input
        id="address-input"
        type="text"
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        placeholder="Inserisci l'indirizzo..."
        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
      />
      {!isLoaded && (
        <div className="absolute right-2 top-1/2 -translate-y-1/2">
          <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
        </div>
      )}
    </div>
  );
};

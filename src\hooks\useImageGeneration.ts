
import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface GenerateImageParams {
  prompt?: string;
  businessType?: string;
  businessName?: string;
  negativePrompt?: string;
}

export const useImageGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImageUrl, setGeneratedImageUrl] = useState<string | null>(null);

  const generateImage = async ({
    prompt = "",
    businessType = "",
    businessName = "",
    negativePrompt = ""
  }: GenerateImageParams): Promise<string | null> => {
    setIsGenerating(true);
    setGeneratedImageUrl(null);

    try {
      console.log("Richiesta generazione immagine:", { prompt, businessType, businessName });
      
      const { data, error } = await supabase.functions.invoke('generate-business-image', {
        body: {
          prompt,
          businessType,
          businessName,
          negativePrompt
        }
      });

      if (error) {
        console.error("Errore chiamata edge function:", error);
        throw error;
      }

      if (!data.success) {
        console.error("Errore generazione immagine:", data.message);
        throw new Error(data.message || "Errore durante la generazione dell'immagine");
      }

      // Se abbiamo un URL dell'immagine diretto, lo utilizziamo
      if (data.imageUrl) {
        setGeneratedImageUrl(data.imageUrl);
        return data.imageUrl;
      }
      
      // Altrimenti, se abbiamo un ID prediction, dovremmo fare polling (non implementato qui)
      if (data.id) {
        toast.info("La generazione dell'immagine potrebbe richiedere qualche minuto");
        // In un'implementazione reale, qui inizieresti il polling
      }

      return null;
    } catch (error) {
      console.error("Errore completo:", error);
      toast.error("Errore durante la generazione dell'immagine");
      return null;
    } finally {
      setIsGenerating(false);
    }
  };

  const saveImageFromUrl = async (imageUrl: string, businessId: string): Promise<string | null> => {
    try {
      // Scarica l'immagine
      const response = await fetch(imageUrl);
      if (!response.ok) throw new Error("Impossibile scaricare l'immagine");
      
      const blob = await response.blob();
      const fileExt = "jpg"; // Assumiamo jpg per le immagini generate
      const filePath = `${businessId}/${crypto.randomUUID()}.${fileExt}`;
      
      // Carica su Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('business-photos')
        .upload(filePath, blob, {
          upsert: true,
        });

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('business-photos')
        .getPublicUrl(filePath);

      return publicUrl;
    } catch (error) {
      console.error("Errore durante il salvataggio dell'immagine:", error);
      toast.error("Errore durante il salvataggio dell'immagine");
      return null;
    }
  };

  return {
    generateImage,
    saveImageFromUrl,
    isGenerating,
    generatedImageUrl,
    setGeneratedImageUrl
  };
};

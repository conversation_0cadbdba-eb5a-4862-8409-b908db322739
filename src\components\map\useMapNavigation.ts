
import { useState, useCallback } from "react";
import { toast } from "sonner";
import { Business, NavigationState, TransportMode } from "./types";

export const useMapNavigation = (
  userLocation: { lat: number; lng: number } | null,
  isGoogleLoaded: boolean
) => {
  const [navigationState, setNavigationState] = useState<NavigationState>({
    isActive: false,
    mode: "DRIVING",
    directions: null,
  });
  const [showTransportMenu, setShowTransportMenu] = useState(false);

  const calculateRoute = useCallback(
    async (destination: Business, mode: TransportMode) => {
      if (
        !userLocation ||
        !destination.latitude ||
        !destination.longitude ||
        !isGoogleLoaded
      ) {
        toast.error(
          "Impossibile calcolare il percorso. Assicurati di aver attivato la geolocalizzazione."
        );
        return;
      }

      try {
        if (window.google && window.google.maps) {
          const directionsService = new window.google.maps.DirectionsService();
          const result = await directionsService.route({
            origin: userLocation,
            destination: {
              lat: destination.latitude,
              lng: destination.longitude,
            },
            travelMode: window.google.maps.TravelMode[mode],
            language: "it"
          });
          
          setNavigationState({
            isActive: true,
            mode,
            directions: result,
          });
        }
      } catch (error) {
        console.error("Error calculating route:", error);
        toast.error("Errore nel calcolo del percorso");
      }
    },
    [userLocation, isGoogleLoaded]
  );

  const clearNavigation = () => {
    setNavigationState({
      isActive: false,
      mode: "DRIVING",
      directions: null,
    });
    setShowTransportMenu(false);
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours} h ${minutes} min`;
    }
    return `${minutes} min`;
  };

  const formatDistance = (meters: number): string => {
    if (meters >= 1000) {
      return `${(meters / 1000).toFixed(1)} km`;
    }
    return `${meters} m`;
  };

  return {
    navigationState,
    setNavigationState,
    showTransportMenu,
    setShowTransportMenu,
    calculateRoute,
    clearNavigation,
    formatDuration,
    formatDistance
  };
};

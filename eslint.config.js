import js from "@eslint/js";
import globals from "globals";
import tseslint from "typescript-eslint";
import pluginReact from "eslint-plugin-react";

export default [
  {
    ignores: ["dist/**", "build/**", "node_modules/**", "*.config.js", "public/**"]
  },
  js.configs.recommended,
  { 
    files: ["**/*.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"], 
    languageOptions: { 
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.serviceworker,
        React: "readonly",
        JSX: "readonly",
        google: "readonly",
        process: "readonly",
        __dirname: "readonly",
        Deno: "readonly",
        NodeJS: "readonly",
        VoiceStream: "readonly",
        SpeechRecognition: "readonly",
        RTCSessionDescriptionInit: "readonly",
        RTCSdpType: "readonly",
        importScripts: "readonly",
        registration: "readonly",
        caches: "readonly",
        FetchEvent: "readonly",
        define: "readonly",
      }
    } 
  },
  ...tseslint.configs.recommended,
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    plugins: {
      react: pluginReact,
    },
    rules: {
      ...pluginReact.configs.recommended.rules,
      "react/react-in-jsx-scope": "off",
      "react/no-unescaped-entities": "warn",
      "react/prop-types": "off",
      "react/jsx-key": "warn",
      "react/no-unknown-property": "warn",
      "@typescript-eslint/no-unused-vars": "warn",
      "@typescript-eslint/no-explicit-any": "warn",
      "@typescript-eslint/no-unused-expressions": "warn",
      "@typescript-eslint/no-empty-object-type": "warn",
      "prefer-const": "warn",
      "no-extra-boolean-cast": "warn",
      "no-useless-escape": "warn",
      "no-var": "warn",
      "no-undef": "warn",
      "no-empty": "warn",
      "no-cond-assign": "warn",
    },
    settings: {
      react: {
        version: "detect",
      },
    },
  },
];

import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Switch } from "@/components/ui/switch";
import { useSession } from "@/hooks/auth/useSession";
import { useUserDetails } from "@/hooks/auth/useUserDetails";
import useUserPreferencesManager from "@/hooks/useUserPreferencesManager";
import { useState, useEffect } from "react";

const Settings = () => {
  const navigate = useNavigate();
  const { user } = useSession();
  const { userDetails, updateLocationEnabled } = useUserDetails(user?.id);
  const { preferences, isLoading, updateCategories, updatePriceRange, updateNotificationPreferences } = useUserPreferencesManager();
  
  // State for preferences editing
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedPriceRange, setSelectedPriceRange] = useState<string>("budget");
  const [notificationSettings, setNotificationSettings] = useState({
    special_offers: true,
    appointment_reminders: true,
    new_businesses: false,
    voice_assistant: false
  });

  // Load current preferences
  useEffect(() => {
    if (preferences) {
      setSelectedCategories(preferences.categories || []);
      setSelectedPriceRange(preferences.price_range || "budget");
      setNotificationSettings(preferences.notification_preferences || {
        special_offers: true,
        appointment_reminders: true,
        new_businesses: false,
        voice_assistant: false
      });
    }
  }, [preferences]);

  // Handle preference updates
  const handleCategoriesUpdate = async () => {
    await updateCategories(selectedCategories);
  };

  const handlePriceRangeUpdate = async () => {
    await updatePriceRange(selectedPriceRange);
  };

  const handleNotificationToggle = async (key: string, value: boolean) => {
    const updatedSettings = { ...notificationSettings, [key]: value };
    setNotificationSettings(updatedSettings);
    await updateNotificationPreferences({ [key]: value });
  };

  const handleLocationToggle = async (checked: boolean) => {
    await updateLocationEnabled(checked);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm px-4 py-3 flex items-center justify-between">
        <button onClick={() => navigate("/profile")} className="text-gray-700">
          <ArrowLeft className="h-6 w-6" />
        </button>
        <h1 className="text-xl font-semibold text-gray-800">Impostazioni</h1>
        <div className="w-6" /> {/* Spacer for alignment */}
      </header>

      <main className="p-4 space-y-6">
        <section className="bg-white rounded-xl shadow-sm divide-y">
          <div className="px-4 py-3">
            <h2 className="text-lg font-semibold text-gray-800">Generali</h2>
            <p className="text-sm text-gray-500">Gestisci le impostazioni del tuo account</p>
          </div>
          <div className="px-4 py-3">
            <h2 className="text-lg font-semibold text-gray-800">Notifiche</h2>
            <p className="text-sm text-gray-500">Gestisci le tue preferenze di notifica</p>
          </div>
          <div className="px-4 py-3 space-y-3">
            <h2 className="text-lg font-semibold text-gray-800">Privacy</h2>
            <p className="text-sm text-gray-500 mb-3">Gestisci le tue impostazioni sulla privacy</p>
            
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-base font-medium text-gray-700">Abilita posizione</h3>
                <p className="text-sm text-gray-500">Consenti all'app di utilizzare la tua posizione</p>
              </div>
              <Switch 
                checked={userDetails?.location_enabled ?? true}
                onCheckedChange={handleLocationToggle}
                className="data-[state=checked]:bg-brand-primary data-[state=unchecked]:bg-gray-200"
              />
            </div>
          </div>
        </section>

        {/* New Preferences Section */}
        <section className="bg-white rounded-xl shadow-sm divide-y">
          <div className="px-4 py-3">
            <h2 className="text-lg font-semibold text-gray-800">Preferenze Personali</h2>
            <p className="text-sm text-gray-500">Gestisci le tue preferenze per offerte personalizzate</p>
          </div>
          
          {/* Price Range Preferences */}
          <div className="px-4 py-3 space-y-3">
            <h3 className="text-base font-medium text-gray-700">Fascia di Prezzo</h3>
            <div className="space-y-2">
              {["budget", "mid-range", "premium"].map(range => (
                <div key={range} className="flex items-center">
                  <input
                    type="radio"
                    id={range}
                    name="priceRange"
                    checked={selectedPriceRange === range}
                    onChange={() => setSelectedPriceRange(range)}
                    className="mr-2"
                  />
                  <label htmlFor={range} className="text-gray-700">
                    {range === "budget" ? "Economico" : 
                     range === "mid-range" ? "Medio" : "Premium"}
                  </label>
                </div>
              ))}
              <button 
                onClick={handlePriceRangeUpdate}
                className="mt-2 px-3 py-1 text-sm bg-brand-primary text-white rounded-md"
                disabled={isLoading}
              >
                Aggiorna
              </button>
            </div>
          </div>
          
          {/* Notification Preferences */}
          <div className="px-4 py-3 space-y-3">
            <h3 className="text-base font-medium text-gray-700">Preferenze Notifiche</h3>
            
            {Object.entries(notificationSettings).map(([key, enabled]) => (
              <div key={key} className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-700">
                    {key === "special_offers" ? "Offerte speciali" :
                     key === "appointment_reminders" ? "Promemoria appuntamenti" :
                     key === "new_businesses" ? "Nuove attività" : "Assistente Vocale"}
                  </h4>
                </div>
                <Switch 
                  checked={enabled}
                  onCheckedChange={(checked) => handleNotificationToggle(key, checked)}
                  className="data-[state=checked]:bg-brand-primary data-[state=unchecked]:bg-gray-200"
                />
              </div>
            ))}
          </div>
          
          {/* Link to full preferences page */}
          <div className="px-4 py-3">
            <button
              onClick={() => navigate("/preferenze-personali")}
              className="w-full py-2 text-brand-primary font-medium"
            >
              Gestisci tutte le preferenze
            </button>
          </div>
        </section>
      </main>
    </div>
  );
};

export default Settings;

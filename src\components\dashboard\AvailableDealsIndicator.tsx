import React from "react";
import { motion } from "framer-motion";
import { Minus, Navigation2, Plus } from "lucide-react";
import { cn } from "@/lib/utils";

interface AvailableDealsIndicatorProps {
  count: number;
  isLoading: boolean;
  time: string;
  onRadiusChange?: (radius: number) => void;
  showRadius?: boolean;
  radius?: number;
  minRadius?: number;
  maxRadius?: number;
}

const AvailableDealsIndicator: React.FC<AvailableDealsIndicatorProps> = ({
  count,
  isLoading,
  time,
  onRadiusChange,
  showRadius,
  radius = 2000,
  minRadius = 500,
  maxRadius = 5000,
}) => {
  const handleIncreaseRadius = () => {
    if (onRadiusChange && radius < maxRadius) {
      const newRadius = Math.min(radius + 100, maxRadius);
      onRadiusChange(newRadius);
    }
  };

  const handleDecreaseRadius = () => {
    if (onRadiusChange && radius > minRadius) {
      const newRadius = Math.max(radius - 100, minRadius);
      onRadiusChange(newRadius);
    }
  };

  return (
    <div className="flex flex-col gap-3 px-4 py-3">
      {/* First line with radius control */}
      {showRadius && (
        <div className="flex justify-end items-center">
          <div className="flex items-center gap-2">
            <button
              onClick={handleDecreaseRadius}
              disabled={radius <= minRadius}
              className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center",
                radius <= minRadius
                  ? "text-gray-300"
                  : "text-gray-500 active:bg-gray-200"
              )}
              aria-label="Diminuisci raggio"
            >
              <Minus className="h-5 w-5" />
            </button>

            <div className="flex items-center gap-1">
              <Navigation2 className="h-4 w-4 text-gray-400" />
              <span className="text-base font-medium text-gray-700">
                {(radius / 1000).toFixed(1)} km
              </span>
            </div>

            <button
              onClick={handleIncreaseRadius}
              disabled={radius >= maxRadius}
              className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center",
                radius >= maxRadius
                  ? "text-gray-300"
                  : "text-gray-500 active:bg-gray-200"
              )}
              aria-label="Aumenta raggio"
            >
              <Plus className="h-5 w-5" />
            </button>
          </div>
        </div>
      )}

      {/* Second line with status indicator */}
      <div className="flex items-center gap-2">
        <div className={cn(
          "w-2 h-2 rounded-full",
          isLoading ? "bg-blue-500 animate-pulse" : "bg-green-500"
        )} />
        <span className="text-sm text-gray-700">
          {isLoading ? (
            <motion.span
              initial={{ opacity: 0.5 }}
              animate={{ opacity: 1 }}
              transition={{ repeat: Infinity, duration: 1, repeatType: "reverse" }}
            >
              Ricerca attività...
            </motion.span>
          ) : (
            `${count} ${count === 1 ? 'attività offre' : 'attività offrono'} sconto ${count > 0 ? `alle ${time}` : 'ora'}`
          )}
        </span>
      </div>
    </div>
  );
};

export default AvailableDealsIndicator;

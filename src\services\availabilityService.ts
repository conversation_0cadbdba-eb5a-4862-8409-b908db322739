import { supabase } from '../integrations/supabase/client';
import { Database } from '../integrations/supabase/types';

type Deal = Database['public']['Tables']['deals']['Row'];
type TimeSlotBooking = Database['public']['Tables']['time_slot_bookings']['Row'];

/**
 * Extracts an array of date strings from a deal's time_slot field
 */
export function extractDatesFromTimeSlot(timeSlot: any): string[] {
  // This implementation depends on your time_slot structure
  // Example assuming timeSlot is an object with dates as keys
  if (typeof timeSlot === 'object' && timeSlot !== null) {
    return Object.keys(timeSlot);
  }
  
  // Alternative implementation if timeSlot is an array of date objects
  if (Array.isArray(timeSlot)) {
    return timeSlot.map(slot => slot.date);
  }
  
  return [];
}

/**
 * Service for fetching and managing deal availability
 */
export const availabilityService = {
  /**
   * Get all available dates for a deal
   */
  async getAvailableDates(dealId: string): Promise<string[]> {
    const { data, error } = await supabase
      .from('deals')
      .select('time_slots')
      .eq('id', dealId)
      .single();
    
    if (error || !data) return [];
    
    return extractDatesFromTimeSlot(data.time_slots);
  },
  
  /**
   * Get availability for a specific date
   */
  async getAvailabilityForDate(dealId: string, date: string): Promise<{
    capacity: number;
    booked: number;
    available: number;
  }> {
    // Get deal capacity
    const { data: deal, error: dealError } = await supabase
      .from('deals')
      .select('*')
      .eq('id', dealId)
      .single();
    
    if (dealError || !deal) return { capacity: 0, booked: 0, available: 0 };
    
    // Default capacity if not specified
    const capacity = 10; // You may need to adjust this based on your data model
    
    // Get bookings for this date
    const { data: bookings, error: bookingsError } = await supabase
      .from('time_slot_bookings')
      .select('booked_seats')
      .eq('deal_id', dealId)
      .eq('booking_date', date);
    
    if (bookingsError) return { capacity, booked: 0, available: capacity };
    
    // Sum up all booked_seats for this date
    const booked = bookings?.reduce((sum, booking) => sum + (booking.booked_seats || 1), 0) || 0;
    const available = Math.max(0, capacity - booked);
    
    return {
      capacity,
      booked,
      available
    };
  },
  
  /**
   * Get availability for specific time slots on a date
   * @param dealId - The ID of the deal
   * @param date - The date in YYYY-MM-DD format
   * @returns A map of time slots to their booking counts
   */
  async getTimeSlotBookings(dealId: string, date: string): Promise<Record<string, number>> {
    if (!dealId || !date) return {};
    
    const { data, error } = await supabase
      .from('time_slot_bookings')
      .select('start_time, booked_seats')
      .eq('deal_id', dealId)
      .eq('booking_date', date);
    
    if (error || !data) return {};
    
    // Create a map of start_time to total booked seats
    return data.reduce((acc, booking) => {
      const startTime = booking.start_time;
      if (startTime) {
        acc[startTime] = (acc[startTime] || 0) + (booking.booked_seats || 1);
      }
      return acc;
    }, {} as Record<string, number>);
  },
  
  /**
   * Calculate remaining seats for a time slot
   * @param totalSeats - Total available seats for the time slot
   * @param bookedSeats - Number of booked seats
   * @returns The number of remaining seats
   */
  calculateRemainingSeats(totalSeats: number, bookedSeats: number): number {
    return Math.max(0, totalSeats - bookedSeats);
  },
  
  /**
   * Get availability summary for a deal across all dates
   */
  async getAvailabilitySummary(dealId: string): Promise<{
    totalDays: number;
    availableDays: number;
    lowestAvailability: number;
    highestAvailability: number;
    status: 'available' | 'limited' | 'sold-out';
  }> {
    const dates = await this.getAvailableDates(dealId);
    
    if (dates.length === 0) {
      return {
        totalDays: 0,
        availableDays: 0,
        lowestAvailability: 0,
        highestAvailability: 0,
        status: 'sold-out'
      };
    }
    
    // Default capacity if not specified
    const capacity = 10; // You may need to adjust this based on your data model
    
    // Get all bookings for this deal
    const { data: bookings, error: bookingsError } = await supabase
      .from('time_slot_bookings')
      .select('booking_date, booked_seats')
      .eq('deal_id', dealId);
    
    if (bookingsError) {
      return {
        totalDays: dates.length,
        availableDays: dates.length,
        lowestAvailability: capacity,
        highestAvailability: capacity,
        status: 'available'
      };
    }
    
    // Calculate availability for each date
    const bookingsByDate = (bookings || []).reduce<Record<string, number>>((acc, booking) => {
      if (booking.booking_date) {
        acc[booking.booking_date] = (acc[booking.booking_date] || 0) + (booking.booked_seats || 1);
      }
      return acc;
    }, {});
    
    let availableDays = 0;
    let lowestAvailability = capacity;
    let highestAvailability = 0;
    
    for (const date of dates) {
      const booked = bookingsByDate[date] || 0;
      const available = Math.max(0, capacity - booked);
      
      if (available > 0) {
        availableDays++;
      }
      
      lowestAvailability = Math.min(lowestAvailability, available);
      highestAvailability = Math.max(highestAvailability, available);
    }
    
    // Determine overall status
    let status: 'available' | 'limited' | 'sold-out' = 'sold-out';
    
    if (availableDays > 0) {
      status = 'available';
      
      // If less than 20% of capacity is available on any day, mark as limited
      if (lowestAvailability <= capacity * 0.2) {
        status = 'limited';
      }
    }
    
    return {
      totalDays: dates.length,
      availableDays,
      lowestAvailability,
      highestAvailability,
      status
    };
  }
}; 
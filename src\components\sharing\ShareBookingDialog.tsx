import { ShareDialog } from "./ShareDialog";
import { useShareBooking } from "@/hooks/sharing/useShareBooking";

interface ShareBookingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  bookingId: string;
  dealTitle: string;
}

export const ShareBookingDialog = ({
  open,
  onOpenChange,
  bookingId,
  dealTitle,
}: ShareBookingDialogProps) => {
  const shareBooking = useShareBooking();

  const handleShare = (groupIds: string[], message?: string) => {
    shareBooking.mutate({
      bookingId,
      groupIds,
      message,
    });
  };

  return (
    <ShareDialog
      open={open}
      onOpenChange={onOpenChange}
      title="Condividi Prenotazione"
      description={`Condividi la tua prenotazione per "${dealTitle}" con i tuoi gruppi`}
      onShare={handleShare}
      isLoading={shareBooking.isPending}
    />
  );
};
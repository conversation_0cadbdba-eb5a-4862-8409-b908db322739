import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface SocialActivity {
  id: string;
  type: 'deal_share' | 'booking_share';
  user_name: string;
  user_avatar?: string;
  group_name: string;
  message?: string;
  created_at: string;
  shared_by: string; // Added to identify who shared
  deal?: {
    id: string;
    title: string;
    description: string;
    images: string[];
    original_price: number;
    discounted_price: number;
    business_name: string;
  };
  booking?: {
    id: string;
    deal_title: string;
    deal_images: string[];
    booking_date: string;
    booking_time: string;
    business_name: string;
    status: string;
  };
}

export const useSocialFeed = () => {
  return useQuery({
    queryKey: ['social-feed'],
    queryFn: async (): Promise<SocialActivity[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get user's groups
      const { data: userGroups, error: groupsError } = await supabase
        .from('group_members')
        .select('group_id')
        .eq('user_id', user.id);

      if (groupsError) throw groupsError;

      if (!userGroups || userGroups.length === 0) {
        return [];
      }

      const groupIds = userGroups.map(ug => ug.group_id);

      // Fetch deal shares from groups (include all shares, we'll deduplicate client-side)
      const { data: dealShares, error: dealSharesError } = await supabase
        .from('group_deal_shares')
        .select(`
          id,
          message,
          created_at,
          group_id,
          shared_by,
          groups!inner(name),
          profiles:users_with_details!group_deal_shares_shared_by_fkey(
            first_name,
            last_name,
            avatar_url
          ),
          deals!inner(
            id,
            title,
            description,
            images,
            original_price,
            discounted_price,
            businesses!inner(name)
          )
        `)
        .in('group_id', groupIds)
        .order('created_at', { ascending: false })
        .limit(50);

      if (dealSharesError) throw dealSharesError;

      // Fetch booking shares from groups (include all shares, we'll deduplicate client-side)
      const { data: bookingShares, error: bookingSharesError } = await supabase
        .from('group_booking_shares')
        .select(`
          id,
          message,
          created_at,
          group_id,
          shared_by,
          groups!inner(name),
          profiles:users_with_details!group_booking_shares_shared_by_fkey(
            first_name,
            last_name,
            avatar_url
          ),
          bookings!inner(
            id,
            booking_date,
            booking_time,
            status,
            deals!inner(
              title,
              images,
              businesses!inner(name)
            )
          )
        `)
        .in('group_id', groupIds)
        .order('created_at', { ascending: false })
        .limit(50);

      if (bookingSharesError) throw bookingSharesError;

      // Transform and combine activities
      const activities: SocialActivity[] = [];

      // Add deal shares
      dealShares?.forEach((share: any) => {
        activities.push({
          id: `deal-${share.id}`,
          type: 'deal_share',
          user_name: `${share.profiles?.first_name || ''} ${share.profiles?.last_name || ''}`.trim() || 'Utente',
          user_avatar: share.profiles?.avatar_url,
          group_name: share.groups?.name || 'Gruppo',
          message: share.message,
          created_at: share.created_at,
          shared_by: share.shared_by,
          deal: {
            id: share.deals.id,
            title: share.deals.title,
            description: share.deals.description,
            images: share.deals.images || [],
            original_price: share.deals.original_price,
            discounted_price: share.deals.discounted_price,
            business_name: share.deals.businesses?.name || 'Business',
          },
        });
      });

      // Add booking shares
      bookingShares?.forEach((share: any) => {
        activities.push({
          id: `booking-${share.id}`,
          type: 'booking_share',
          user_name: `${share.profiles?.first_name || ''} ${share.profiles?.last_name || ''}`.trim() || 'Utente',
          user_avatar: share.profiles?.avatar_url,
          group_name: share.groups?.name || 'Gruppo',
          message: share.message,
          created_at: share.created_at,
          shared_by: share.shared_by,
          booking: {
            id: share.bookings.id,
            deal_title: share.bookings.deals?.title || 'Deal',
            deal_images: share.bookings.deals?.images || [],
            booking_date: share.bookings.booking_date,
            booking_time: share.bookings.booking_time,
            business_name: share.bookings.deals?.businesses?.name || 'Business',
            status: share.bookings.status,
          },
        });
      });

      // Sort by created_at descending
      activities.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      return activities;
    },
    enabled: true,
  });
};
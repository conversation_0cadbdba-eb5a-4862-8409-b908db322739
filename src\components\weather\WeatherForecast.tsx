import { Calendar, TrendingUp, TrendingDown, Cloud, Sun, CloudRain, CloudSnow, Zap } from 'lucide-react';
import { DailyForecast } from '@/services/weatherService';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface WeatherForecastProps {
  forecast: DailyForecast[];
}

const getWeatherIcon = (condition: string, size = 'h-8 w-8') => {
  const conditionLower = condition.toLowerCase();
  
  if (conditionLower.includes('sun') || conditionLower.includes('clear')) {
    return <Sun className={cn(size, 'text-yellow-500')} />;
  } else if (conditionLower.includes('rain') || conditionLower.includes('drizzle')) {
    return <CloudRain className={cn(size, 'text-blue-500')} />;
  } else if (conditionLower.includes('snow')) {
    return <CloudSnow className={cn(size, 'text-blue-400')} />;
  } else if (conditionLower.includes('thunder') || conditionLower.includes('storm')) {
    return <Zap className={cn(size, 'text-yellow-600')} />;
  } else {
    return <Cloud className={cn(size, 'text-gray-500')} />;
  }
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);

  if (date.toDateString() === today.toDateString()) {
    return 'Oggi';
  } else if (date.toDateString() === tomorrow.toDateString()) {
    return 'Domani';
  } else {
    return date.toLocaleDateString('it-IT', { 
      weekday: 'short', 
      day: 'numeric',
      month: 'short'
    });
  }
};

export const WeatherForecast = ({ forecast }: WeatherForecastProps) => {
  return (
    <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 w-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3 text-xl">
          <div className="bg-blue-100 rounded-full p-2">
            <Calendar className="h-5 w-5 text-blue-600" />
          </div>
          Previsioni 5 Giorni
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {forecast.map((day, index) => (
          <div
            key={index}
            className={cn(
              "flex items-center justify-between p-3 rounded-xl border transition-all hover:shadow-md",
              index === 0
                ? "bg-blue-50 border-blue-200 shadow-sm" 
                : "bg-white border-gray-200 hover:bg-gray-50"
            )}
          >
            {/* Date and Icon */}
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <div className="flex-shrink-0">
                <div className={cn(
                  "font-semibold text-sm",
                  index === 0 ? "text-blue-700" : "text-gray-700"
                )}>
                  {formatDate(day.date)}
                </div>
              </div>
              
              <div className="flex-shrink-0">
                {getWeatherIcon(day.condition, 'h-6 w-6')}
              </div>
            </div>

            {/* Temperature Range */}
            <div className="flex items-center gap-2">
              <span className="font-semibold text-gray-900 text-sm">
                {Math.round(day.tempMax)}°
              </span>
              <span className="text-gray-500 text-sm">
                {Math.round(day.tempMin)}°
              </span>
            </div>

            {/* Precipitation */}
            {day.precipitation > 0 && (
              <div className="flex-shrink-0">
                <span className="text-xs text-blue-600 bg-blue-50 rounded px-1">
                  💧{day.precipitation.toFixed(0)}%
                </span>
              </div>
            )}
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

import React from 'react';
import { Button } from '@/components/ui/button';
import { Users } from 'lucide-react';
import { useContactPicker, Contact } from '@/hooks/useContactPicker';
import { toast } from 'sonner';

interface ContactPickerButtonProps {
  onContactsSelected: (contacts: Contact[]) => void;
  disabled?: boolean;
  variant?: 'default' | 'outline' | 'secondary';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}

export const ContactPickerButton: React.FC<ContactPickerButtonProps> = ({
  onContactsSelected,
  disabled = false,
  variant = 'outline',
  size = 'default',
  className = ''
}) => {
  const { isSupported, pickContacts } = useContactPicker();

  const handlePickContacts = async () => {
    if (!isSupported) {
      toast.error('La selezione dei contatti non è supportata su questo dispositivo');
      return;
    }

    const result = await pickContacts({ multiple: true });
    
    if (result.error) {
      toast.error(result.error);
      return;
    }

    if (result.contacts.length > 0) {
      onContactsSelected(result.contacts);
      toast.success(`${result.contacts.length} contatti selezionati`);
    }
  };

  if (!isSupported) {
    return (
      <div className="text-center py-4 text-gray-500">
        <p className="text-sm">
          La selezione dei contatti non è supportata su questo dispositivo.
        </p>
        <p className="text-xs mt-1">
          Questa funzionalità è disponibile solo su dispositivi mobili con supporto nativo.
        </p>
      </div>
    );
  }

  return (
    <Button
      onClick={handlePickContacts}
      disabled={disabled}
      variant={variant}
      size={size}
      className={className}
    >
      <Users className="h-4 w-4 mr-2" />
      Seleziona dai contatti
    </Button>
  );
};


import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Plus, Users, ArrowRight } from 'lucide-react';
import { useCreateGroup } from '@/hooks/group/useGroups';
import { ContactPickerButton } from '@/components/contacts/ContactPickerButton';
import { ContactList } from '@/components/contacts/ContactList';
import { Contact, useContactPicker } from '@/hooks/useContactPicker';

interface CreateGroupDialogProps {
  trigger?: React.ReactNode;
}

const CreateGroupDialog = ({ trigger }: CreateGroupDialogProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState<'details' | 'invite'>('details');
  const [createdGroupId, setCreatedGroupId] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    avatar_url: ''
  });
  
  const [selectedContacts, setSelectedContacts] = useState<Contact[]>([]);
  const { isSupported } = useContactPicker();

  const createGroupMutation = useCreateGroup();

  const handleCreateGroup = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      return;
    }

    try {
      const result = await createGroupMutation.mutateAsync({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        avatar_url: formData.avatar_url.trim() || undefined
      });
      
      setCreatedGroupId(result.id);
      setCurrentStep('invite');
    } catch (error) {
      console.error('Error creating group:', error);
    }
  };

  const handleContactsSelected = (contacts: Contact[]) => {
    setSelectedContacts(prev => [...prev, ...contacts]);
  };

  const handleRemoveContact = (index: number) => {
    setSelectedContacts(prev => prev.filter((_, i) => i !== index));
  };

  const handleFinish = () => {
    // TODO: Here we would send invitations to the selected contacts
    console.log('Inviting contacts:', selectedContacts);
    
    // Reset and close
    setFormData({ name: '', description: '', avatar_url: '' });
    setSelectedContacts([]);
    setCurrentStep('details');
    setCreatedGroupId(null);
    setIsOpen(false);
  };

  const handleSkipInvitations = () => {
    setFormData({ name: '', description: '', avatar_url: '' });
    setSelectedContacts([]);
    setCurrentStep('details');
    setCreatedGroupId(null);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="bg-brand-primary hover:bg-brand-primary/90">
            <Plus className="h-4 w-4 mr-2" />
            Crea Gruppo
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] bg-white border border-gray-200 shadow-lg z-50 max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-gray-900">
            <Users className="h-5 w-5" />
            {currentStep === 'details' ? 'Crea Nuovo Gruppo' : 'Invita Membri'}
          </DialogTitle>
        </DialogHeader>

        {currentStep === 'details' && (
          <form onSubmit={handleCreateGroup} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-gray-700">Nome Gruppo *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Es. Amici di Roma, Famiglia, Colleghi..."
                required
                className="bg-white border-gray-300"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description" className="text-gray-700">Descrizione (opzionale)</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Descrivi il tuo gruppo..."
                rows={3}
                className="bg-white border-gray-300"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="avatar" className="text-gray-700">URL Avatar (opzionale)</Label>
              <Input
                id="avatar"
                type="url"
                value={formData.avatar_url}
                onChange={(e) => setFormData(prev => ({ ...prev, avatar_url: e.target.value }))}
                placeholder="https://esempio.com/avatar.jpg"
                className="bg-white border-gray-300"
              />
            </div>
            
            <div className="flex justify-end space-x-2 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsOpen(false)}
                className="bg-white border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Annulla
              </Button>
              <Button 
                type="submit" 
                disabled={!formData.name.trim() || createGroupMutation.isPending}
                className="bg-brand-primary hover:bg-brand-primary/90 text-white"
              >
                {createGroupMutation.isPending ? 'Creazione...' : 'Crea Gruppo'}
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </form>
        )}

        {currentStep === 'invite' && (
          <div className="space-y-4">
            <div className="text-center py-2">
              <p className="text-sm text-gray-600 mb-4">
                Invita persone dai tuoi contatti. Puoi farlo anche più tardi.
              </p>
            </div>

            <ContactPickerButton
              onContactsSelected={handleContactsSelected}
              variant="outline"
              className="w-full"
            />

            <ContactList
              contacts={selectedContacts}
              onRemoveContact={handleRemoveContact}
            />

            <div className="flex justify-between pt-4">
              <Button
                variant="outline"
                onClick={handleSkipInvitations}
                className="bg-white border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Salta per ora
              </Button>
              
              <Button
                onClick={handleFinish}
                disabled={selectedContacts.length === 0}
                className="bg-brand-primary hover:bg-brand-primary/90 text-white"
              >
                Invita {selectedContacts.length > 0 ? `${selectedContacts.length} ` : ''}
                {selectedContacts.length === 1 ? 'persona' : 'persone'}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CreateGroupDialog;

import React from "react";
import { useNavigate } from "react-router-dom";
import { 
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Badge } from "@/components/ui/badge";
import DealCard from '@/components/deals/core/DealCard';
import SwipeHintIcon from '@/components/ui/SwipeHintIcon';
import { BusinessAvailability } from "@/hooks/useAvailabilitySearch";
import { useAuth } from "@/hooks/auth/useAuth";
import { Deal, JsonCompatible } from "@/types/deals";
import { Database } from "@/integrations/supabase/types";
import { useInitialBounceAnimation } from '@/hooks/useInitialBounceAnimation';
import { cn } from '@/lib/utils';

interface BusinessCarouselProps {
  businesses: BusinessAvailability[];
  isLoading: boolean;
  title: string;
  showVisitBusiness: boolean;
}

const BusinessCarousel = ({ 
  businesses, 
  isLoading, 
  showVisitBusiness,
  title 
}: BusinessCarouselProps) => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const { animationClasses, showSwipeIcon } = useInitialBounceAnimation({
    delay: 1200,
    duration: 800,
    enabled: businesses.length > 1 && !isLoading
  });

  const transformBusinessToDeal = (business: BusinessAvailability): Deal => {
    const timeSlots: JsonCompatible = business.available_slots ? 
      JSON.parse(JSON.stringify(business.available_slots)) : null;
    
    return {
      id: business.business_id,
      title: business.business_name,
      description: "Offerta speciale disponibile ora",
      business_id: business.business_id,
      discounted_price: 0,
      original_price: 0,
      discount_percentage: 20,
      start_date: new Date().toISOString(),
      end_date: new Date().toISOString(),
      time_slots: timeSlots,
      status: "published",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      images: business.photos || [],
      businesses: {
        name: business.business_name,
        address: business.address
      },
      auto_confirm: true,
      category_id: "",
      fake: false,
      price: 0,
      image_url: business.photos && business.photos.length > 0 ? business.photos[0] : "",
      business_name: business.business_name,
      capacity: 10,
      terms_conditions: "Informazioni importanti -La prenotazione non è rimborsabile -Presentati 5 minuti prima dell'orario prenotato -Porta con te un documento d'identità"
    };
  };

  const handleCardClick = (businessId: string) => {
    navigate(`/business/${businessId}`);
  };

  if (isLoading) {
    return (
      <div className="my-4">
        <h2 className="text-lg font-semibold mb-2">{title}</h2>
        <div className="flex gap-4 overflow-hidden py-2">
          {[1, 2, 3].map((_, index) => (
            <div 
              key={index} 
              className="min-w-[280px] h-64 bg-gray-200 rounded-xl animate-pulse"
            />
          ))}
        </div>
      </div>
    );
  }

  if (businesses.length === 0) {
    return null;
  }

  return (
    <div className="my-4">
      <div className="flex items-center gap-2">
        <h2 className="text-lg font-semibold">{title}</h2>
        <Badge variant="secondary" className="bg-brand-primary text-white">
          {businesses.length}
        </Badge>
      </div>
      
      <div className="relative">
        <Carousel
          opts={{
            align: "start",
            loop: businesses.length > 3
          }}
          className={cn("mt-2", animationClasses)}
        >
          <CarouselContent>
            {businesses.map((business) => (
              <CarouselItem 
                key={business.business_id} 
                className="md:basis-1/2 lg:basis-1/3"
              >
                <div className="p-1">
                  <DealCard
                    deal={transformBusinessToDeal(business)}
                    variant="compact"
                    onClick={() => handleCardClick(business.business_id)}
                    showVisitBusiness={showVisitBusiness}
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="left-2 bg-white" />
          <CarouselNext className="right-2 bg-white" />
        </Carousel>
        
        <SwipeHintIcon show={showSwipeIcon} />
      </div>
    </div>
  );
};

export default BusinessCarousel;

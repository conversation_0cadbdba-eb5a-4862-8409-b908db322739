
import { Plus, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { DAYS_OF_WEEK } from "@/data/daysNames";
import { TimeSlot } from "@/types/deals";



interface DealScheduleProps {
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  recurringDays: number[];
  timeSlots: TimeSlot[];
  exceptions: string[];
  onStartDateChange: (value: string) => void;
  onEndDateChange: (value: string) => void;
  onStartTimeChange: (value: string) => void;
  onEndTimeChange: (value: string) => void;
  onRecurringDayToggle: (day: number) => void;
  onAddTimeSlot: () => void;
  onRemoveTimeSlot: (index: number) => void;
  onAddException: (date: string) => void;
  onRemoveException: (index: number) => void;
}

export const DealSchedule = ({
  startDate,
  endDate,
  startTime,
  endTime,
  recurringDays,
  timeSlots,
  exceptions,
  onStartDateChange,
  onEndDateChange,
  onStartTimeChange,
  onEndTimeChange,
  onRecurringDayToggle,
  onAddTimeSlot,
  onRemoveTimeSlot,
  onAddException,
  onRemoveException,
}: DealScheduleProps) => {
  return (
    <div className="border rounded-xl p-4 space-y-4">
      <h3 className="font-medium text-gray-900">Periodo di Validità</h3>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Data Inizio
          </label>
          <input
            type="datetime-local"
            required
            value={startDate}
            onChange={e => onStartDateChange(e.target.value)}
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Data Fine
          </label>
          <input
            type="datetime-local"
            required
            value={endDate}
            onChange={e => onEndDateChange(e.target.value)}
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Giorni di Ripetizione
        </label>
        <div className="flex flex-wrap gap-2">
          {DAYS_OF_WEEK.map(day => (
            <button
              key={day.value}
              type="button"
              onClick={() => onRecurringDayToggle(day.value)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                recurringDays.includes(day.value)
                  ? 'bg-brand-primary text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {day.label}
            </button>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Fasce Orarie
        </label>
        <div className="space-y-3">
          <div className="flex gap-2">
            <input
              type="time"
              value={startTime}
              onChange={e => onStartTimeChange(e.target.value)}
              className="flex-1 px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
            />
            <input
              type="time"
              value={endTime}
              onChange={e => onEndTimeChange(e.target.value)}
              className="flex-1 px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
            />
            <button
              type="button"
              onClick={onAddTimeSlot}
              className="px-4 py-3 bg-brand-primary text-white rounded-xl hover:bg-brand-primary/90"
            >
              <Plus className="h-5 w-5" />
            </button>
          </div>

          {timeSlots.map((slot, index) => (
            <div key={index} className="flex items-center gap-2 bg-gray-50 p-2 rounded-lg">
              <span className="flex-1 text-sm">
                {slot.start_time} - {slot.end_time}
              </span>
              <button
                type="button"
                onClick={() => onRemoveTimeSlot(index)}
                className="p-1 text-red-500 hover:bg-red-50 rounded"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Eccezioni (giorni di chiusura)
        </label>
        <div className="space-y-3">
          <input
            type="date"
            onChange={e => onAddException(e.target.value)}
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />

          {exceptions.map((date, index) => (
            <div key={index} className="flex items-center gap-2 bg-gray-50 p-2 rounded-lg">
              <span className="flex-1 text-sm">
                {format(new Date(date), "EEEE d MMMM yyyy", { locale: it })}
              </span>
              <button
                type="button"
                onClick={() => onRemoveException(index)}
                className="p-1 text-red-500 hover:bg-red-50 rounded"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};


export default DealSchedule;
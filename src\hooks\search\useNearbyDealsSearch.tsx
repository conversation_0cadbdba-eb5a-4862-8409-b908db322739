// src/hooks/search/useNearbyDealsSearch.tsx
import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import {
  DealAvailability,
  SearchLocation,
  TimeSlotQueryParams,
  SearchMetrics,
} from "@/types/search";

export function useNearbyDealsSearch(
  initialParams?: Partial<TimeSlotQueryParams>
) {
  const [params, setParams] = useState<TimeSlotQueryParams>({
    date: initialParams?.date || new Date().toISOString().split("T")[0],
    time: initialParams?.time,
    location: initialParams?.location,
    cityName: initialParams?.cityName,
    category_id: initialParams?.category_id,
    limit: initialParams?.limit || 20,
  });

  const [results, setResults] = useState<DealAvailability[]>([]);
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<SearchMetrics>({
    queryTime: 0,
    totalResults: 0,
    isCached: false,
    pageNumber: 1,
  });

  const searchAvailability = useCallback(
    async (isLoadMore: boolean = false) => {
      if (!params.date) return;

      const searchLocation = params.location;

      if (!searchLocation && !params.cityName) {
        setError("Posizione non disponibile per la ricerca");
        return;
      }

      try {
        isLoadMore ? setIsLoadingMore(true) : setIsLoading(true);
        setError(null);

        const payload = {
          date: params.date,
          time: params.time,
          location: searchLocation,
          cityName: params.cityName,
          category_id: params.category_id,
          cursor: isLoadMore ? nextCursor : null,
          limit: params.limit,
        };
        console.log("Payload for get-nearby-deals-by-location", payload);
        const startTime = performance.now();
        const { data, error } = await supabase.functions.invoke(
          "get-nearby-deals-by-location",
          {
            body: payload,
          }
        );
        console.log("Data for get-nearby-deals-by-location", data);
        if (error) throw new Error(error.message);

        const queryTime = performance.now() - startTime;

        setMetrics((prev) => ({
          queryTime,
          totalResults: isLoadMore
            ? prev.totalResults + (data.deals?.length || 0)
            : data.deals?.length || 0,
          isCached: data.cached || false,
          pageNumber: isLoadMore ? prev.pageNumber + 1 : 1,
        }));

        setResults((prev) =>
          isLoadMore ? [...prev, ...data.deals] : data.deals || []
        );
        setNextCursor(data.next_cursor);
      } catch (err: any) {
        console.error("Errore nella ricerca disponibilità:", err);
        setError(err.message || "Errore durante la ricerca");
        toast.error("Impossibile completare la ricerca di disponibilità");
      } finally {
        isLoadMore ? setIsLoadingMore(false) : setIsLoading(false);
      }
    },
    [params, nextCursor]
  );

  useEffect(() => {
    searchAvailability(false);
  }, [params]);

  const updateSearchParams = (newParams: Partial<TimeSlotQueryParams>) => {
    setParams((prev) => ({ ...prev, ...newParams }));
    setNextCursor(null); // Reset pagination when params change
  };

  const loadMore = () => {
    if (nextCursor && !isLoadingMore) {
      searchAvailability(true);
    }
  };

  const refresh = () => {
    setNextCursor(null);
    searchAvailability(false);
  };

  return {
    results,
    isLoading,
    isLoadingMore,
    error,
    params,
    metrics,
    hasMore: !!nextCursor,
    updateSearchParams,
    loadMore,
    refresh,
  };
}

// import { useState, useEffect, useCallback } from "react";
// import { supabase } from "@/integrations/supabase/client";
// import { toast } from "sonner";
// import { useLocationManagement } from "../location/useLocationManagement";
// import { DealAvailability, SearchLocation, TimeSlotQueryParams, SearchMetrics } from "@/types/search";

// export function useNearbyDealsSearch(
//   initialParams?: Partial<TimeSlotQueryParams>
// ) {
//   const [params, setParams] = useState<TimeSlotQueryParams>({
//     date: initialParams?.date || new Date().toISOString().split("T")[0],
//     time: initialParams?.time,
//     location: initialParams?.location,
//     cityName: initialParams?.cityName,
//     category_id: initialParams?.category_id,
//     limit: initialParams?.limit || 20,
//   });

//   const [results, setResults] = useState<DealAvailability[]>([]);
//   const [nextCursor, setNextCursor] = useState<string | null>(null);
//   const [isLoading, setIsLoading] = useState<boolean>(false);
//   const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
//   const [error, setError] = useState<string | null>(null);
//   const [metrics, setMetrics] = useState<SearchMetrics>({
//     queryTime: 0,
//     totalResults: 0,
//     isCached: false,
//     pageNumber: 1,
//   });

//   const { userLocation } = useLocationManagement();

//   const searchAvailability = useCallback(
//     async (isLoadMore: boolean = false) => {
//       if (!params.date) return;

//       const searchLocation =
//         params.location ||
//         (userLocation
//           ? { lat: userLocation.lat, lng: userLocation.lng }
//           : undefined);

//       if (!searchLocation && !params.cityName) {
//         setError("Posizione non disponibile per la ricerca");
//         return;
//       }

//       try {
//         isLoadMore ? setIsLoadingMore(true) : setIsLoading(true);
//         setError(null);

//         const startTime = performance.now();
//         console.log("startTime: ", startTime);
//         const { data, error } = await supabase.functions.invoke(
//           "get-nearby-deals-by-location",
//           {
//             body: {
//               date: params.date,
//               time: params.time,
//               location: searchLocation,
//               cityName: params.cityName,
//               category_id: params.category_id,
//               cursor: isLoadMore ? nextCursor : null,
//               limit: params.limit,
//             },
//           }
//         );
//         console.log("availability: ", data);
//         if (error) throw new Error(error.message);

//         const queryTime = performance.now() - startTime;

//         setMetrics((prev) => ({
//           queryTime,
//           totalResults: isLoadMore
//             ? prev.totalResults + (data.deals?.length || 0)
//             : data.deals?.length || 0,
//           isCached: data.cached || false,
//           pageNumber: isLoadMore ? prev.pageNumber + 1 : 1,
//         }));

//         setResults((prev) =>
//           isLoadMore ? [...prev, ...data.deals] : data.deals || []
//         );
//         setNextCursor(data.next_cursor);
//       } catch (err: any) {
//         console.error("Errore nella ricerca disponibilità:", err);
//         setError(err.message || "Errore durante la ricerca");
//         toast.error("Impossibile completare la ricerca di disponibilità");
//       } finally {
//         isLoadMore ? setIsLoadingMore(false) : setIsLoading(false);
//       }
//     },
//     [params, userLocation, nextCursor]
//   );

//   useEffect(() => {
//     searchAvailability(false);
//   }, [params, userLocation]);

//   const updateSearchParams = (newParams: Partial<TimeSlotQueryParams>) => {
//     setParams((prev) => ({ ...prev, ...newParams }));
//     setNextCursor(null); // Reset pagination when params change
//   };

//   const loadMore = () => {
//     if (nextCursor && !isLoadingMore) {
//       searchAvailability(true);
//     }
//   };

//   const refresh = () => {
//     setNextCursor(null);
//     searchAvailability(false);
//   };

//   return {
//     results,
//     isLoading,
//     isLoadingMore,
//     error,
//     params,
//     metrics,
//     hasMore: !!nextCursor,
//     updateSearchParams,
//     loadMore,
//     refresh,
//   };
// }

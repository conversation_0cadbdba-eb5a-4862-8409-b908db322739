
import React from "react";
import { AdvancedMarker, Pin } from "@vis.gl/react-google-maps";

const PinWrapper = (props: {
  background: string;
  borderColor: string;
  glyphColor: string;
  scale: number;
}) => {
  // Filtra solo le proprietà accettate da Pin per evitare errori con data-lov-id
  const { background, borderColor, glyphColor, scale } = props;
  return React.createElement(Pin, { background, borderColor, glyphColor, scale });
};

/**
 * Marker component for user location
 */
export const UserLocationMarker = ({ position }: { position: { lat: number, lng: number } }) => {
  return (
    <AdvancedMarker
      position={position}
      zIndex={2500}
      title="La tua posizione"
    >
      <div className="relative">
        {/* Effetto pulsante animato */}
        <div className="absolute w-12 h-12 rounded-full bg-blue-500 opacity-25 animate-ping" style={{ top: '-6px', left: '-6px' }}></div>
        {/* Pin di Google Maps con colore blu */}
        <PinWrapper
          background={"#1976D2"} 
          borderColor={"#FFFFFF"}
          glyphColor={"#FFFFFF"}
          scale={1.0}
        />
      </div>
    </AdvancedMarker>
  );
};


import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { settings } from "@/config/settings";

export const useBusinessPhotos = () => {
  const [photos, setPhotos] = useState<File[]>([]);
  const [photoPreviews, setPhotoPreviews] = useState<string[]>([]);
  const [isUploadingPhotos, setIsUploadingPhotos] = useState(false);

  const handlePhotoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    if (photos.length + files.length > settings.maxBusinessPhotos) {
      toast.error(`Puoi caricare un massimo di ${settings.maxBusinessPhotos} foto`);
      return;
    }

    const invalidFiles = files.filter(file => !file.type.startsWith('image/'));
    if (invalidFiles.length > 0) {
      toast.error("Per favore seleziona solo file immagine");
      return;
    }

    const oversizedFiles = files.filter(file => file.size > 5 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      toast.error("Le immagini devono essere più piccole di 5MB");
      return;
    }

    setPhotos(prev => [...prev, ...files]);
    
    // Genera preview per le nuove foto
    files.forEach(file => {
      const preview = URL.createObjectURL(file);
      setPhotoPreviews(prev => [...prev, preview]);
    });
  };

  const removePhoto = (index: number) => {
    URL.revokeObjectURL(photoPreviews[index]); // Revoca l'URL della preview
    setPhotos(prev => prev.filter((_, i) => i !== index));
    setPhotoPreviews(prev => prev.filter((_, i) => i !== index));
  };

  const uploadPhotos = async (businessId: string): Promise<string[]> => {
    if (photos.length === 0) return [];

    setIsUploadingPhotos(true);
    const uploadedUrls: string[] = [];

    try {
      console.log('Inizio upload di', photos.length, 'foto');
      
      for (const photo of photos) {
        const fileExt = photo.name.split('.').pop();
        const filePath = `${businessId}/${crypto.randomUUID()}.${fileExt}`;
        
        console.log('Uploading file:', filePath);

        const { error: uploadError, data } = await supabase.storage
          .from('business-photos')
          .upload(filePath, photo, {
            upsert: true,
          });

        if (uploadError) {
          console.error('Errore upload foto:', uploadError);
          throw uploadError;
        }

        const { data: { publicUrl } } = supabase.storage
          .from('business-photos')
          .getPublicUrl(filePath);

        console.log('File caricato:', publicUrl);
        uploadedUrls.push(publicUrl);
      }

      console.log('Upload completato, URL:', uploadedUrls);
      
      // Reset delle foto dopo il caricamento riuscito
      setPhotos([]);
      setPhotoPreviews([]);
      
      return uploadedUrls;
    } catch (error) {
      console.error('Errore durante il caricamento delle foto:', error);
      toast.error("Errore durante il caricamento delle foto");
      return [];
    } finally {
      setIsUploadingPhotos(false);
    }
  };

  return {
    photos,
    photoPreviews,
    isUploadingPhotos,
    handlePhotoChange,
    removePhoto,
    uploadPhotos
  };
};

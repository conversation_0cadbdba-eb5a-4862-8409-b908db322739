import { useEffect, useRef, useState, useC<PERSON>back, useMemo } from "react";

import {
  GoogleMap,
  LoadScript,
  <PERSON><PERSON>,
  Directions<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "@react-google-maps/api";

import {
  Store,
  X,
  
  MapPin,
  Navigation2,
  ChevronRight,
  Clock,
  
  Car,
  PersonStanding,
  Bus,
  Bike,
  
  User2,
} from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import { toast } from "sonner";

import { supabase } from "@/integrations/supabase/client";
import { cn } from "@/lib/utils";
import { debounce } from "lodash";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";
import { NEARBY_DEFAULT_RADIUS_IN_METERS } from "@/data/userSettings";
import DealCard from "../deals/core/DealCard";

interface Business {
  id: string;
  name: string;
  latitude: number | null;
  longitude: number | null;
  deal_count_published: number | null;
  deals?: any[];
  fake: boolean;
  icon?: string | null;
}

interface MapViewProps {
  businesses: Business[];
  onBusinessClick?: (business: Business) => void;
  onDealClick?: (dealId: string) => void;
  locationEnabled?: boolean;
}

type TransportMode = "DRIVING" | "WALKING" | "TRANSIT" | "BICYCLING";

interface NavigationState {
  isActive: boolean;
  mode: TransportMode;
  directions: google.maps.DirectionsResult | null;
}

const DEFAULT_ZOOM = 14;

export const MapView = ({
  businesses,
  onBusinessClick,
  onDealClick,
  locationEnabled = true,
}: MapViewProps) => {
  const mapRef = useRef<google.maps.Map>();
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(
    null
  );
  const [center, setCenter] = useState<{ lat: number; lng: number }>({
    lat: 45.4671,
    lng: 9.1526,
  });
  const [apiKey, setApiKey] = useState<string>("");
  const [isSlideVisible, setIsSlideVisible] = useState(false);
  const slideRef = useRef<HTMLDivElement>(null);
  const touchStartY = useRef<number | null>(null);
  const [currentBounds, setCurrentBounds] =
    useState<google.maps.LatLngBounds | null>(null);
  const [isLoadingMarkers, setIsLoadingMarkers] = useState(false);
  const [navigationState, setNavigationState] = useState<NavigationState>({
    isActive: false,
    mode: "DRIVING",
    directions: null,
  });
  const [showTransportMenu, setShowTransportMenu] = useState(false);

  const { userLocation, demoEnabled } = useLocationManagement();
  const [isGoogleLoaded, setIsGoogleLoaded] = useState(false);

  const [userCircle, setUserCircle] = useState<google.maps.Circle | null>(null);
  const [userCircleCenter, setUserCircleCenter] = useState<{
    lat: number;
    lng: number;
  } | null>(null);

  const [userCircleRadius, setUserCircleRadius] =
    useState<number>(NEARBY_DEFAULT_RADIUS_IN_METERS);

  const increaseRadius = useCallback(() => {
    setUserCircleRadius((prev) => prev + 100);
  }, []);

  const decreaseRadius = useCallback(() => {
    setUserCircleRadius((prev) => Math.max(100, prev - 100));
  }, []);

  useEffect(() => {
    if (userLocation && locationEnabled) {
      setCenter(userLocation);
    }
  }, [userLocation, locationEnabled]);

  useEffect(() => {
    const getGoogleMapsKey = async () => {
      const {
        data: { GOOGLE_MAPS_API_KEY },
        error,
      } = await supabase.functions.invoke("get-google-maps-key");
      if (error) {
        console.error("Errore nel recupero della chiave API:", error);
        return;
      }
      setApiKey(GOOGLE_MAPS_API_KEY);
    };

    getGoogleMapsKey();
  }, []);

  const createBusinessMarkerIcon = (business: Business) => {
    if (!isGoogleLoaded || !google?.maps) {
      console.log("Google Maps non ancora caricato");
      return null;
    }

    // Get the icon SVG content for the business category
    const getBusinessIconSvg = (iconName: string | null) => {
      if (!iconName) {
        // Default store icon fallback
        return `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7"/>
          <path d="M4 7v6a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7"/>
          <path d="M8 13v4"/>
          <path d="M16 13v4"/>
        </svg>`;
      }

      // Icon mapping for business categories based on your database
      const iconMappings: { [key: string]: string } = {
        // Supermercato Alimentari - Shopping Cart
        'openmoji:shopping-cart': `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
          <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
        </svg>`,

        // Palestre - Person Lifting Weights
        'openmoji:person-lifting-weights': `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
          <path d="M20.57 14.86L22 13.43 20.57 12 17 15.57 8.43 7 12 3.43 10.57 2 9.14 3.43 7.71 2 5.57 4.14 4.14 2.71 2.71 4.14l1.43 1.43L2 7.71l1.43 1.43L4.86 7.71l8.58 8.58-1.43 1.43L13.43 19l1.43 1.43L16.29 19l1.43 1.43 1.43-1.43-1.43-1.43L19.14 16l1.43-1.14zM12.8 12.8c-.78.78-2.05.78-2.83 0-.78-.78-.78-2.05 0-2.83.78-.78 2.05-.78 2.83 0 .78.78.78 2.04 0 2.83z"/>
        </svg>`,

        // Ristorante - Fork and Knife
        'openmoji:fork-and-knife': `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
          <path d="M8.1 13.34l2.83-2.83L3.91 3.5c-1.56 1.56-1.56 4.09 0 5.66l4.19 4.18zm6.78-1.81c1.53.71 3.68.21 5.27-1.38 1.91-1.91 2.28-4.65.81-6.12-1.46-1.46-4.20-1.10-6.12.81-1.59 1.59-2.09 3.74-1.38 5.27L3.7 19.87l1.41 1.41L12 14.41l6.88 6.88 1.41-1.41L13.41 13l1.47-1.47z"/>
        </svg>`,

        // Bar - Beer Mug
        'openmoji:beer-mug': `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
          <path d="M5 6V4h5.5l1.21-1.21C12.45 2.04 13.18 2 14 2s1.55.04 2.29.79L17.5 4H20v2h-1v11c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2V6H5zm2 11h8V6H7v11zm2-9h1v7H9V8zm3 0h1v7h-1V8z"/>
        </svg>`,

        // Hotel
        'openmoji:hotel': `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
          <path d="M7 13c1.66 0 3-1.34 3-3S8.66 7 7 7s-3 1.34-3 3 1.34 3 3 3zm12-6h-8v7H9.5v-7h-8v12H3v-2h18v2h1.5V7z"/>
        </svg>`,

        // Parrucchieri - Scissors
        'openmoji:scissors': `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
          <path d="M9.64 7.64c.23-.5.36-1.05.36-1.64 0-2.21-1.79-4-4-4S2 3.79 2 6s1.79 4 4 4c.59 0 1.14-.13 1.64-.36L10 12l-2.36 2.36C7.14 14.13 6.59 14 6 14c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4c0-.59-.13-1.14-.36-1.64L12 14l7 7h3v-1L9.64 7.64zM6 8c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm0 12c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm6-7.5c-.28 0-.5-.22-.5-.5s.22-.5.5-.5.5.22.5.5-.22.5-.5.5zM19 3l-6 6 2 2 7-7V3z"/>
        </svg>`,

        // Spa - Person in Lotus Position
        'openmoji:person-in-lotus-position': `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
          <path d="M8.55 12C9.66 7.7 12.14 5.7 12.14 5.7S14.62 7.7 15.73 12C16.84 16.3 12.14 18.3 12.14 18.3S7.44 16.3 8.55 12ZM12.14 2C12.14 2 6.34 5.56 5.78 12.95C5.18 21.03 11.88 22 12.14 22S19.1 21.03 18.5 12.95C17.94 5.56 12.14 2 12.14 2Z"/>
        </svg>`,

        // Fioraio - Bouquet
        'openmoji:bouquet': `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
          <path d="M12 22c4.97 0 9-4.03 9-9-4.97 0-9 4.03-9 9zM5.6 10.25c0 1.38 1.12 2.5 2.5 2.5.53 0 1.01-.16 1.42-.44l-.02.19c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5l-.02-.19c.4.28.89.44 1.42.44 1.38 0 2.5-1.12 2.5-2.5 0-1.25-.93-2.29-2.14-2.46.21-.34.34-.74.34-1.18 0-1.25-.93-2.29-2.14-2.46C14.48 3.94 13.75 3.5 12.9 3.5c-.85 0-1.58.44-2.02.85C9.67 4.21 8.74 5.25 8.74 6.5c0 .44.13.84.34 1.18C7.87 7.85 6.94 8.89 6.94 10.14c0 .44.13.84.34 1.18C6.07 11.49 5.6 10.92 5.6 10.25z"/>
        </svg>`,

        // Estetica - Nail Polish
        'openmoji:nail-polish': `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
          <path d="M8.6 10.08c1.78 0 3.22-1.44 3.22-3.22S10.38 3.64 8.6 3.64 5.38 5.08 5.38 6.86s1.44 3.22 3.22 3.22zm6.8 0c1.78 0 3.22-1.44 3.22-3.22S17.18 3.64 15.4 3.64s-3.22 1.44-3.22 3.22 1.44 3.22 3.22 3.22zM8.6 12.5c-2.33 0-7 1.17-7 3.5V18c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-2c0-2.33-4.67-3.5-7-3.5zm6.8 0c-.29 0-.62.02-.97.05.02.01.03.03.04.04 1.14.83 1.93 1.94 1.93 3.41V18c0 .35-.07.69-.18 1H22c.55 0 1-.45 1-1v-2c0-2.33-4.67-3.5-7-3.5z"/>
        </svg>`
      };

      // Check if we have a mapped icon
      if (iconMappings[iconName]) {
        return iconMappings[iconName];
      }

      // Default fallback for unmapped icons
      return `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
        <circle cx="12" cy="12" r="10"/>
        <path d="M12 6v6l4 2"/>
      </svg>`;
    };

    return {
      url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
        <svg xmlns="http://www.w3.org/2000/svg" width="140" height="80" viewBox="0 0 140 80">
          <defs>
            <!-- Drop shadow filter -->
            <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
              <feDropShadow dx="0" dy="1" stdDeviation="2" flood-opacity="0.2" />
            </filter>
            <!-- Subtle inner glow for the info box -->
            <filter id="innerGlow">
              <feFlood flood-color="#A0AEC0" flood-opacity="0.05" result="flood" />
              <feComposite in="flood" in2="SourceGraphic" operator="in" result="mask" />
              <feGaussianBlur in="mask" stdDeviation="1" result="blur" />
              <feComposite in="SourceGraphic" in2="blur" operator="over" />
            </filter>
          </defs>
          
          <!-- Main group with shadow -->
          <g filter="url(#shadow)">
            <!-- Info box with rounded corners -->
            <rect x="20" y="5" width="100" height="40" rx="8" ry="8" fill="white" stroke="#A0AEC0" stroke-width="1.5" filter="url(#innerGlow)" />
            
            <!-- Triangle pointer with smooth connection to the box and more rounded corners -->
            <path d="M70,65 C68,65 56,47 55,45 C60,45 80,45 85,45 C84,47 72,65 70,65 Z" fill="${business.fake ? 'red' : 'white'}" fill-opacity="${business.fake ? 0.10 : 0.9}" stroke="#A0AEC0" stroke-width="1.5" stroke-linejoin="round" />
          </g>
          
          <!-- Business icon -->
          <g transform="translate(28, 12)">
            ${getBusinessIconSvg(business.icon)}
          </g>
          
          <!-- Business name and deal count with improved typography -->
          <foreignObject x="48" y="8" width="68" height="34">
            <div xmlns="http://www.w3.org/1999/xhtml" style="
              font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              text-align: left;
              width: 100%;
              height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: center;
              padding-left: 4px;
            ">
              <div style="
                font-weight: 600; 
                color: #1F2937; 
                font-size: 11px; 
                white-space: nowrap; 
                overflow: hidden; 
                text-overflow: ellipsis;
                letter-spacing: -0.01em;
                line-height: 1.2;
              ">
                ${business.name}
              </div>
              ${
                business.deal_count_published &&
                business.deal_count_published > 0
                  ? `
              <div style="
                color: #f44336; 
                font-size: 10px; 
                margin-top: 2px;
                font-weight: 500;
                display: flex;
                align-items: center;
                line-height: 1.2;
              ">
                <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 2px;">
                  <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                  <line x1="7" y1="7" x2="7.01" y2="7"></line>
                </svg>
                ${business.deal_count_published} ${
                      business.deal_count_published > 1 ? "Offerte" : "Offerta"
                    }
              </div>
              `
                  : ""
              }
            </div>
          </foreignObject>
        </svg>
      `)}`,
      anchor: new google.maps.Point(70, 65),
      scaledSize: new google.maps.Size(140, 80),
    };
  };

  const createUserMarkerIcon = () => {
    if (!isGoogleLoaded || !google?.maps) {
      console.log("Google Maps non ancora caricato per il marker utente");
      return null;
    }
    return {
      path: google.maps.SymbolPath.CIRCLE,
      fillColor: "#4F46E5",
      fillOpacity: 1,
      strokeColor: "#FFFFFF",
      strokeWeight: 2,
      scale: 8,
    };
  };

  const handleMarkerClick = async (business: Business) => {
    if (selectedBusiness?.id !== business.id) {
      setNavigationState({
        isActive: false,
        mode: "DRIVING",
        directions: null,
      });
      setShowTransportMenu(false);
    }

    if (!business.deals || business.deals.length === 0) {
      const { data, error } = await supabase
        .from("deals")
        .select("*")
        .eq("business_id", business.id)
        .eq("status", "published");

      if (error) {
        console.error("Errore nel recupero delle offerte:", error);
        toast.error("Errore nel recupero delle offerte");
      } else {
        business.deals = data || [];
      }
    }

    setSelectedBusiness({ ...business });
    setIsSlideVisible(true);
    if (onBusinessClick) {
      onBusinessClick(business);
    }
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartY.current = e.touches[0].clientY;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (touchStartY.current === null || !slideRef.current) return;

    const touchY = e.touches[0].clientY;
    const diff = touchY - touchStartY.current;

    if (diff > 0) {
      slideRef.current.style.transform = `translateY(${diff}px)`;
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (touchStartY.current === null || !slideRef.current) return;

    const touchY = e.changedTouches[0].clientY;
    const diff = touchY - touchStartY.current;

    slideRef.current.style.transform = "";

    if (diff > 100) {
      setIsSlideVisible(false);
      setTimeout(() => setSelectedBusiness(null), 300);
    }

    touchStartY.current = null;
  };

  const mapOptions = {
    zoomControl: false,
    mapTypeControl: false,
    scaleControl: false,
    streetViewControl: false,
    rotateControl: false,
    fullscreenControl: false,
    cameraControl: false,
    gestureHandling: "greedy",
    styles: [
      {
        featureType: "poi",
        elementType: "labels",
        stylers: [{ visibility: "off" }],
      },
    ],
  };

  const handleBoundsChanged = useCallback(
    debounce(() => {
      if (mapRef.current && isGoogleLoaded) {
        setIsLoadingMarkers(true);
        const bounds = mapRef.current.getBounds();
        if (bounds) {
          setCurrentBounds(bounds);
        }
        setTimeout(() => {
          setIsLoadingMarkers(false);
        }, 500);
      }
    }, 300),
    [isGoogleLoaded]
  );

  const visibleBusinesses = useMemo(() => {
    if (!currentBounds || !isGoogleLoaded) return [];

    return businesses.filter((business) => {
      if (!business.latitude || !business.longitude) return false;

      return currentBounds.contains(
        new google.maps.LatLng(business.latitude, business.longitude)
      );
    });
  }, [businesses, currentBounds, isGoogleLoaded]);

  const calculateRoute = useCallback(
    async (destination: Business, mode: TransportMode) => {
      if (
        !userLocation ||
        !destination.latitude ||
        !destination.longitude ||
        !isGoogleLoaded
      ) {
        toast.error(
          "Impossibile calcolare il percorso. Assicurati di aver attivato la geolocalizzazione."
        );
        return;
      }

      const directionsService = new google.maps.DirectionsService();

      try {
        const result = await directionsService.route({
          origin: userLocation,
          destination: {
            lat: destination.latitude,
            lng: destination.longitude,
          },
          travelMode: google.maps.TravelMode[mode],
          language: "it",
        });
        console.log("route", result);
        setNavigationState({
          isActive: true,
          mode,
          directions: result,
        });

        if (mapRef.current && result.routes[0].bounds) {
          mapRef.current.fitBounds(result.routes[0].bounds);
        }
      } catch (error) {
        console.error("Error calculating route:", error);
        toast.error("Errore nel calcolo del percorso");
      }
    },
    [userLocation, isGoogleLoaded]
  );

  const clearNavigation = () => {
    setNavigationState({
      isActive: false,
      mode: "DRIVING",
      directions: null,
    });
    setShowTransportMenu(false);
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours} h ${minutes} min`;
    }
    return `${minutes} min`;
  };

  const formatDistance = (meters: number): string => {
    if (meters >= 1000) {
      return `${(meters / 1000).toFixed(1)} km`;
    }
    return `${meters} m`;
  };

  const getTransportIcon = (mode: TransportMode) => {
    switch (mode) {
      case "DRIVING":
        return <Car className="h-4 w-4" />;
      case "WALKING":
        return <PersonStanding className="h-4 w-4" />;
      case "TRANSIT":
        return <Bus className="h-4 w-4" />;
      case "BICYCLING":
        return <Bike className="h-4 w-4" />;
      default:
        return null;
    }
  };

  const centerMapToUserLocation = useCallback(() => {
    if (userLocation && mapRef.current) {
      mapRef.current.panTo(userLocation);
      mapRef.current.setZoom(DEFAULT_ZOOM);
      toast.success("Mappa centrata sulla tua posizione");
    } else if (!userLocation) {
      toast.error("Posizione utente non disponibile");
    }
  }, [userLocation]);

  if (!apiKey) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center h-[calc(100vh-120px)]">
          <div className="animate-spin">
            <MapPin className="h-8 w-8 text-brand-primary" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      <LoadScript
        googleMapsApiKey={apiKey}
        onLoad={() => setIsGoogleLoaded(true)}
        preventGoogleFontsLoading={true}
        loadingElement={<div>Loading...</div>}
        id="google-maps-script"
      >
        <GoogleMap
          mapContainerClassName="w-full h-full"
          zoom={DEFAULT_ZOOM}
          center={center}
          options={mapOptions}
          onLoad={(map) => {
            mapRef.current = map;
            if (map.getBounds()) {
              setCurrentBounds(map.getBounds());
            }
          }}
          onBoundsChanged={handleBoundsChanged}
        >
          {!navigationState.isActive && (
            <MarkerClusterer
              options={{
                imagePath:
                  "https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m",
                gridSize: 50,
                minimumClusterSize: 3,
                maxZoom: 15,
              }}
            >
              {(clusterer) => (
                <>
                  {visibleBusinesses.map(
                    (business) =>
                      business.latitude &&
                      business.longitude && (
                        <Marker
                          key={business.id}
                          position={{
                            lat: business.latitude,
                            lng: business.longitude,
                          }}
                          onClick={() => handleMarkerClick(business)}
                          icon={createBusinessMarkerIcon(business)}
                          zIndex={1}
                          clusterer={clusterer}
                        />
                      )
                  )}
                </>
              )}
            </MarkerClusterer>
          )}

          {userLocation && isGoogleLoaded && (
            <>
              <Marker
                position={userLocation}
                icon={createUserMarkerIcon()}
                title="La tua posizione"
                zIndex={2500}
              />

              <Circle
                center={userLocation}
                radius={userCircleRadius}
                options={{
                  strokeColor: "#4F46E5",
                  strokeWeight: 2,
                  strokeOpacity: 0.5,
                  fillColor: "#4F46E5",
                  fillOpacity: 0.1,
                }}
                onLoad={(circle) => setUserCircle(circle as google.maps.Circle)}
                onUnmount={(circle) => setUserCircle(null)}
                onCenterChanged={() =>
                  userCircle &&
                  setUserCircleCenter({
                    lat: userCircle["center"].lat(),
                    lng: userCircle["center"].lng(),
                  })
                }
                onRadiusChanged={() =>
                  userCircle &&
                  setUserCircleRadius(
                    parseFloat(userCircle.getRadius().toString())
                  )
                }
              />
            </>
          )}

          {navigationState.directions && (
            <DirectionsRenderer
              directions={navigationState.directions}
              options={{
                suppressMarkers: false,
                polylineOptions: {
                  strokeColor: "#4F46E5",
                  strokeWeight: 6,
                  strokeOpacity: 0.8,
                },
                markerOptions: {
                  zIndex: 2000,
                },
                preserveViewport: false,
              }}
            />
          )}
        </GoogleMap>
      </LoadScript>

      {userLocation && (
        <div className="fixed left-8 bottom-24 z-10 flex flex-col gap-2">
          <button
            onClick={centerMapToUserLocation}
            className="bg-white shadow-lg rounded-full p-3 hover:bg-gray-50 transition-colors border border-brand-primary flex items-center justify-center"
            title="Centra sulla tua posizione"
            aria-label="Centra sulla tua posizione"
          >
            <User2 className="h-5 w-5 text-brand-primary" />
          </button>

          {/* Radius control buttons */}
          <div className="bg-white shadow-lg rounded-full flex flex-col border border-brand-primary overflow-hidden">
            <button
              onClick={increaseRadius}
              className="p-2 hover:bg-gray-50 transition-colors border-b border-gray-200 focus:outline-none flex items-center justify-center"
              title="Aumenta raggio"
              aria-label="Aumenta raggio"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-brand-primary"
              >
                <path d="M12 5v14M5 12h14"></path>
              </svg>
            </button>

            {/* Radius indicator */}
            <div className="relative group">
              <div className="flex items-center justify-center p-1.5 border-b border-gray-200 bg-gray-50">
                <span className="text-xs font-medium text-brand-primary">
                  {(userCircleRadius / 1000).toFixed(1)} km
                </span>
              </div>
              {/* Tooltip */}
              <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-2 py-1 rounded text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
                Raggio di ricerca
              </div>
            </div>

            <button
              onClick={decreaseRadius}
              className="p-2 hover:bg-gray-50 transition-colors focus:outline-none flex items-center justify-center"
              title="Diminuisci raggio"
              aria-label="Diminuisci raggio"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-brand-primary"
              >
                <path d="M5 12h14"></path>
              </svg>
            </button>
          </div>
        </div>
      )}

      {isLoadingMarkers && (
        <div className="fixed top-16 left-1/2 transform -translate-x-1/2 bg-white px-4 py-2 rounded-full shadow-md z-20 flex items-center">
          <span className="text-sm font-medium mr-2">Caricamento</span>
          <div className="flex space-x-1">
            <div
              className="w-2 h-2 bg-brand-primary rounded-full animate-bounce"
              style={{ animationDelay: "0ms" }}
            ></div>
            <div
              className="w-2 h-2 bg-brand-primary rounded-full animate-bounce"
              style={{ animationDelay: "150ms" }}
            ></div>
            <div
              className="w-2 h-2 bg-brand-primary rounded-full animate-bounce"
              style={{ animationDelay: "300ms" }}
            ></div>
          </div>
        </div>
      )}

      <div className="fixed left-4 top-24 z-50">
        <div className="relative">
          {selectedBusiness ? (
            <button
              onClick={() => {
                setShowTransportMenu(!showTransportMenu);
              }}
              className={cn(
                "fixed left-4 top-20 z-10 bg-brand-primary shadow-lg rounded-full px-5 py-2.5",
                navigationState.directions
                  ? "hover:bg-brand-primary/90 flex items-center gap-2"
                  : "hover:bg-brand-primary/50"
              )}
              title={
                navigationState.directions
                  ? "Cambia mezzo di trasporto"
                  : "Naviga verso l'attività"
              }
            >
              <Navigation2 className="h-6 w-6 text-white" />
              {navigationState.directions && (
                <span className="text-white text-sm font-medium flex items-center gap-2">
                  {getTransportIcon(navigationState.mode)}
                  {formatDuration(
                    navigationState.directions.routes[0].legs[0].duration.value
                  )}
                </span>
              )}
            </button>
          ) : null}

          {showTransportMenu && selectedBusiness && (
            <div className="absolute top-full mb-2 left-0 bg-white rounded-lg shadow-lg p-2 min-w-[200px] z-50">
              <div className="flex flex-col gap-2">
                <div className="px-4 py-2 text-sm font-medium text-gray-600 border-b border-gray-100">
                  {navigationState.directions
                    ? "Opzioni percorso"
                    : "Scegli il mezzo"}
                </div>
                {navigationState.directions ? (
                  <>
                    <button
                      onClick={() => {
                        setNavigationState((prev) => ({
                          ...prev,
                          isActive: true,
                        }));
                        setShowTransportMenu(false);
                      }}
                      className="flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md"
                    >
                      <ChevronRight className="h-4 w-4" /> Mostra indicazioni
                    </button>
                    <div className="border-t border-gray-100 my-1"></div>
                  </>
                ) : null}
                <button
                  onClick={() => {
                    calculateRoute(selectedBusiness, "DRIVING");
                    setShowTransportMenu(false);
                  }}
                  className={cn(
                    "flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md",
                    navigationState.mode === "DRIVING" &&
                      "text-brand-primary font-medium"
                  )}
                >
                  <Car className="h-4 w-4" /> In auto
                </button>
                <button
                  onClick={() => {
                    calculateRoute(selectedBusiness, "WALKING");
                    setShowTransportMenu(false);
                  }}
                  className={cn(
                    "flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md",
                    navigationState.mode === "WALKING" &&
                      "text-brand-primary font-medium"
                  )}
                >
                  <PersonStanding className="h-4 w-4" /> A piedi
                </button>
                <button
                  onClick={() => {
                    calculateRoute(selectedBusiness, "TRANSIT");
                    setShowTransportMenu(false);
                  }}
                  className={cn(
                    "flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md",
                    navigationState.mode === "TRANSIT" &&
                      "text-brand-primary font-medium"
                  )}
                >
                  <Bus className="h-4 w-4" /> Mezzi pubblici
                </button>
                <button
                  onClick={() => {
                    calculateRoute(selectedBusiness, "BICYCLING");
                    setShowTransportMenu(false);
                  }}
                  className={cn(
                    "flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md",
                    navigationState.mode === "BICYCLING" &&
                      "text-brand-primary font-medium"
                  )}
                >
                  <Bike className="h-4 w-4" /> In bicicletta
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {navigationState.isActive && navigationState.directions && (
        <div className="fixed right-4 top-24 bottom-24 w-80 bg-white rounded-lg shadow-lg z-50 flex flex-col overflow-hidden">
          <div className="p-4 bg-brand-primary text-white">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold">Indicazioni stradali</h3>
              <button
                onClick={() => {
                  setNavigationState((prev) => ({
                    ...prev,
                    isActive: false,
                  }));
                }}
                className="p-1 hover:bg-white/10 rounded-full transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <div className="flex items-center gap-1.5">
                {getTransportIcon(navigationState.mode)}
                <Clock className="h-4 w-4" />
              </div>
              <span>
                {formatDuration(
                  navigationState.directions.routes[0].legs[0].duration.value
                )}
              </span>
              <span className="mx-1">·</span>
              <span>
                {formatDistance(
                  navigationState.directions.routes[0].legs[0].distance.value
                )}
              </span>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            <div className="p-4 space-y-4">
              {navigationState.directions.routes[0].legs[0].steps.map(
                (step, index) => (
                  <div key={index} className="flex gap-3">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center">
                      {index === 0 ? (
                        <div className="w-2 h-2 rounded-full bg-brand-primary" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div
                        className="text-sm text-gray-700"
                        dangerouslySetInnerHTML={{ __html: step.instructions }}
                      />
                      <div className="text-xs text-gray-500 mt-1">
                        {formatDistance(step.distance.value)} ·{" "}
                        {formatDuration(step.duration.value)}
                      </div>
                    </div>
                  </div>
                )
              )}
            </div>
          </div>
        </div>
      )}

      {selectedBusiness && (
        <div
          ref={slideRef}
          className={cn(
            "fixed left-0 right-0 z-10 transition-all duration-300 ease-in-out",
            isSlideVisible ? "bottom-16 opacity-100" : "-bottom-full opacity-0"
          )}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <div className="relative bg-white rounded-t-xl shadow-lg p-4 mx-4 max-h-[70vh] overflow-y-auto">
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gray-300 rounded-full mb-4"></div>

            <button
              onClick={() => {
                setIsSlideVisible(false);
                clearNavigation();
                setTimeout(() => setSelectedBusiness(null), 300);
              }}
              className="absolute right-2 top-2 p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
            >
              <X className="h-4 w-4 text-gray-600" />
            </button>

            <div className="flex items-center gap-2 mb-4 mt-4">
              <Store className="h-5 w-5 text-brand-primary" />
              <h3 className="font-semibold text-lg">{selectedBusiness.name}</h3>
            </div>

            {selectedBusiness.deals && selectedBusiness.deals.length > 0 ? (
              <Carousel className="w-full">
                <CarouselContent>
                  {selectedBusiness.deals.map((deal) => (
                    <CarouselItem key={deal.id}>
                      <DealCard
                        deal={deal}
                        variant="compact"
                        onClick={() => onDealClick?.(deal.id)}
                        showVisitBusiness={true}
                      />
                    </CarouselItem>
                  ))}
                </CarouselContent>
              </Carousel>
            ) : (
              <p className="text-gray-500 text-center py-4">
                Nessuna offerta disponibile al momento
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
